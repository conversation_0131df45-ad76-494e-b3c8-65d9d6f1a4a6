
# Development Guide

Guide for developers working on Eigen CRM Schmiede.

## Table of Contents
- [Project Structure](#project-structure)
- [Development Workflow](#development-workflow)
- [Code Standards](#code-standards)
- [Testing Strategy](#testing-strategy)
- [Component Development](#component-development)
- [State Management](#state-management)
- [AI Integration](#ai-integration)

## Project Structure

```
eigen-crm-schmiede/
├── src/
│   ├── components/          # React components
│   │   ├── ui/             # Base UI components (shadcn/ui)
│   │   ├── dashboard/      # Dashboard-specific components
│   │   ├── ai/             # AI-related components
│   │   └── [modules]/      # Business module components
│   ├── contexts/           # React contexts
│   ├── hooks/              # Custom React hooks
│   ├── services/           # Business logic and API calls
│   │   ├── agent/          # AI agent management
│   │   ├── nlprocessor/    # Natural language processing
│   │   ├── tools/          # Tool integrations
│   │   └── [modules]/      # Module-specific services
│   ├── stores/             # Zustand state stores
│   ├── lib/                # Utility libraries
│   ├── types/              # TypeScript type definitions
│   └── data/               # Mock data and constants
├── docs/                   # Documentation
├── database/               # Database schema and migrations
└── public/                 # Static assets
```

### Key Directories

#### `/src/components/`
- **UI Components**: Base components from shadcn/ui
- **Feature Components**: Business logic components
- **Module Components**: Organized by business domain

#### `/src/services/`
- **Core Services**: Database, authentication, logging
- **AI Services**: Agent management, NLP, model routing
- **Business Services**: Domain-specific logic

#### `/src/stores/`
- **Zustand Stores**: Global state management
- **Store Organization**: One store per domain/feature

## Development Workflow

### 1. Setting Up Development Environment

```bash
# Clone repository
git clone <repository-url>
cd eigen-crm-schmiede

# Install dependencies
npm install

# Copy environment file
cp .env.example .env.local

# Start development server
npm run dev
```

### 2. Feature Development Process

#### Create Feature Branch
```bash
git checkout -b feature/new-feature-name
```

#### Development Cycle
1. **Plan**: Define requirements and architecture
2. **Develop**: Write code following standards
3. **Test**: Write and run tests
4. **Review**: Self-review and peer review
5. **Deploy**: Merge and deploy

#### Commit Convention
```bash
# Format: type(scope): description
git commit -m "feat(dashboard): add AI health monitoring"
git commit -m "fix(auth): resolve login redirect issue"
git commit -m "docs(api): update authentication examples"
```

**Types**: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`

### 3. Branch Strategy

- **main**: Production-ready code
- **develop**: Integration branch for features
- **feature/***: New features
- **hotfix/***: Critical bug fixes
- **release/***: Release preparation

## Code Standards

### TypeScript Guidelines

#### Type Definitions
```typescript
// Use interfaces for object shapes
interface Person {
  id: string
  name: string
  email: string
  company?: Company
}

// Use types for unions and primitives
type Status = 'active' | 'inactive' | 'pending'
type EventHandler = (event: Event) => void

// Use generics for reusable components
interface ApiResponse<T> {
  data: T
  error?: string
  metadata: {
    page: number
    limit: number
    total: number
  }
}
```

#### Component Props
```typescript
// Define props interface
interface DashboardProps {
  title: string
  showMetrics?: boolean
  onRefresh?: () => void
  children?: React.ReactNode
}

// Use in component
const Dashboard: React.FC<DashboardProps> = ({
  title,
  showMetrics = true,
  onRefresh,
  children
}) => {
  // Component implementation
}
```

### React Best Practices

#### Component Structure
```typescript
// 1. Imports
import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { useToast } from '@/hooks/use-toast'

// 2. Types/Interfaces
interface ComponentProps {
  // props definition
}

// 3. Component
export const Component: React.FC<ComponentProps> = ({ prop1, prop2 }) => {
  // 4. Hooks
  const [state, setState] = useState()
  const { toast } = useToast()

  // 5. Effects
  useEffect(() => {
    // side effects
  }, [])

  // 6. Event handlers
  const handleClick = () => {
    // handler logic
  }

  // 7. Render
  return (
    <div>
      {/* JSX */}
    </div>
  )
}
```

#### Performance Optimization
```typescript
// Use React.memo for expensive components
const ExpensiveComponent = React.memo<Props>(({ data }) => {
  return <div>{/* expensive rendering */}</div>
})

// Use useCallback for event handlers
const handleSubmit = useCallback((data: FormData) => {
  // submit logic
}, [dependency])

// Use useMemo for expensive calculations
const processedData = useMemo(() => {
  return data.map(item => processItem(item))
}, [data])
```

### CSS/Styling Guidelines

#### Tailwind CSS Usage
```typescript
// Use semantic class names
<div className="bg-background text-foreground border border-border rounded-lg p-4">
  
// Responsive design
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">

// State variants
<button className="bg-primary hover:bg-primary/80 disabled:opacity-50">

// Use CSS variables for theming
<div className="bg-card text-card-foreground">
```

#### Component Styling
```typescript
// Use cn utility for conditional classes
import { cn } from '@/lib/utils'

const Button = ({ variant, className, ...props }) => {
  return (
    <button
      className={cn(
        'base-button-classes',
        variant === 'primary' && 'primary-variant-classes',
        variant === 'secondary' && 'secondary-variant-classes',
        className
      )}
      {...props}
    />
  )
}
```

## Testing Strategy

### Unit Testing
```typescript
// Component testing with React Testing Library
import { render, screen, fireEvent } from '@testing-library/react'
import { Button } from './Button'

describe('Button', () => {
  it('renders with correct text', () => {
    render(<Button>Click me</Button>)
    expect(screen.getByText('Click me')).toBeInTheDocument()
  })

  it('calls onClick handler', () => {
    const handleClick = jest.fn()
    render(<Button onClick={handleClick}>Click me</Button>)
    
    fireEvent.click(screen.getByText('Click me'))
    expect(handleClick).toHaveBeenCalledTimes(1)
  })
})
```

### Integration Testing
```typescript
// Service testing
import { DatabaseService } from './DatabaseService'

describe('DatabaseService', () => {
  beforeEach(() => {
    // Setup test database
  })

  it('creates person successfully', async () => {
    const person = await DatabaseService.createPerson({
      name: 'Test User',
      email: '<EMAIL>'
    })
    
    expect(person.id).toBeDefined()
    expect(person.name).toBe('Test User')
  })
})
```

### AI Testing
```typescript
// AI service testing
import { nlProcessor } from './NLProcessor'

describe('NLProcessor', () => {
  it('classifies intent correctly', async () => {
    const result = await nlProcessor.processMessage('show me leads')
    
    expect(result.intent).toBe('search')
    expect(result.entities.target).toBe('leads')
    expect(result.confidence).toBeGreaterThan(0.8)
  })
})
```

## Component Development

### Creating New Components

#### 1. Base Component Structure
```typescript
// src/components/example/ExampleComponent.tsx
import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

interface ExampleComponentProps {
  title: string
  data: any[]
  onAction?: () => void
}

export const ExampleComponent: React.FC<ExampleComponentProps> = ({
  title,
  data,
  onAction
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent>
        {/* Component content */}
      </CardContent>
    </Card>
  )
}
```

#### 2. Component Documentation
```typescript
/**
 * ExampleComponent displays data in a card format
 * 
 * @param title - The card title
 * @param data - Array of data items to display
 * @param onAction - Optional callback for user actions
 * 
 * @example
 * <ExampleComponent
 *   title="Recent Activity"
 *   data={activities}
 *   onAction={() => console.log('Action clicked')}
 * />
 */
```

#### 3. Export from Index
```typescript
// src/components/example/index.ts
export { ExampleComponent } from './ExampleComponent'
export type { ExampleComponentProps } from './ExampleComponent'
```

### Component Patterns

#### Container/Presentation Pattern
```typescript
// Container component (logic)
const PeopleContainer: React.FC = () => {
  const { people, isLoading, createPerson } = usePeople()
  
  return (
    <PeopleList
      people={people}
      isLoading={isLoading}
      onCreatePerson={createPerson}
    />
  )
}

// Presentation component (UI)
interface PeopleListProps {
  people: Person[]
  isLoading: boolean
  onCreatePerson: (person: Partial<Person>) => void
}

const PeopleList: React.FC<PeopleListProps> = ({
  people,
  isLoading,
  onCreatePerson
}) => {
  // Pure UI rendering
}
```

#### Compound Component Pattern
```typescript
// Parent component
const Table = ({ children }: { children: React.ReactNode }) => {
  return <table className="w-full">{children}</table>
}

// Sub-components
Table.Header = ({ children }: { children: React.ReactNode }) => {
  return <thead>{children}</thead>
}

Table.Body = ({ children }: { children: React.ReactNode }) => {
  return <tbody>{children}</tbody>
}

Table.Row = ({ children }: { children: React.ReactNode }) => {
  return <tr>{children}</tr>
}

Table.Cell = ({ children }: { children: React.ReactNode }) => {
  return <td className="p-2">{children}</td>
}

// Usage
<Table>
  <Table.Header>
    <Table.Row>
      <Table.Cell>Name</Table.Cell>
      <Table.Cell>Email</Table.Cell>
    </Table.Row>
  </Table.Header>
  <Table.Body>
    {/* rows */}
  </Table.Body>
</Table>
```

## State Management

### Zustand Store Patterns

#### Store Definition
```typescript
// src/stores/peopleStore.ts
import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'

interface PeopleState {
  people: Person[]
  selectedPerson: Person | null
  isLoading: boolean
  error: string | null
}

interface PeopleActions {
  setPeople: (people: Person[]) => void
  selectPerson: (person: Person | null) => void
  addPerson: (person: Person) => void
  updatePerson: (id: string, updates: Partial<Person>) => void
  removePerson: (id: string) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
}

export const usePeopleStore = create<PeopleState & PeopleActions>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    people: [],
    selectedPerson: null,
    isLoading: false,
    error: null,

    // Actions
    setPeople: (people) => set({ people }),
    selectPerson: (selectedPerson) => set({ selectedPerson }),
    addPerson: (person) => set((state) => ({
      people: [...state.people, person]
    })),
    updatePerson: (id, updates) => set((state) => ({
      people: state.people.map(p => 
        p.id === id ? { ...p, ...updates } : p
      )
    })),
    removePerson: (id) => set((state) => ({
      people: state.people.filter(p => p.id !== id)
    })),
    setLoading: (isLoading) => set({ isLoading }),
    setError: (error) => set({ error })
  }))
)
```

#### Store Usage in Components
```typescript
// Select specific state
const people = usePeopleStore(state => state.people)
const addPerson = usePeopleStore(state => state.addPerson)

// Select multiple values
const { people, isLoading, error } = usePeopleStore(state => ({
  people: state.people,
  isLoading: state.isLoading,
  error: state.error
}))

// Subscribe to changes
useEffect(() => {
  const unsubscribe = usePeopleStore.subscribe(
    (state) => state.selectedPerson,
    (selectedPerson) => {
      if (selectedPerson) {
        console.log('Person selected:', selectedPerson.name)
      }
    }
  )
  
  return unsubscribe
}, [])
```

### React Query Integration
```typescript
// Custom hook combining Zustand and React Query
export const usePeople = () => {
  const { setPeople, setLoading, setError } = usePeopleStore()
  
  const query = useQuery({
    queryKey: ['people'],
    queryFn: async () => {
      setLoading(true)
      try {
        const people = await DatabaseService.getPeople()
        setPeople(people)
        return people
      } catch (error) {
        setError(error.message)
        throw error
      } finally {
        setLoading(false)
      }
    }
  })
  
  return {
    ...query,
    people: usePeopleStore(state => state.people)
  }
}
```

## AI Integration

### Service Layer Architecture
```typescript
// Base AI service interface
interface AIService {
  initialize(): Promise<void>
  isReady(): boolean
  processMessage(message: string, context?: any): Promise<AIResponse>
  cleanup(): void
}

// Implementation for different providers
class OllamaService implements AIService {
  private baseUrl: string
  private model: string
  
  constructor(config: OllamaConfig) {
    this.baseUrl = config.baseUrl
    this.model = config.model
  }
  
  async initialize(): Promise<void> {
    // Initialize connection
  }
  
  async processMessage(message: string, context?: any): Promise<AIResponse> {
    // Process with Ollama
  }
}
```

### Context Management
```typescript
// Enhanced AI context provider
export const EnhancedAIProvider: React.FC<{ children: React.ReactNode }> = ({
  children
}) => {
  const [context, setContext] = useState<AIContext>({
    currentView: 'dashboard',
    userPreferences: {},
    conversationHistory: []
  })
  
  const sendMessage = useCallback(async (message: string, options?: MessageOptions) => {
    const response = await aiService.processMessage(message, {
      ...context,
      ...options
    })
    
    setContext(prev => ({
      ...prev,
      conversationHistory: [...prev.conversationHistory, {
        message,
        response: response.text,
        timestamp: new Date()
      }]
    }))
    
    return response
  }, [context])
  
  return (
    <EnhancedAIContext.Provider value={{ context, sendMessage, setContext }}>
      {children}
    </EnhancedAIContext.Provider>
  )
}
```

### Agent Development
```typescript
// Base agent class
abstract class BaseAgent {
  protected id: string
  protected name: string
  protected capabilities: string[]
  protected status: 'idle' | 'running' | 'error'
  
  constructor(config: AgentConfig) {
    this.id = config.id
    this.name = config.name
    this.capabilities = config.capabilities
    this.status = 'idle'
  }
  
  abstract async execute(task: Task): Promise<TaskResult>
  abstract async initialize(): Promise<void>
  abstract getCapabilities(): string[]
  
  async start(): Promise<void> {
    this.status = 'running'
    await this.initialize()
  }
  
  async stop(): Promise<void> {
    this.status = 'idle'
  }
}

// Specific agent implementation
class LeadResearchAgent extends BaseAgent {
  async execute(task: Task): Promise<TaskResult> {
    // Implement lead research logic
    const leads = await this.researchLeads(task.parameters)
    return {
      success: true,
      data: leads,
      message: `Found ${leads.length} potential leads`
    }
  }
  
  private async researchLeads(parameters: any): Promise<Lead[]> {
    // Lead research implementation
  }
}
```

This development guide provides the foundation for contributing to the project. Follow these patterns and standards to maintain code quality and consistency.
