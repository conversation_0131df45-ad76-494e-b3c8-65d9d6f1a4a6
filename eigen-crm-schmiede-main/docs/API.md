
# API Documentation

Comprehensive API reference for Eigen CRM Schmiede backend services.

## Table of Contents
- [Authentication](#authentication)
- [Core Entities](#core-entities)
- [AI Services](#ai-services)
- [Real-time Features](#real-time-features)
- [Error Handling](#error-handling)
- [Rate Limiting](#rate-limiting)

## Authentication

All API requests require authentication via Supabase Auth.

### Authentication Methods

#### 1. Session-based (Recommended)
```javascript
// Login
const { data, error } = await supabase.auth.signInWithPassword({
  email: '<EMAIL>',
  password: 'password'
})

// All subsequent requests automatically include auth header
```

#### 2. API Key
```javascript
// Include in request headers
headers: {
  'Authorization': `Bearer ${apiKey}`,
  'apikey': anon_key
}
```

### User Management

#### Get Current User
```javascript
const { data: user } = await supabase.auth.getUser()
```

#### Update User Profile
```javascript
const { data, error } = await supabase.auth.updateUser({
  data: { 
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON>'
  }
})
```

## Core Entities

### People

#### List People
```javascript
const { data, error } = await supabase
  .from('people')
  .select(`
    id,
    name,
    email,
    phone,
    title,
    company:companies(name),
    created_at
  `)
  .order('name')
  .limit(50)
```

#### Create Person
```javascript
const { data, error } = await supabase
  .from('people')
  .insert({
    name: 'John Doe',
    email: '<EMAIL>',
    phone: '+1234567890',
    title: 'CEO',
    company_id: 'uuid-here'
  })
  .select()
```

#### Update Person
```javascript
const { data, error } = await supabase
  .from('people')
  .update({
    title: 'CTO',
    phone: '+1987654321'
  })
  .eq('id', personId)
  .select()
```

#### Delete Person
```javascript
const { error } = await supabase
  .from('people')
  .delete()
  .eq('id', personId)
```

### Companies

#### List Companies
```javascript
const { data, error } = await supabase
  .from('companies')
  .select(`
    id,
    name,
    domain,
    industry,
    size,
    status,
    people:people(count),
    opportunities:opportunities(count)
  `)
  .order('name')
```

#### Company Analytics
```javascript
const { data, error } = await supabase
  .rpc('get_company_analytics', { 
    company_id: 'uuid-here',
    date_range: '30d'
  })
```

### Opportunities

#### List Opportunities
```javascript
const { data, error } = await supabase
  .from('opportunities')
  .select(`
    id,
    title,
    value,
    stage,
    probability,
    expected_close_date,
    company:companies(name),
    owner:profiles(name)
  `)
  .eq('stage', 'negotiation')
  .order('expected_close_date')
```

#### Update Opportunity Stage
```javascript
const { data, error } = await supabase
  .from('opportunities')
  .update({
    stage: 'closed_won',
    closed_date: new Date().toISOString(),
    probability: 100
  })
  .eq('id', opportunityId)
  .select()
```

### Tasks

#### List Tasks
```javascript
const { data, error } = await supabase
  .from('tasks')
  .select(`
    id,
    title,
    description,
    status,
    priority,
    due_date,
    assignee:profiles(name),
    related_to,
    created_at
  `)
  .eq('status', 'pending')
  .order('due_date')
```

#### Create Task
```javascript
const { data, error } = await supabase
  .from('tasks')
  .insert({
    title: 'Follow up with lead',
    description: 'Send proposal and pricing',
    priority: 'high',
    due_date: '2025-05-30T10:00:00Z',
    assignee_id: 'uuid-here',
    related_to: 'opportunity',
    related_id: 'opportunity-uuid'
  })
  .select()
```

## AI Services

### Natural Language Processing

#### Process Message
```javascript
// POST /api/ai/nlp/process
const response = await fetch('/api/ai/nlp/process', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    message: "show me leads from tech companies",
    context: {
      user_id: "uuid-here",
      current_view: "dashboard"
    }
  })
})

const result = await response.json()
// Returns: { intent, entities, confidence, action }
```

#### Intent Classification
```javascript
const { intent, confidence } = await nlProcessor.classifyIntent(
  "schedule a meeting with John next Tuesday"
)
// Returns: { intent: "schedule_meeting", confidence: 0.95 }
```

### AI Agent Management

#### List Agents
```javascript
const { data, error } = await supabase
  .from('ai_agents')
  .select(`
    id,
    name,
    type,
    status,
    capabilities,
    configuration,
    performance_metrics,
    last_active
  `)
  .eq('status', 'active')
```

#### Start Agent
```javascript
// POST /api/ai/agents/{agentId}/start
const response = await fetch(`/api/ai/agents/${agentId}/start`, {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    configuration: {
      model: "llama3.1:8b",
      temperature: 0.7,
      max_tokens: 2048
    }
  })
})
```

#### Agent Communication
```javascript
// POST /api/ai/agents/{agentId}/message
const response = await fetch(`/api/ai/agents/${agentId}/message`, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    message: "Analyze the leads from this week",
    context: {
      timeframe: "7d",
      filters: { industry: "technology" }
    }
  })
})
```

### Enhanced AI Context

#### Send Message to AI
```javascript
const { sendMessage } = useEnhancedAI()

const response = await sendMessage("Find opportunities in Q1", {
  intent: "search",
  entities: { timeframe: "Q1" },
  context: { current_view: "opportunities" }
})
```

#### Get AI Suggestions
```javascript
// GET /api/ai/suggestions?context=leads&limit=5
const suggestions = await fetch('/api/ai/suggestions?context=leads&limit=5', {
  headers: { 'Authorization': `Bearer ${token}` }
}).then(r => r.json())
```

## Real-time Features

### WebSocket Connection

#### Connect to Real-time Updates
```javascript
const channel = supabase
  .channel('dashboard-updates')
  .on('postgres_changes', {
    event: 'INSERT',
    schema: 'public',
    table: 'opportunities'
  }, (payload) => {
    console.log('New opportunity:', payload.new)
  })
  .on('postgres_changes', {
    event: 'UPDATE',
    schema: 'public',
    table: 'tasks'
  }, (payload) => {
    console.log('Task updated:', payload.new)
  })
  .subscribe()
```

#### Agent Status Updates
```javascript
const agentChannel = supabase
  .channel('agent-status')
  .on('broadcast', {
    event: 'agent_status_change'
  }, (payload) => {
    console.log('Agent status:', payload)
  })
  .subscribe()
```

### Notifications

#### Subscribe to Notifications
```javascript
const notificationChannel = supabase
  .channel('notifications')
  .on('postgres_changes', {
    event: 'INSERT',
    schema: 'public',
    table: 'notifications',
    filter: `user_id=eq.${userId}`
  }, (payload) => {
    showNotification(payload.new)
  })
  .subscribe()
```

## Analytics & Reporting

### Revenue Analytics

#### Get Revenue Metrics
```javascript
// GET /api/analytics/revenue?period=30d
const metrics = await fetch('/api/analytics/revenue?period=30d', {
  headers: { 'Authorization': `Bearer ${token}` }
}).then(r => r.json())

// Returns:
// {
//   total_revenue: 125000,
//   growth_rate: 0.15,
//   pipeline_value: 450000,
//   conversion_rate: 0.23,
//   avg_deal_size: 15000
// }
```

#### Deal Velocity Analysis
```javascript
const { data, error } = await supabase
  .rpc('calculate_deal_velocity', {
    date_from: '2025-01-01',
    date_to: '2025-05-26',
    stage_filter: ['negotiation', 'proposal']
  })
```

### Performance Metrics

#### Agent Performance
```javascript
// GET /api/analytics/agents/{agentId}/performance
const performance = await fetch(`/api/analytics/agents/${agentId}/performance`, {
  headers: { 'Authorization': `Bearer ${token}` }
}).then(r => r.json())
```

#### System Health Metrics
```javascript
// GET /api/health/metrics
const health = await fetch('/api/health/metrics', {
  headers: { 'Authorization': `Bearer ${token}` }
}).then(r => r.json())

// Returns:
// {
//   database: { status: "healthy", response_time: 45 },
//   ai_services: { status: "healthy", active_agents: 3 },
//   memory_usage: 0.65,
//   cpu_usage: 0.34
// }
```

## Error Handling

### Standard Error Response
```javascript
{
  error: {
    code: "VALIDATION_ERROR",
    message: "Invalid email format",
    details: {
      field: "email",
      value: "invalid-email"
    },
    timestamp: "2025-05-26T10:00:00Z",
    request_id: "req_abc123"
  }
}
```

### Common Error Codes

| Code | Description | Resolution |
|------|-------------|------------|
| `AUTH_REQUIRED` | Authentication required | Provide valid auth token |
| `FORBIDDEN` | Insufficient permissions | Check user roles/permissions |
| `NOT_FOUND` | Resource not found | Verify resource ID |
| `VALIDATION_ERROR` | Invalid input data | Check request format |
| `RATE_LIMITED` | Too many requests | Implement backoff strategy |
| `AI_SERVICE_ERROR` | AI service unavailable | Check AI service status |
| `DATABASE_ERROR` | Database operation failed | Check connection/retry |

### Error Handling Examples

#### Handle API Errors
```javascript
const handleApiCall = async () => {
  try {
    const { data, error } = await supabase
      .from('people')
      .insert(newPerson)
    
    if (error) {
      switch (error.code) {
        case 'PGRST116':
          throw new Error('Person not found')
        case '23505':
          throw new Error('Email already exists')
        default:
          throw new Error('Database error occurred')
      }
    }
    
    return data
  } catch (error) {
    console.error('API Error:', error)
    toast.error(error.message)
    throw error
  }
}
```

## Rate Limiting

### Default Limits
- **API Calls**: 1000 requests per hour per user
- **AI Requests**: 100 requests per hour per user
- **Real-time Connections**: 10 concurrent per user
- **File Uploads**: 50MB per request, 500MB per hour

### Rate Limit Headers
```javascript
// Response headers
{
  'X-RateLimit-Limit': '1000',
  'X-RateLimit-Remaining': '995', 
  'X-RateLimit-Reset': '1640995200',
  'X-RateLimit-Retry-After': '3600'
}
```

### Handle Rate Limiting
```javascript
const apiCallWithRetry = async (apiCall, maxRetries = 3) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await apiCall()
    } catch (error) {
      if (error.status === 429) {
        const retryAfter = error.headers['X-RateLimit-Retry-After']
        await new Promise(resolve => 
          setTimeout(resolve, retryAfter * 1000)
        )
        continue
      }
      throw error
    }
  }
  throw new Error('Max retries exceeded')
}
```

## SDK Examples

### JavaScript SDK Usage
```javascript
import { createClient } from '@supabase/supabase-js'
import { EnhancedAIProvider } from './contexts/EnhancedAIContext'

// Initialize client
const supabase = createClient(url, key)

// Use with React
function App() {
  return (
    <EnhancedAIProvider>
      <CRMDashboard />
    </EnhancedAIProvider>
  )
}
```

### Custom Hooks
```javascript
// Custom hook for people management
export const usePeople = () => {
  const queryClient = useQueryClient()
  
  const { data: people, isLoading, error } = useQuery({
    queryKey: ['people'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('people')
        .select('*')
        .order('name')
      
      if (error) throw error
      return data
    }
  })
  
  const createPerson = useMutation({
    mutationFn: async (newPerson) => {
      const { data, error } = await supabase
        .from('people')
        .insert(newPerson)
        .select()
      
      if (error) throw error
      return data[0]
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['people'] })
    }
  })
  
  return { people, isLoading, error, createPerson }
}
```

This API documentation provides comprehensive coverage of the backend services. For implementation details, refer to the source code in the `src/services/` directory.
