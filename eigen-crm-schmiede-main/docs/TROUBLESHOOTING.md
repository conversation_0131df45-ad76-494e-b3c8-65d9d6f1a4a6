
# Troubleshooting Guide

Common issues and solutions for Eigen CRM Schmiede.

## Table of Contents
- [Installation Issues](#installation-issues)
- [Database Problems](#database-problems)
- [AI Integration Issues](#ai-integration-issues)
- [Performance Problems](#performance-problems)
- [Deployment Issues](#deployment-issues)
- [Debug Tools](#debug-tools)

## Installation Issues

### Node.js Version Conflicts
**Problem**: Build fails with Node.js version errors
```bash
Error: unsupported engine "node" 18.0.0
```

**Solution**:
```bash
# Check current version
node --version

# Install correct version with nvm
nvm install 18
nvm use 18

# Or update with package manager
# macOS: brew install node@18
# Ubuntu: curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
```

### Dependency Installation Failures
**Problem**: npm install fails with permission or network errors

**Solution**:
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Use different registry if needed
npm config set registry https://registry.npmjs.org/

# Check network/proxy settings
npm config get proxy
npm config get https-proxy
```

### TypeScript Compilation Errors
**Problem**: Build fails with TypeScript errors

**Solution**:
```bash
# Check TypeScript version
npx tsc --version

# Clean and rebuild
rm -rf dist
npm run build

# Check for type conflicts
npx tsc --noEmit

# Update type definitions
npm update @types/*
```

## Database Problems

### Supabase Connection Issues
**Problem**: Cannot connect to Supabase database

**Symptoms**:
- "Network Error" in browser console
- Dashboard shows "Database: Warning"
- Authentication failures

**Diagnosis**:
```bash
# Test connection
curl -H "apikey: YOUR_ANON_KEY" \
     "YOUR_SUPABASE_URL/rest/v1/"

# Check environment variables
echo $VITE_SUPABASE_URL
echo $VITE_SUPABASE_ANON_KEY
```

**Solutions**:
1. **Invalid URL/Key**:
   ```bash
   # Verify in Supabase dashboard
   # Settings > API > Project URL and anon key
   ```

2. **Network/Firewall**:
   ```bash
   # Test connectivity
   ping your-project.supabase.co
   nslookup your-project.supabase.co
   ```

3. **Local Supabase Issues**:
   ```bash
   # Restart local instance
   supabase stop
   supabase start
   
   # Check Docker containers
   docker ps
   docker logs supabase_db_*
   ```

### Database Schema Issues
**Problem**: Missing tables or incorrect schema

**Solution**:
```bash
# Reset local database
supabase db reset

# Apply migrations manually
supabase db push

# Check current schema
supabase db dump --schema-only
```

### Authentication Problems
**Problem**: User authentication fails

**Solutions**:
1. **Check Auth Settings**:
   - Supabase Dashboard > Authentication > Settings
   - Verify site URL matches your domain
   - Check enabled providers

2. **Debug Auth Flow**:
   ```javascript
   // Add to browser console
   console.log(supabase.auth.getSession())
   ```

## AI Integration Issues

### Ollama Connection Problems
**Problem**: AI features not working with local Ollama

**Symptoms**:
- AI Integration Monitor shows "Error"
- "Failed to connect to Ollama" messages
- Voice commands not processing

**Diagnosis**:
```bash
# Check if Ollama is running
curl http://localhost:11434/api/version

# Check available models
ollama list

# Test model directly
ollama run llama3.1:8b "Hello, world!"
```

**Solutions**:
1. **Start Ollama Service**:
   ```bash
   # macOS/Linux
   ollama serve
   
   # Windows
   # Run Ollama desktop app
   ```

2. **Install Required Models**:
   ```bash
   ollama pull llama3.1:8b
   ollama pull nomic-embed-text
   ```

3. **Fix Network Issues**:
   ```bash
   # Check firewall
   sudo ufw allow 11434
   
   # Check port binding
   netstat -tlnp | grep 11434
   ```

### Cloud AI Provider Issues
**Problem**: OpenAI/Anthropic API calls failing

**Solutions**:
1. **API Key Issues**:
   ```bash
   # Test API key
   curl -H "Authorization: Bearer $VITE_OPENAI_API_KEY" \
        "https://api.openai.com/v1/models"
   ```

2. **Rate Limiting**:
   - Check usage limits in provider dashboard
   - Implement request throttling
   - Consider upgrading plan

3. **Network/Proxy Issues**:
   ```bash
   # Test connectivity
   curl https://api.openai.com/v1/models
   ```

### Voice Recognition Problems
**Problem**: Voice control not working

**Solutions**:
1. **Browser Support**:
   - Use Chrome or Edge (best support)
   - Enable microphone permissions
   - Use HTTPS (required for voice)

2. **Microphone Issues**:
   ```javascript
   // Test in browser console
   navigator.mediaDevices.getUserMedia({ audio: true })
     .then(() => console.log('Microphone access granted'))
     .catch(console.error)
   ```

## Performance Problems

### Slow Application Loading
**Problem**: Application takes too long to load

**Diagnosis**:
```bash
# Check bundle size
npm run build
ls -la dist/assets/

# Analyze bundle
npm install --save-dev webpack-bundle-analyzer
npx webpack-bundle-analyzer dist/assets/*.js
```

**Solutions**:
1. **Enable Code Splitting**:
   ```javascript
   // Lazy load components
   const Dashboard = lazy(() => import('./Dashboard'))
   ```

2. **Optimize Images**:
   ```bash
   # Compress images
   npm install --save-dev imagemin
   ```

3. **Enable Caching**:
   ```nginx
   # nginx.conf
   location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
       expires 1y;
       add_header Cache-Control "public, immutable";
   }
   ```

### Memory Leaks
**Problem**: Application consumes increasing memory

**Diagnosis**:
```javascript
// Monitor memory in browser DevTools
// Performance tab > Memory > Take heap snapshot
```

**Solutions**:
1. **Clean Up Subscriptions**:
   ```javascript
   useEffect(() => {
     const subscription = service.subscribe()
     return () => subscription.unsubscribe()
   }, [])
   ```

2. **Optimize Re-renders**:
   ```javascript
   // Use React.memo for expensive components
   const ExpensiveComponent = React.memo(({ data }) => {
     // Component logic
   })
   ```

### Database Query Performance
**Problem**: Slow database queries

**Solutions**:
1. **Add Indexes**:
   ```sql
   CREATE INDEX idx_people_company_id ON people(company_id);
   CREATE INDEX idx_opportunities_stage ON opportunities(stage);
   ```

2. **Optimize Queries**:
   ```javascript
   // Use select to limit columns
   const { data } = await supabase
     .from('people')
     .select('id, name, email')
     .limit(50)
   ```

## Deployment Issues

### Build Failures in Production
**Problem**: Production build fails but development works

**Solutions**:
1. **Environment Variables**:
   ```bash
   # Check all required vars are set
   echo $VITE_SUPABASE_URL
   echo $VITE_SUPABASE_ANON_KEY
   ```

2. **Type Checking**:
   ```bash
   # Fix TypeScript errors
   npx tsc --noEmit
   ```

3. **Dependency Issues**:
   ```bash
   # Use exact versions
   npm ci --production
   ```

### SSL Certificate Problems
**Problem**: HTTPS not working properly

**Solutions**:
1. **Let's Encrypt Issues**:
   ```bash
   # Renew certificate
   sudo certbot renew
   
   # Check certificate status
   sudo certbot certificates
   ```

2. **Custom Certificate Issues**:
   ```bash
   # Verify certificate
   openssl x509 -in certificate.crt -text -noout
   
   # Check private key
   openssl rsa -in private.key -check
   ```

### Docker Deployment Issues
**Problem**: Docker containers not starting

**Solutions**:
1. **Check Logs**:
   ```bash
   docker logs container_name
   docker-compose logs -f
   ```

2. **Resource Issues**:
   ```bash
   # Check system resources
   docker system df
   docker stats
   ```

3. **Port Conflicts**:
   ```bash
   # Check port usage
   netstat -tlnp | grep :8080
   ```

## Debug Tools

### Browser DevTools
1. **Console**: Check for JavaScript errors
2. **Network**: Monitor API requests and responses
3. **Application**: Inspect localStorage and cookies
4. **Performance**: Profile rendering and memory usage

### Application Debug Mode
```bash
# Enable debug mode
VITE_ENABLE_DEBUG=true npm run dev
```

### Health Check Endpoints
```bash
# Application health
curl http://localhost:8080/health

# Database health
curl http://localhost:8080/api/health/db

# AI service health
curl http://localhost:11434/api/version
```

### Log Analysis
```bash
# Application logs
tail -f logs/app.log

# Browser console logs
# F12 > Console > Check for errors

# System logs
journalctl -f -u nginx
```

### Debug Scripts
```javascript
// Add to browser console for debugging
window.debugApp = {
  store: window.__ZUSTAND_STORE__,
  supabase: window.__SUPABASE_CLIENT__,
  ai: window.__AI_CONTEXT__
}
```

## Getting Help

If you're still experiencing issues:

1. **Check Browser Console**: Look for error messages
2. **Review Logs**: Check application and system logs
3. **Test Components**: Use the AI Integration Test Panel
4. **Document Issue**: Include error messages, environment details, and steps to reproduce
5. **Seek Support**: Create an issue with detailed information

Remember to **never share** sensitive information like API keys or passwords when seeking help.
