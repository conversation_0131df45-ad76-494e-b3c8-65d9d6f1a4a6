#!/bin/bash

# SPQR Server Deployment Script
# Target Server: vince@**************

set -e

echo "🚀 SPQR Server Deployment Starting..."

# Configuration
SERVER_USER="vince"
SERVER_HOST="**************"
SERVER_PATH="/home/<USER>/spqr-production"
DOCKER_COMPOSE_FILE="docker-compose.yml"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we can connect to the server
check_server_connection() {
    print_status "Checking server connection..."
    if ssh -o ConnectTimeout=10 ${SERVER_USER}@${SERVER_HOST} "echo 'Connection successful'" > /dev/null 2>&1; then
        print_success "Server connection established"
    else
        print_error "Cannot connect to server ${SERVER_HOST}"
        exit 1
    fi
}

# Check prerequisites on server
check_server_prerequisites() {
    print_status "Checking server prerequisites..."
    
    ssh ${SERVER_USER}@${SERVER_HOST} << 'EOF'
        # Check Docker
        if ! command -v docker &> /dev/null; then
            echo "ERROR: Docker is not installed"
            exit 1
        fi
        
        # Check Docker Compose
        if ! command -v docker-compose &> /dev/null; then
            echo "ERROR: Docker Compose is not installed"
            exit 1
        fi
        
        # Check Node.js
        if ! command -v node &> /dev/null; then
            echo "WARNING: Node.js is not installed (needed for local development)"
        fi
        
        echo "Prerequisites check completed"
EOF
    
    if [ $? -eq 0 ]; then
        print_success "Server prerequisites verified"
    else
        print_error "Server prerequisites check failed"
        exit 1
    fi
}

# Create deployment directory
create_deployment_directory() {
    print_status "Creating deployment directory..."
    ssh ${SERVER_USER}@${SERVER_HOST} "mkdir -p ${SERVER_PATH}"
    print_success "Deployment directory created: ${SERVER_PATH}"
}

# Copy files to server
copy_files_to_server() {
    print_status "Copying application files to server..."
    
    # Create temporary archive
    tar -czf spqr-deployment.tar.gz \
        --exclude=node_modules \
        --exclude=dist \
        --exclude=.git \
        --exclude=*.log \
        --exclude=.env.local \
        .
    
    # Copy to server
    scp spqr-deployment.tar.gz ${SERVER_USER}@${SERVER_HOST}:${SERVER_PATH}/
    
    # Extract on server
    ssh ${SERVER_USER}@${SERVER_HOST} << EOF
        cd ${SERVER_PATH}
        tar -xzf spqr-deployment.tar.gz
        rm spqr-deployment.tar.gz
        
        # Copy production environment
        cp .env.production .env
        
        # Set proper permissions
        chmod +x deploy-server.sh
        chmod +x scripts/*.js
EOF
    
    # Clean up local archive
    rm spqr-deployment.tar.gz
    
    print_success "Files copied to server"
}

# Build application on server
build_application() {
    print_status "Building application on server..."
    
    ssh ${SERVER_USER}@${SERVER_HOST} << EOF
        cd ${SERVER_PATH}
        
        # Install dependencies
        npm ci --production
        
        # Build application
        npm run build
        
        echo "Application built successfully"
EOF
    
    print_success "Application built on server"
}

# Deploy with Docker
deploy_with_docker() {
    print_status "Deploying with Docker Compose..."
    
    ssh ${SERVER_USER}@${SERVER_HOST} << EOF
        cd ${SERVER_PATH}
        
        # Stop existing containers
        docker-compose down || true
        
        # Pull latest images
        docker-compose pull
        
        # Start services
        docker-compose up -d
        
        # Wait for services to start
        sleep 30
        
        # Check service status
        docker-compose ps
EOF
    
    print_success "Docker deployment completed"
}

# Verify deployment
verify_deployment() {
    print_status "Verifying deployment..."
    
    # Wait a bit for services to fully start
    sleep 10
    
    # Check if application is responding
    if curl -f http://${SERVER_HOST}:8080/health > /dev/null 2>&1; then
        print_success "Application is responding on port 8080"
    else
        print_warning "Application health check failed - may still be starting"
    fi
    
    # Check Docker services
    ssh ${SERVER_USER}@${SERVER_HOST} << EOF
        cd ${SERVER_PATH}
        echo "Docker service status:"
        docker-compose ps
        
        echo "Application logs (last 20 lines):"
        docker-compose logs --tail=20 crm-app
EOF
}

# Main deployment process
main() {
    echo "🎯 Starting SPQR deployment to ${SERVER_HOST}"
    echo "================================================"
    
    check_server_connection
    check_server_prerequisites
    create_deployment_directory
    copy_files_to_server
    build_application
    deploy_with_docker
    verify_deployment
    
    echo "================================================"
    print_success "🎉 SPQR deployment completed!"
    echo ""
    echo "📋 Next Steps:"
    echo "1. Configure your Supabase credentials in .env"
    echo "2. Set up SSL certificates if needed"
    echo "3. Configure domain/DNS if using custom domain"
    echo "4. Test all functionality"
    echo ""
    echo "🌐 Application URL: http://${SERVER_HOST}:8080"
    echo "📊 Monitoring: http://${SERVER_HOST}:3001 (Grafana)"
    echo "🔍 Logs: ssh ${SERVER_USER}@${SERVER_HOST} 'cd ${SERVER_PATH} && docker-compose logs -f'"
}

# Run main function
main "$@"
