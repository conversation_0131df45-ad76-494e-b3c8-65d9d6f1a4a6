#!/bin/bash

# SPQR Deployment Testing Script
# Tests all critical functionality before developer handoff

set -e

# Configuration
SERVER_HOST="**************"
APP_PORT="8080"
BASE_URL="http://${SERVER_HOST}:${APP_PORT}"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_test() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_pass() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_fail() {
    echo -e "${RED}[FAIL]${NC} $1"
}

print_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# Test counters
TESTS_RUN=0
TESTS_PASSED=0
TESTS_FAILED=0

run_test() {
    local test_name="$1"
    local test_command="$2"

    TESTS_RUN=$((TESTS_RUN + 1))
    print_test "$test_name"

    if eval "$test_command" > /dev/null 2>&1; then
        print_pass "$test_name"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        print_fail "$test_name"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# Test server connectivity
test_server_connectivity() {
    print_test "Server SSH connectivity"
    if ssh -o ConnectTimeout=10 vince@${SERVER_HOST} "echo 'SSH OK'" > /dev/null 2>&1; then
        print_pass "SSH connection successful"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        print_fail "Cannot connect via SSH"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TESTS_RUN=$((TESTS_RUN + 1))
}

# Test application health
test_application_health() {
    run_test "Application health endpoint" "curl -f ${BASE_URL}/health"
}

# Test main application
test_main_application() {
    run_test "Main application loads" "curl -f ${BASE_URL}/"
}

# Test API endpoints
test_api_endpoints() {
    run_test "API proxy endpoint" "curl -f ${BASE_URL}/api/"
    run_test "Auth endpoint" "curl -f ${BASE_URL}/auth/"
}

# Test AI services
test_ai_services() {
    print_test "Ollama AI service"
    if ssh vince@${SERVER_HOST} "curl -f http://localhost:11434/api/version" > /dev/null 2>&1; then
        print_pass "Ollama service responding"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        print_fail "Ollama service not responding"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TESTS_RUN=$((TESTS_RUN + 1))
}

# Test AI provider endpoints
test_ai_providers() {
    print_test "AI provider configuration endpoint"
    if curl -f ${BASE_URL}/api/ai/providers > /dev/null 2>&1; then
        print_pass "AI providers endpoint responding"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        print_fail "AI providers endpoint not responding"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TESTS_RUN=$((TESTS_RUN + 1))
}

# Test lead scoring functionality
test_lead_scoring() {
    print_test "Lead scoring service"
    if curl -f ${BASE_URL}/api/leads/scoring/test > /dev/null 2>&1; then
        print_pass "Lead scoring service responding"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        print_fail "Lead scoring service not responding"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TESTS_RUN=$((TESTS_RUN + 1))
}

# Test enhanced features
test_enhanced_features() {
    print_test "Enhanced AI features"

    # Test multi-provider routing
    if curl -f ${BASE_URL}/api/ai/routing/test > /dev/null 2>&1; then
        print_pass "AI routing system functional"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        print_fail "AI routing system not responding"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TESTS_RUN=$((TESTS_RUN + 1))

    # Test customer health monitoring
    print_test "Customer health monitoring"
    if curl -f ${BASE_URL}/api/customers/health > /dev/null 2>&1; then
        print_pass "Customer health monitoring active"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        print_fail "Customer health monitoring not responding"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TESTS_RUN=$((TESTS_RUN + 1))
}

# Test Docker services
test_docker_services() {
    print_test "Docker services status"

    local services_output
    services_output=$(ssh vince@${SERVER_HOST} "cd /home/<USER>/spqr-production && docker-compose ps --format table" 2>/dev/null || echo "")

    if [[ -n "$services_output" ]]; then
        print_pass "Docker services are running"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        echo "$services_output"
    else
        print_fail "Docker services not running or not accessible"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TESTS_RUN=$((TESTS_RUN + 1))
}

# Generate test report
generate_report() {
    echo ""
    echo "🧪 SPQR Deployment Test Report"
    echo "=============================="
    echo "Server: ${SERVER_HOST}"
    echo "Application URL: ${BASE_URL}"
    echo "Test Date: $(date)"
    echo ""
    echo "📊 Test Results:"
    echo "Tests Run: ${TESTS_RUN}"
    echo "Tests Passed: ${TESTS_PASSED}"
    echo "Tests Failed: ${TESTS_FAILED}"
    echo "Success Rate: $(( TESTS_PASSED * 100 / TESTS_RUN ))%"
    echo ""

    if [[ $TESTS_FAILED -eq 0 ]]; then
        echo "🎉 All tests passed! System is ready for developer acceptance testing."
    elif [[ $TESTS_FAILED -le 2 ]]; then
        echo "⚠️  Minor issues detected. System is mostly functional."
    else
        echo "❌ Multiple issues detected. System needs attention."
    fi

    echo ""
    echo "📋 Next Steps for Developer:"
    echo "1. Review test results above"
    echo "2. Access application at: ${BASE_URL}"
    echo "3. Test CRM functionality"
    echo "4. Verify AI integration"
    echo "5. Check user authentication"
    echo "6. Test automation features"
}

# Main test execution
main() {
    echo "🧪 Starting SPQR Deployment Tests"
    echo "=================================="
    echo "Target: ${BASE_URL}"
    echo "Time: $(date)"
    echo ""

    # Core connectivity tests
    test_server_connectivity
    test_application_health
    test_main_application

    # Service tests
    test_docker_services
    test_ai_services
    test_ai_providers
    test_lead_scoring
    test_enhanced_features
    test_api_endpoints

    # Generate final report
    generate_report
}

# Run tests
main "$@"
