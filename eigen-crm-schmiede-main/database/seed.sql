
-- ConstructionOS Seed Data
-- This file contains demo data for testing and development

-- Insert demo companies
INSERT INTO public.companies (id, name, address, phone, email, website, industry, size_category, annual_revenue) VALUES
('550e8400-e29b-41d4-a716-446655440001', 'Metro Construction Ltd', '123 Main St, Downtown City', '******-0101', '<EMAIL>', 'https://metroconstruction.com', 'Construction', 'medium', 5000000.00),
('550e8400-e29b-41d4-a716-446655440002', 'GreenBuild Solutions', '456 Oak Ave, Green Valley', '******-0102', '<EMAIL>', 'https://greenbuildsolutions.com', 'Sustainable Construction', 'small', 2500000.00),
('550e8400-e29b-41d4-a716-446655440003', 'Urban Development Corp', '789 Steel Blvd, Industrial Zone', '******-0103', '<EMAIL>', 'https://urbandevelopment.com', 'Real Estate Development', 'large', 15000000.00);

-- Insert demo projects
INSERT INTO public.projects (id, name, description, status, priority, start_date, end_date, budget, actual_cost, progress_percentage, company_id) VALUES
('660e8400-e29b-41d4-a716-446655440001', 'Downtown Office Complex', 'Modern 20-story office building in the heart of downtown', 'active', 'high', '2024-01-15', '2025-06-30', 8500000.00, 3200000.00, 35, '550e8400-e29b-41d4-a716-446655440001'),
('660e8400-e29b-41d4-a716-446655440002', 'Highway Bridge Project', 'Major bridge construction over the central highway', 'active', 'critical', '2024-03-01', '2025-12-15', 12000000.00, 4800000.00, 45, '550e8400-e29b-41d4-a716-446655440001'),
('660e8400-e29b-41d4-a716-446655440003', 'Residential Tower', '30-unit luxury residential building', 'planning', 'medium', '2024-07-01', '2025-11-30', 6200000.00, 0.00, 5, '550e8400-e29b-41d4-a716-446655440002'),
('660e8400-e29b-41d4-a716-446655440004', 'Green Energy Facility', 'Solar panel installation and energy storage facility', 'active', 'high', '2024-02-15', '2024-10-30', 3800000.00, 1900000.00, 65, '550e8400-e29b-41d4-a716-446655440002');

-- Insert demo AI agents
INSERT INTO public.ai_agents (id, name, type, status, last_activity) VALUES
('770e8400-e29b-41d4-a716-446655440001', 'Market Research Agent', 'research', 'running', NOW() - INTERVAL '5 minutes'),
('770e8400-e29b-41d4-a716-446655440002', 'Safety Monitor', 'monitoring', 'running', NOW() - INTERVAL '2 minutes'),
('770e8400-e29b-41d4-a716-446655440003', 'Budget Analyzer', 'analysis', 'idle', NOW() - INTERVAL '1 hour'),
('770e8400-e29b-41d4-a716-446655440004', 'Communication Bot', 'communication', 'running', NOW() - INTERVAL '30 seconds');

-- Insert demo conversations
INSERT INTO public.conversations (id, title, participants, last_message_at, status) VALUES
('880e8400-e29b-41d4-a716-446655440001', 'Project Team Chat', '{}', NOW() - INTERVAL '10 minutes', 'active'),
('880e8400-e29b-41d4-a716-446655440002', 'Safety Protocol Discussion', '{}', NOW() - INTERVAL '1 hour', 'active');

-- Insert demo approval requests
INSERT INTO public.approval_requests (id, title, description, type, status, priority, amount, project_id, due_date) VALUES
('990e8400-e29b-41d4-a716-446655440001', 'Additional Steel Beams', 'Request for additional steel reinforcement for building foundation', 'purchase', 'pending', 'high', 125000.00, '660e8400-e29b-41d4-a716-446655440001', NOW() + INTERVAL '3 days'),
('990e8400-e29b-41d4-a716-446655440002', 'Safety Equipment Upgrade', 'New safety harnesses and helmets for construction crew', 'safety', 'pending', 'urgent', 15000.00, '660e8400-e29b-41d4-a716-446655440002', NOW() + INTERVAL '1 day'),
('990e8400-e29b-41d4-a716-446655440003', 'Design Change Request', 'Modify lobby design to include sustainable materials', 'design', 'approved', 'medium', 85000.00, '660e8400-e29b-41d4-a716-446655440001', NOW() - INTERVAL '2 days');

-- Insert demo activity feed entries
INSERT INTO public.activity_feed (type, title, description, status, value, project_id) VALUES
('agent', 'AI Agent Completed Research', 'Market analysis for Downtown Office Complex project finished', 'success', '$2.4M opportunity identified', '660e8400-e29b-41d4-a716-446655440001'),
('revenue', 'Deal Progress Updated', 'Residential Tower project moved to negotiation stage', 'success', '+$890K pipeline', '660e8400-e29b-41d4-a716-446655440003'),
('automation', 'Workflow Automation Triggered', 'Lead qualification process initiated for 5 new prospects', 'info', '5 leads processed', NULL),
('system', 'Safety Inspection Scheduled', 'Automated safety check scheduled for Highway Bridge Project', 'info', NULL, '660e8400-e29b-41d4-a716-446655440002'),
('agent', 'Competitive Intelligence Update', 'New competitor analysis available for Metro Construction Ltd', 'warning', 'Risk level: Medium', NULL),
('user', 'New Project Created', 'Green Energy Facility project added to portfolio', 'success', '$1.2M estimated value', '660e8400-e29b-41d4-a716-446655440004');

-- Insert demo tasks
INSERT INTO public.tasks (title, description, status, priority, due_date, estimated_hours, project_id) VALUES
('Foundation Inspection', 'Complete foundation safety and quality inspection', 'in_progress', 'high', NOW() + INTERVAL '2 days', 8.0, '660e8400-e29b-41d4-a716-446655440001'),
('Material Delivery Coordination', 'Coordinate delivery of steel beams and concrete', 'todo', 'medium', NOW() + INTERVAL '5 days', 4.0, '660e8400-e29b-41d4-a716-446655440002'),
('Environmental Impact Assessment', 'Complete environmental review for green energy facility', 'review', 'high', NOW() + INTERVAL '7 days', 16.0, '660e8400-e29b-41d4-a716-446655440004'),
('Budget Review Meeting', 'Quarterly budget review with stakeholders', 'todo', 'medium', NOW() + INTERVAL '10 days', 2.0, '660e8400-e29b-41d4-a716-446655440003');

-- Insert demo safety reports
INSERT INTO public.safety_reports (title, description, severity, status, location, project_id) VALUES
('Minor Equipment Malfunction', 'Crane hydraulic system showing signs of wear', 'medium', 'investigating', 'Construction Site A - Crane Bay', '660e8400-e29b-41d4-a716-446655440001'),
('Safety Protocol Violation', 'Worker spotted without proper safety harness', 'high', 'resolved', 'Highway Bridge - Section B', '660e8400-e29b-41d4-a716-446655440002'),
('Material Storage Issue', 'Improper storage of hazardous materials', 'medium', 'open', 'Green Energy Site - Storage Area', '660e8400-e29b-41d4-a716-446655440004');
