
-- Extended schema for remaining components
-- Communication module tables
CREATE TABLE public.channels (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    type TEXT DEFAULT 'channel' CHECK (type IN ('channel', 'direct')),
    is_private BOOLEAN DEFAULT false,
    created_by UUID REFERENCES public.profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE public.messages (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    channel_id UUID REFERENCES public.channels(id),
    sender_id UUID REFERENCES public.profiles(id),
    content TEXT NOT NULL,
    type TEXT DEFAULT 'text' CHECK (type IN ('text', 'file', 'system')),
    file_url TEXT,
    reactions JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE public.announcements (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    author_id UUID REFERENCES public.profiles(id),
    priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
    is_pinned BOOLEAN DEFAULT false,
    views INTEGER DEFAULT 0,
    reactions INTEGER DEFAULT 0,
    comments INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Fleet management tables
CREATE TABLE public.vehicles (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('truck', 'excavator', 'crane', 'bulldozer', 'van', 'car')),
    make TEXT,
    model TEXT,
    year INTEGER,
    license_plate TEXT,
    vin TEXT,
    status TEXT DEFAULT 'available' CHECK (status IN ('available', 'in_use', 'maintenance', 'out_of_service')),
    location TEXT,
    mileage INTEGER DEFAULT 0,
    fuel_level DECIMAL(5,2),
    last_maintenance DATE,
    next_maintenance DATE,
    assigned_to UUID REFERENCES public.profiles(id),
    project_id UUID REFERENCES public.projects(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE public.maintenance_records (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    vehicle_id UUID REFERENCES public.vehicles(id),
    type TEXT NOT NULL CHECK (type IN ('routine', 'repair', 'inspection')),
    description TEXT,
    cost DECIMAL(10,2),
    performed_by TEXT,
    performed_at TIMESTAMP WITH TIME ZONE,
    next_due_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Logistics tables
CREATE TABLE public.suppliers (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    contact_person TEXT,
    email TEXT,
    phone TEXT,
    address TEXT,
    type TEXT CHECK (type IN ('materials', 'equipment', 'services')),
    rating DECIMAL(3,2),
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE public.shipments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    tracking_number TEXT UNIQUE,
    supplier_id UUID REFERENCES public.suppliers(id),
    project_id UUID REFERENCES public.projects(id),
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'shipped', 'in_transit', 'delivered', 'delayed')),
    items JSONB DEFAULT '[]',
    weight DECIMAL(10,2),
    dimensions TEXT,
    shipped_date DATE,
    expected_delivery DATE,
    actual_delivery DATE,
    delivery_location TEXT,
    cost DECIMAL(10,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Quality management tables
CREATE TABLE public.quality_inspections (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    project_id UUID REFERENCES public.projects(id),
    inspector_id UUID REFERENCES public.profiles(id),
    type TEXT NOT NULL CHECK (type IN ('pre_construction', 'in_progress', 'final', 'regulatory')),
    area TEXT,
    status TEXT DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'in_progress', 'completed', 'failed')),
    score INTEGER CHECK (score >= 0 AND score <= 100),
    findings TEXT,
    recommendations TEXT,
    photos JSONB DEFAULT '[]',
    scheduled_date DATE,
    completed_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE public.quality_issues (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    inspection_id UUID REFERENCES public.quality_inspections(id),
    project_id UUID REFERENCES public.projects(id),
    title TEXT NOT NULL,
    description TEXT,
    severity TEXT DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    status TEXT DEFAULT 'open' CHECK (status IN ('open', 'in_progress', 'resolved', 'closed')),
    assigned_to UUID REFERENCES public.profiles(id),
    due_date DATE,
    resolved_date DATE,
    resolution_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- HR/Workforce tables
CREATE TABLE public.employees (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    employee_id TEXT UNIQUE NOT NULL,
    profile_id UUID REFERENCES public.profiles(id),
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    email TEXT,
    phone TEXT,
    position TEXT,
    department TEXT,
    hire_date DATE,
    employment_type TEXT CHECK (type IN ('full_time', 'part_time', 'contract', 'temporary')),
    salary DECIMAL(10,2),
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'terminated', 'on_leave')),
    emergency_contact JSONB DEFAULT '{}',
    skills JSONB DEFAULT '[]',
    certifications JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE public.training_sessions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    instructor TEXT,
    date DATE,
    start_time TIME,
    end_time TIME,
    location TEXT,
    max_participants INTEGER,
    enrolled_participants INTEGER DEFAULT 0,
    category TEXT,
    status TEXT DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'in_progress', 'completed', 'cancelled')),
    cost DECIMAL(8,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE public.training_enrollments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    employee_id UUID REFERENCES public.employees(id),
    session_id UUID REFERENCES public.training_sessions(id),
    enrollment_date DATE DEFAULT CURRENT_DATE,
    status TEXT DEFAULT 'enrolled' CHECK (status IN ('enrolled', 'completed', 'no_show', 'cancelled')),
    score INTEGER CHECK (score >= 0 AND score <= 100),
    completion_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Calendar/Events tables
CREATE TABLE public.events (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    all_day BOOLEAN DEFAULT false,
    location TEXT,
    type TEXT CHECK (type IN ('meeting', 'deadline', 'training', 'maintenance', 'inspection')),
    project_id UUID REFERENCES public.projects(id),
    created_by UUID REFERENCES public.profiles(id),
    attendees JSONB DEFAULT '[]',
    reminders JSONB DEFAULT '[]',
    status TEXT DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'completed', 'cancelled')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Automation workflows
CREATE TABLE public.workflows (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    trigger_type TEXT NOT NULL,
    trigger_config JSONB DEFAULT '{}',
    actions JSONB DEFAULT '[]',
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'error')),
    last_run TIMESTAMP WITH TIME ZONE,
    run_count INTEGER DEFAULT 0,
    created_by UUID REFERENCES public.profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX idx_messages_channel_id ON public.messages(channel_id);
CREATE INDEX idx_messages_created_at ON public.messages(created_at DESC);
CREATE INDEX idx_vehicles_status ON public.vehicles(status);
CREATE INDEX idx_vehicles_assigned_to ON public.vehicles(assigned_to);
CREATE INDEX idx_shipments_status ON public.shipments(status);
CREATE INDEX idx_quality_inspections_project_id ON public.quality_inspections(project_id);
CREATE INDEX idx_employees_status ON public.employees(status);
CREATE INDEX idx_events_start_date ON public.events(start_date);

-- Enable RLS
ALTER TABLE public.channels ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.announcements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.vehicles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.maintenance_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.suppliers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.shipments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.quality_inspections ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.quality_issues ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.employees ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.training_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.training_enrollments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.events ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.workflows ENABLE ROW LEVEL SECURITY;

-- Create triggers for updated_at
CREATE TRIGGER handle_channels_updated_at BEFORE UPDATE ON public.channels FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();
CREATE TRIGGER handle_announcements_updated_at BEFORE UPDATE ON public.announcements FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();
CREATE TRIGGER handle_vehicles_updated_at BEFORE UPDATE ON public.vehicles FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();
CREATE TRIGGER handle_suppliers_updated_at BEFORE UPDATE ON public.suppliers FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();
CREATE TRIGGER handle_shipments_updated_at BEFORE UPDATE ON public.shipments FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();
CREATE TRIGGER handle_quality_inspections_updated_at BEFORE UPDATE ON public.quality_inspections FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();
CREATE TRIGGER handle_quality_issues_updated_at BEFORE UPDATE ON public.quality_issues FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();
CREATE TRIGGER handle_employees_updated_at BEFORE UPDATE ON public.employees FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();
CREATE TRIGGER handle_training_sessions_updated_at BEFORE UPDATE ON public.training_sessions FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();
CREATE TRIGGER handle_events_updated_at BEFORE UPDATE ON public.events FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();
CREATE TRIGGER handle_workflows_updated_at BEFORE UPDATE ON public.workflows FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

-- AI Provider Configurations
CREATE TABLE public.ai_providers (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('ollama', 'openai', 'anthropic', 'google', 'huggingface', 'openrouter', 'nvidia', 'mistral', 'cohere', 'cloudflare')),
    base_url TEXT,
    api_key_encrypted TEXT,
    models JSONB DEFAULT '[]',
    is_local BOOLEAN DEFAULT false,
    privacy_score INTEGER DEFAULT 5 CHECK (privacy_score >= 1 AND privacy_score <= 10),
    cost_per_request DECIMAL(10,6) DEFAULT 0,
    rate_limits JSONB DEFAULT '{}',
    capabilities JSONB DEFAULT '[]',
    average_response_time INTEGER DEFAULT 2000,
    reliability DECIMAL(3,2) DEFAULT 0.9,
    is_active BOOLEAN DEFAULT true,
    configuration JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI Provider Usage Tracking
CREATE TABLE public.ai_provider_usage (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    provider_id UUID REFERENCES public.ai_providers(id),
    requests_today INTEGER DEFAULT 0,
    requests_this_minute INTEGER DEFAULT 0,
    tokens_today INTEGER DEFAULT 0,
    tokens_this_minute INTEGER DEFAULT 0,
    total_cost DECIMAL(10,4) DEFAULT 0,
    last_request_time TIMESTAMP WITH TIME ZONE,
    last_reset_date DATE DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Lead Scoring System
CREATE TABLE public.lead_scores (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    contact_id UUID REFERENCES public.contacts(id),
    company_id UUID REFERENCES public.companies(id),
    opportunity_id UUID REFERENCES public.opportunities(id),
    overall_score INTEGER DEFAULT 0 CHECK (overall_score >= 0 AND overall_score <= 100),
    demographic_score INTEGER DEFAULT 0,
    behavioral_score INTEGER DEFAULT 0,
    engagement_score INTEGER DEFAULT 0,
    firmographic_score INTEGER DEFAULT 0,
    conversion_probability DECIMAL(5,4) DEFAULT 0,
    predicted_value DECIMAL(15,2) DEFAULT 0,
    optimal_contact_time TIMESTAMP WITH TIME ZONE,
    recommended_actions JSONB DEFAULT '[]',
    ai_insights JSONB DEFAULT '{}',
    last_calculated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Lead Scoring Factors
CREATE TABLE public.lead_scoring_factors (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    category TEXT NOT NULL CHECK (category IN ('demographic', 'behavioral', 'engagement', 'firmographic')),
    weight DECIMAL(3,2) DEFAULT 1.0,
    scoring_logic JSONB NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES public.profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Customer Health Scores
CREATE TABLE public.customer_health (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    company_id UUID REFERENCES public.companies(id),
    contact_id UUID REFERENCES public.contacts(id),
    health_score INTEGER DEFAULT 50 CHECK (health_score >= 0 AND health_score <= 100),
    churn_risk TEXT DEFAULT 'low' CHECK (churn_risk IN ('low', 'medium', 'high', 'critical')),
    churn_probability DECIMAL(5,4) DEFAULT 0,
    engagement_trend TEXT DEFAULT 'stable' CHECK (engagement_trend IN ('improving', 'stable', 'declining')),
    last_interaction TIMESTAMP WITH TIME ZONE,
    interaction_frequency INTEGER DEFAULT 0,
    support_tickets_count INTEGER DEFAULT 0,
    satisfaction_score DECIMAL(3,2),
    revenue_trend DECIMAL(10,2) DEFAULT 0,
    predicted_ltv DECIMAL(15,2),
    risk_factors JSONB DEFAULT '[]',
    recommended_actions JSONB DEFAULT '[]',
    ai_analysis JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI Conversation History Extended
CREATE TABLE public.ai_conversations_extended (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    conversation_id UUID REFERENCES public.conversations(id),
    provider_id UUID REFERENCES public.ai_providers(id),
    model_used TEXT,
    prompt_tokens INTEGER DEFAULT 0,
    completion_tokens INTEGER DEFAULT 0,
    total_tokens INTEGER DEFAULT 0,
    cost DECIMAL(10,6) DEFAULT 0,
    response_time_ms INTEGER DEFAULT 0,
    quality_score DECIMAL(3,2),
    user_feedback TEXT CHECK (user_feedback IN ('positive', 'negative', 'neutral')),
    context_data JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for AI and lead scoring tables
CREATE INDEX idx_lead_scores_contact_id ON public.lead_scores(contact_id);
CREATE INDEX idx_lead_scores_company_id ON public.lead_scores(company_id);
CREATE INDEX idx_lead_scores_overall_score ON public.lead_scores(overall_score DESC);
CREATE INDEX idx_lead_scores_conversion_probability ON public.lead_scores(conversion_probability DESC);
CREATE INDEX idx_customer_health_company_id ON public.customer_health(company_id);
CREATE INDEX idx_customer_health_churn_risk ON public.customer_health(churn_risk);
CREATE INDEX idx_customer_health_health_score ON public.customer_health(health_score);
CREATE INDEX idx_ai_provider_usage_provider_id ON public.ai_provider_usage(provider_id);
CREATE INDEX idx_ai_provider_usage_last_reset_date ON public.ai_provider_usage(last_reset_date);
CREATE INDEX idx_ai_conversations_extended_provider_id ON public.ai_conversations_extended(provider_id);

-- Enable Row Level Security for new tables
ALTER TABLE public.ai_providers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ai_provider_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.lead_scores ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.lead_scoring_factors ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customer_health ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ai_conversations_extended ENABLE ROW LEVEL SECURITY;

-- Create updated_at triggers for new tables
CREATE TRIGGER handle_ai_providers_updated_at
    BEFORE UPDATE ON public.ai_providers
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_ai_provider_usage_updated_at
    BEFORE UPDATE ON public.ai_provider_usage
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_lead_scores_updated_at
    BEFORE UPDATE ON public.lead_scores
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_lead_scoring_factors_updated_at
    BEFORE UPDATE ON public.lead_scoring_factors
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_customer_health_updated_at
    BEFORE UPDATE ON public.customer_health
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();