# SPQR Production Environment Configuration
# Server: vince@**************

# Application Configuration
VITE_APP_ENV=production
VITE_APP_NAME="SPQR CRM+AI System"
VITE_APP_VERSION=1.0.0
VITE_ENABLE_DEBUG=false

# Database Configuration - Local Supabase (Data Sovereignty)
VITE_SUPABASE_URL=http://**************:3000
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0

# AI Configuration - Hybrid approach
VITE_AI_PROVIDER=ollama
VITE_OLLAMA_URL=http://localhost:11434
VITE_OLLAMA_MODEL=llama3.1:8b
VITE_OLLAMA_EMBEDDING_MODEL=nomic-embed-text

# Multi-Provider AI Configuration (All Available Providers)
VITE_OPENAI_API_KEY=********************************************************************************************************************************************************************
VITE_ANTHROPIC_API_KEY=************************************************************************************************************
VITE_GOOGLE_AI_API_KEY=AIzaSyDEZPNmGvZqnfgNFev7Zss4NSGbJzWNmbs
VITE_OPENROUTER_API_KEY=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
VITE_NVIDIA_NIM_API_KEY=**********************************************************************
VITE_MISTRAL_API_KEY=5zzh6GldrXGuxWEO8u7XvVGv2STvfKzK
VITE_COHERE_API_KEY=d2cchd71cXgNdh2IcFkrNNrJuVYjpdBSzBCsna0X
VITE_CLOUDFLARE_API_KEY=****************************************
VITE_HUGGINGFACE_API_KEY=*************************************
VITE_VAPI_API_KEY=342ca77b-f45d-411c-a104-32f7bf4552ec

# Feature Flags - Production Ready
VITE_ENABLE_VOICE=true
VITE_ENABLE_REALTIME=true
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_DEBUG=false
VITE_ENABLE_AI_TESTING=false

# Security - Production Grade
VITE_ENCRYPTION_KEY=your_secure_32_character_production_key_here

# Performance Settings - Optimized for Server
VITE_CACHE_DURATION=3600000
VITE_MAX_UPLOAD_SIZE=52428800
VITE_API_TIMEOUT=60000

# External Integrations
VITE_N8N_WEBHOOK_URL=http://localhost:5678/webhook
VITE_SMTP_HOST=smtp.gmail.com
VITE_SMTP_PORT=587

# Google OAuth Configuration
VITE_GOOGLE_CLIENT_ID=429307374501-08jvm0irh1geer7378rbu6vr247svmng.apps.googleusercontent.com
VITE_GOOGLE_CLIENT_SECRET=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
VITE_GOOGLE_PROJECT_ID=jarvis-386211

# Production Logging
VITE_LOG_LEVEL=error
VITE_MOCK_DATA=false
VITE_DEV_TOOLS=false

# Docker Environment Variables
POSTGRES_DB=spqr_production
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_secure_postgres_password
JWT_SECRET=your-super-secret-jwt-token-with-at-least-32-characters-long
SECRET_KEY_BASE=your-secret-key-base-for-realtime-service
REDIS_PASSWORD=your_secure_redis_password
GRAFANA_PASSWORD=your_secure_grafana_password

# Server Configuration
API_EXTERNAL_URL=http://**************:8080
SITE_URL=http://**************:8080
GOTRUE_URI_ALLOW_LIST=http://**************:8080,http://localhost:8080

# Enhanced Features Configuration
VITE_ENABLE_LEAD_SCORING=true
VITE_ENABLE_CUSTOMER_HEALTH=true
VITE_ENABLE_MARKET_INTELLIGENCE=false
VITE_ENABLE_DOCUMENT_INTELLIGENCE=false
VITE_ENABLE_PROCESS_OPTIMIZATION=false

# AI Provider Routing Configuration
VITE_AI_ROUTING_STRATEGY=intelligent
VITE_AI_PRIVACY_FIRST=true
VITE_AI_COST_OPTIMIZATION=true
VITE_AI_FALLBACK_ENABLED=true
