# SPQR Production Environment Configuration
# Server: vince@**************

# Application Configuration
VITE_APP_ENV=production
VITE_APP_NAME="SPQR CRM+AI System"
VITE_APP_VERSION=1.0.0
VITE_ENABLE_DEBUG=false

# Database Configuration - Cloud Supabase
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_production_anon_key_here

# AI Configuration - Hybrid approach
VITE_AI_PROVIDER=ollama
VITE_OLLAMA_URL=http://localhost:11434
VITE_OLLAMA_MODEL=llama3.1:8b
VITE_OLLAMA_EMBEDDING_MODEL=nomic-embed-text

# Cloud AI Providers (backup/fallback)
# VITE_OPENAI_API_KEY=sk-your_production_openai_key
# VITE_ANTHROPIC_API_KEY=sk-ant-your_production_anthropic_key

# Feature Flags - Production Ready
VITE_ENABLE_VOICE=true
VITE_ENABLE_REALTIME=true
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_DEBUG=false
VITE_ENABLE_AI_TESTING=false

# Security - Production Grade
VITE_ENCRYPTION_KEY=your_secure_32_character_production_key_here

# Performance Settings - Optimized for Server
VITE_CACHE_DURATION=3600000
VITE_MAX_UPLOAD_SIZE=52428800
VITE_API_TIMEOUT=60000

# External Integrations
# VITE_N8N_WEBHOOK_URL=http://localhost:5678/webhook
# VITE_SMTP_HOST=smtp.gmail.com
# VITE_SMTP_PORT=587
# VITE_SMTP_USER=<EMAIL>
# VITE_SMTP_PASS=your-app-password

# Production Logging
VITE_LOG_LEVEL=error
VITE_MOCK_DATA=false
VITE_DEV_TOOLS=false

# Docker Environment Variables
POSTGRES_DB=spqr_production
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_secure_postgres_password
JWT_SECRET=your-super-secret-jwt-token-with-at-least-32-characters-long
SECRET_KEY_BASE=your-secret-key-base-for-realtime-service
REDIS_PASSWORD=your_secure_redis_password
GRAFANA_PASSWORD=your_secure_grafana_password

# Server Configuration
API_EXTERNAL_URL=http://**************:8080
SITE_URL=http://**************:8080
GOTRUE_URI_ALLOW_LIST=http://**************:8080,http://localhost:8080
