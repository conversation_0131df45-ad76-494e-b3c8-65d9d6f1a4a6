
# Eigen CRM Schmiede

A next-generation AI-powered CRM system with autonomous agents, real-time intelligence, and comprehensive business management capabilities.

## 🚀 Features

### Core CRM
- **People & Companies Management** - Comprehensive contact and organization tracking
- **Opportunities & Deals** - Sales pipeline with AI-driven insights
- **Tasks & Projects** - Automated workflow management
- **Communication Hub** - Integrated messaging and collaboration

### AI-Powered Intelligence
- **Multi-Agent System** - Autonomous AI agents for various business functions
- **Enhanced Context Management** - Smart conversation and task context
- **Voice Control** - Natural language commands and voice interaction
- **NLP Processing** - Advanced intent classification and entity extraction
- **Real-time Monitoring** - AI system health and performance tracking

### Business Modules
- **Finance & Revenue Intelligence** - Forecasting and analytics
- **HR & Workforce Management** - Employee tracking and performance
- **Safety & Compliance** - Monitoring and reporting
- **Fleet & Logistics** - Asset and resource management
- **Document Management** - AI-powered indexing and search
- **Quality Assurance** - Process optimization and tracking

### Technical Capabilities
- **Real-time Updates** - WebSocket-based live data synchronization
- **Local AI Models** - Integration with Ollama for on-premises AI
- **Cloud Flexibility** - Support for both local and cloud deployments
- **Advanced Security** - Role-based access and data protection
- **Performance Optimization** - Caching, lazy loading, and efficient rendering

## 🛠 Technology Stack

- **Frontend**: React 18, TypeScript, Vite
- **UI/UX**: Tailwind CSS, shadcn/ui, Lucide icons
- **State Management**: Zustand, React Query
- **Database**: Supabase (local or cloud)
- **AI Models**: Ollama (local), OpenAI/Anthropic (cloud)
- **Real-time**: WebSockets, Supabase Realtime
- **Build Tools**: Vite, ESLint, PostCSS

## 📋 Prerequisites

Before you begin, ensure you have the following installed:
- **Node.js** (version 18 or higher)
- **npm** or **yarn** package manager
- **Docker** (for local Supabase)
- **Git** for version control

## 🚀 Quick Start

### 1. Clone the Repository
```bash
git clone <repository-url>
cd eigen-crm-schmiede
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Environment Setup
```bash
cp .env.example .env.local
```

Edit `.env.local` with your configuration (see [Environment Configuration](#environment-configuration)).

### 4. Start Development Server
```bash
npm run dev
```

The application will be available at `http://localhost:8080`.

## 🔧 Installation & Deployment

For detailed installation instructions, see the [Installation Guide](./docs/INSTALLATION.md).

For deployment options, see the [Deployment Guide](./docs/DEPLOYMENT.md).

## ⚙️ Environment Configuration

### Required Environment Variables

```bash
# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# AI Configuration
VITE_OPENAI_API_KEY=your_openai_key (optional)
VITE_ANTHROPIC_API_KEY=your_anthropic_key (optional)
VITE_OLLAMA_URL=http://localhost:11434 (for local AI)

# Application Settings
VITE_APP_ENV=development
VITE_AI_PROVIDER=ollama # or openai, anthropic
VITE_ENABLE_VOICE=true
VITE_ENABLE_REALTIME=true
```

### Database Options

#### Option 1: Local Supabase (Recommended for Development)
```bash
# Start local Supabase
npm run db:start
```

#### Option 2: Cloud Supabase
1. Create a project at [supabase.com](https://supabase.com)
2. Copy your project URL and anon key to `.env.local`

## 📚 Documentation

- [Installation Guide](./docs/INSTALLATION.md) - Detailed setup instructions
- [Deployment Guide](./docs/DEPLOYMENT.md) - Production deployment options
- [API Documentation](./docs/API.md) - Backend API reference
- [Development Guide](./docs/DEVELOPMENT.md) - Development best practices
- [Troubleshooting](./docs/TROUBLESHOOTING.md) - Common issues and solutions

## 🧪 Testing

```bash
# Run AI integration tests
npm run test:ai

# Run all tests
npm run test

# Run tests in watch mode
npm run test:watch
```

## 🔨 Development Scripts

```bash
# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Lint code
npm run lint

# Type check
npm run type-check

# Database operations
npm run db:start    # Start local Supabase
npm run db:stop     # Stop local Supabase
npm run db:reset    # Reset database
npm run db:seed     # Seed with sample data
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- 📖 [Documentation](./docs/)
- 🐛 [Issue Tracker](./issues)
- 💬 [Discussions](./discussions)

## 🙏 Acknowledgments

- Built with [Lovable](https://lovable.dev) for rapid development
- UI components from [shadcn/ui](https://ui.shadcn.com)
- Icons from [Lucide](https://lucide.dev)
- AI capabilities powered by [Ollama](https://ollama.ai)
