#!/bin/bash

# SPQR AI Providers Testing Script
# Tests all configured AI providers and lead scoring functionality

set -e

# Configuration
SERVER_HOST="**************"
APP_PORT="8080"
BASE_URL="http://${SERVER_HOST}:${APP_PORT}"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_test() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_pass() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_fail() {
    echo -e "${RED}[FAIL]${NC} $1"
}

print_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# Test counters
TESTS_RUN=0
TESTS_PASSED=0
TESTS_FAILED=0

run_test() {
    local test_name="$1"
    local test_command="$2"
    
    TESTS_RUN=$((TESTS_RUN + 1))
    print_test "$test_name"
    
    if eval "$test_command" > /dev/null 2>&1; then
        print_pass "$test_name"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        print_fail "$test_name"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# Test individual AI providers
test_ollama_provider() {
    print_test "Testing Ollama (Local AI)"
    
    # Test Ollama service
    if ssh vince@${SERVER_HOST} "curl -s http://localhost:11434/api/version" > /dev/null 2>&1; then
        print_pass "Ollama service is running"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        
        # Test model availability
        if ssh vince@${SERVER_HOST} "curl -s http://localhost:11434/api/tags | grep -q llama3.1" > /dev/null 2>&1; then
            print_pass "Llama 3.1 model available"
            TESTS_PASSED=$((TESTS_PASSED + 1))
        else
            print_warn "Llama 3.1 model not found"
            TESTS_FAILED=$((TESTS_FAILED + 1))
        fi
        TESTS_RUN=$((TESTS_RUN + 1))
    else
        print_fail "Ollama service not responding"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TESTS_RUN=$((TESTS_RUN + 1))
}

test_openai_provider() {
    print_test "Testing OpenAI GPT-4"
    
    # Test API key configuration
    local api_key_test=$(curl -s -H "Authorization: Bearer ********************************************************************************************************************************************************************" \
        "https://api.openai.com/v1/models" | grep -q "gpt-4")
    
    if [ $? -eq 0 ]; then
        print_pass "OpenAI API key valid and GPT-4 accessible"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        print_fail "OpenAI API key invalid or GPT-4 not accessible"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TESTS_RUN=$((TESTS_RUN + 1))
}

test_anthropic_provider() {
    print_test "Testing Anthropic Claude"
    
    # Test API key configuration
    local api_key_test=$(curl -s -H "x-api-key: ************************************************************************************************************" \
        -H "anthropic-version: 2023-06-01" \
        "https://api.anthropic.com/v1/messages" \
        -d '{"model":"claude-3-haiku-20240307","max_tokens":10,"messages":[{"role":"user","content":"test"}]}' | grep -q "content")
    
    if [ $? -eq 0 ]; then
        print_pass "Anthropic Claude API accessible"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        print_fail "Anthropic Claude API not accessible"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TESTS_RUN=$((TESTS_RUN + 1))
}

test_google_ai_provider() {
    print_test "Testing Google AI (Gemini)"
    
    # Test API key configuration
    local api_key_test=$(curl -s "https://generativelanguage.googleapis.com/v1beta/models?key=AIzaSyDEZPNmGvZqnfgNFev7Zss4NSGbJzWNmbs" | grep -q "gemini")
    
    if [ $? -eq 0 ]; then
        print_pass "Google AI API accessible"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        print_fail "Google AI API not accessible"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TESTS_RUN=$((TESTS_RUN + 1))
}

test_huggingface_provider() {
    print_test "Testing Hugging Face"
    
    # Test API key configuration
    local api_key_test=$(curl -s -H "Authorization: Bearer *************************************" \
        "https://api-inference.huggingface.co/models/meta-llama/Llama-2-7b-chat-hf" \
        -d '{"inputs":"test"}' | grep -q "generated_text\|error")
    
    if [ $? -eq 0 ]; then
        print_pass "Hugging Face API accessible"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        print_fail "Hugging Face API not accessible"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TESTS_RUN=$((TESTS_RUN + 1))
}

test_openrouter_provider() {
    print_test "Testing OpenRouter"
    
    # Test API key configuration
    local api_key_test=$(curl -s -H "Authorization: Bearer 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" \
        "https://openrouter.ai/api/v1/models" | grep -q "data")
    
    if [ $? -eq 0 ]; then
        print_pass "OpenRouter API accessible"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        print_fail "OpenRouter API not accessible"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TESTS_RUN=$((TESTS_RUN + 1))
}

# Test AI provider routing system
test_ai_routing() {
    print_test "Testing AI Provider Routing System"
    
    # This would test the application's AI routing endpoint
    # For now, we'll simulate the test
    if curl -f ${BASE_URL}/api/ai/test > /dev/null 2>&1; then
        print_pass "AI routing system responding"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        print_warn "AI routing endpoint not available (may not be implemented yet)"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TESTS_RUN=$((TESTS_RUN + 1))
}

# Test lead scoring system
test_lead_scoring_system() {
    print_test "Testing Lead Scoring System"
    
    # Test lead scoring endpoint
    if curl -f ${BASE_URL}/api/leads/scoring > /dev/null 2>&1; then
        print_pass "Lead scoring endpoint responding"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        print_warn "Lead scoring endpoint not available (may not be implemented yet)"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TESTS_RUN=$((TESTS_RUN + 1))
}

# Test database schema for AI features
test_ai_database_schema() {
    print_test "Testing AI Database Schema"
    
    # Test if AI tables exist
    local schema_test=$(ssh vince@${SERVER_HOST} "cd /home/<USER>/spqr-production && docker-compose exec -T supabase-db psql -U postgres -c \"SELECT table_name FROM information_schema.tables WHERE table_name IN ('ai_providers', 'lead_scores', 'customer_health');\"" | grep -c "ai_providers\|lead_scores\|customer_health")
    
    if [ "$schema_test" -ge 2 ]; then
        print_pass "AI database tables exist"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        print_fail "AI database tables missing"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TESTS_RUN=$((TESTS_RUN + 1))
}

# Generate comprehensive test report
generate_ai_test_report() {
    echo ""
    echo "🤖 SPQR AI Systems Test Report"
    echo "=============================="
    echo "Server: ${SERVER_HOST}"
    echo "Test Date: $(date)"
    echo ""
    echo "📊 Test Results:"
    echo "Tests Run: ${TESTS_RUN}"
    echo "Tests Passed: ${TESTS_PASSED}"
    echo "Tests Failed: ${TESTS_FAILED}"
    echo "Success Rate: $(( TESTS_PASSED * 100 / TESTS_RUN ))%"
    echo ""
    
    if [[ $TESTS_FAILED -eq 0 ]]; then
        echo "🎉 All AI systems operational! Ready for production use."
        echo ""
        echo "✅ Multi-provider AI system fully functional"
        echo "✅ Lead scoring system ready"
        echo "✅ All API keys configured correctly"
        echo "✅ Database schema properly initialized"
    elif [[ $TESTS_FAILED -le 2 ]]; then
        echo "⚠️  Minor AI system issues detected. Most functionality available."
        echo ""
        echo "🔧 Recommended actions:"
        echo "- Review failed provider configurations"
        echo "- Check API key validity"
        echo "- Verify network connectivity"
    else
        echo "❌ Multiple AI system issues detected. Requires attention."
        echo ""
        echo "🚨 Critical actions needed:"
        echo "- Review all AI provider configurations"
        echo "- Verify API keys and network access"
        echo "- Check database schema initialization"
        echo "- Review application logs"
    fi
    
    echo ""
    echo "📋 AI Provider Status Summary:"
    echo "• Ollama (Local): $([ $TESTS_PASSED -gt 0 ] && echo "✅ Operational" || echo "❌ Issues")"
    echo "• OpenAI GPT-4: $([ $TESTS_PASSED -gt 1 ] && echo "✅ Operational" || echo "❌ Issues")"
    echo "• Anthropic Claude: $([ $TESTS_PASSED -gt 2 ] && echo "✅ Operational" || echo "❌ Issues")"
    echo "• Google Gemini: $([ $TESTS_PASSED -gt 3 ] && echo "✅ Operational" || echo "❌ Issues")"
    echo "• Hugging Face: $([ $TESTS_PASSED -gt 4 ] && echo "✅ Operational" || echo "❌ Issues")"
    echo "• OpenRouter: $([ $TESTS_PASSED -gt 5 ] && echo "✅ Operational" || echo "❌ Issues")"
    echo ""
    echo "🎯 Next Steps:"
    echo "1. Access AI Dashboard: ${BASE_URL}/ai"
    echo "2. Test Lead Scoring: ${BASE_URL}/leads/scoring"
    echo "3. Configure additional providers if needed"
    echo "4. Run production workload tests"
}

# Main test execution
main() {
    echo "🤖 Starting SPQR AI Systems Tests"
    echo "=================================="
    echo "Target: ${BASE_URL}"
    echo "Time: $(date)"
    echo ""
    
    # Test individual AI providers
    test_ollama_provider
    test_openai_provider
    test_anthropic_provider
    test_google_ai_provider
    test_huggingface_provider
    test_openrouter_provider
    
    # Test AI system integration
    test_ai_routing
    test_lead_scoring_system
    test_ai_database_schema
    
    # Generate comprehensive report
    generate_ai_test_report
}

# Check dependencies
if ! command -v curl &> /dev/null; then
    echo "Error: curl is required for testing"
    exit 1
fi

if ! command -v ssh &> /dev/null; then
    echo "Error: ssh is required for testing"
    exit 1
fi

# Run AI tests
main "$@"
