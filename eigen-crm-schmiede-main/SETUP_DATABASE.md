
# Database Setup Instructions

Follow these steps to set up your Supabase database for the CRM application.

## Prerequisites

1. **Supabase Project**: You need an active Supabase project
2. **Environment Variables**: Set up the following in your environment:
   - `VITE_SUPABASE_URL` - Your Supabase project URL
   - `SUPABASE_SERVICE_ROLE_KEY` - Your service role key (not anon key!)

## Step 1: Get Your Supabase Credentials

1. Go to your [Supabase Dashboard](https://supabase.com/dashboard)
2. Select your project
3. Go to **Settings** → **API**
4. Copy the **Project URL** and **service_role** key (NOT the anon key)

## Step 2: Set Environment Variables

Create a `.env` file in your project root:

```bash
VITE_SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here
```

## Step 3: Run the Setup Script

```bash
node scripts/setup-supabase.js
```

This script will:
- Create all necessary database tables
- Set up Row Level Security policies
- Create database functions and triggers
- Verify the setup

## Step 4: Verify Setup

After running the script, you should see:
- ✅ All tables created successfully
- ✅ Functions and triggers in place
- ✅ Ready to use message

## Troubleshooting

**Error: Missing environment variables**
- Double-check your `.env` file
- Make sure you're using the service role key, not anon key

**Error: Function does not exist**
- The script will automatically create required functions
- Make sure your Supabase project has SQL execution enabled

**Error: Permission denied**
- Verify you're using the service role key
- Check that your Supabase project is active

## What Gets Created

The setup creates these main tables:
- `profiles` - User profiles extending Supabase auth
- `companies` - Company information
- `projects` - Project management
- `ai_agents` - AI agent configurations
- `conversations` - Chat history
- `tasks` - Task management
- `documents` - File management
- `safety_reports` - Safety tracking
- `approval_requests` - Approval workflows
- `activity_feed` - System activity logs

## Next Steps

After setup is complete:
1. Start your development server: `npm run dev`
2. Visit the application and create your first user account
3. Begin adding companies, contacts, and projects
4. The mock data will be replaced with real database operations

## Need Help?

If you encounter issues:
1. Check the Supabase Dashboard for any error messages
2. Verify your project is not paused
3. Ensure you have the correct API keys
4. Review the console output for specific error details
