# 🚀 SPQR Production Deployment Checklist

## Pre-Deployment Requirements

### ✅ Server Prerequisites (vince@**************)
- [ ] **Docker & Docker Compose** installed
- [ ] **Node.js 18+** installed
- [ ] **Git** installed
- [ ] **Nginx** (optional, for custom proxy)
- [ ] **SSL certificates** (if using HTTPS)
- [ ] **Firewall** configured (ports 8080, 8443, 5432, 11434)

### ✅ Database Setup
- [ ] **Supabase project** created at supabase.com
- [ ] **Database schema** applied (from `database/schema.sql`)
- [ ] **Row Level Security** policies configured
- [ ] **API keys** obtained (URL + anon key)
- [ ] **Service role key** for admin operations

### ✅ AI Configuration
- [ ] **Ollama** installed on server
- [ ] **AI models** downloaded (`llama3.1:8b`, `nomic-embed-text`)
- [ ] **OpenAI API key** (backup/fallback)
- [ ] **Anthropic API key** (optional)

### ✅ Environment Configuration
- [ ] **Production .env** file configured
- [ ] **Encryption keys** generated (32+ characters)
- [ ] **JWT secrets** configured
- [ ] **Database passwords** set
- [ ] **Redis password** configured

## Deployment Process

### Phase 1: Initial Setup
```bash
# 1. Make deployment script executable
chmod +x deploy-server.sh

# 2. Run deployment
./deploy-server.sh
```

### Phase 2: Configuration
- [ ] **Update .env.production** with actual credentials
- [ ] **Configure Supabase** connection
- [ ] **Set up AI providers** (Ollama + cloud backup)
- [ ] **Configure external integrations** (n8n, SMTP)

### Phase 3: Security Hardening
- [ ] **Change default passwords** for all services
- [ ] **Configure SSL/TLS** certificates
- [ ] **Set up firewall rules**
- [ ] **Enable audit logging**
- [ ] **Configure backup strategy**

## Post-Deployment Verification

### ✅ Application Health
- [ ] **Main application** loads at http://**************:8080
- [ ] **Health endpoint** responds: `/health`
- [ ] **Database connection** working
- [ ] **Authentication** system functional
- [ ] **AI integration** responding

### ✅ Core Features Testing
- [ ] **User registration/login** works
- [ ] **Dashboard** displays correctly
- [ ] **CRM functions** (Companies, People, Opportunities)
- [ ] **Project management** features
- [ ] **Document upload/management**
- [ ] **AI chat** functionality
- [ ] **Voice control** (if enabled)
- [ ] **Real-time updates** working

### ✅ Performance & Monitoring
- [ ] **Response times** acceptable (<2s)
- [ ] **Memory usage** within limits
- [ ] **CPU usage** stable
- [ ] **Disk space** sufficient
- [ ] **Log rotation** configured
- [ ] **Monitoring** dashboards accessible

## Configuration Files Checklist

### Required Files
- [ ] `.env.production` - Production environment variables
- [ ] `docker-compose.yml` - Container orchestration
- [ ] `nginx.conf` - Web server configuration
- [ ] `database/schema.sql` - Database structure

### API Keys & Credentials Needed
```bash
# Supabase
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_anon_key

# AI Providers
VITE_OPENAI_API_KEY=sk-your_key (from @api_keys-and-passwords)
VITE_ANTHROPIC_API_KEY=sk-ant-your_key

# Security
VITE_ENCRYPTION_KEY=32_character_random_string
JWT_SECRET=64_character_jwt_secret
POSTGRES_PASSWORD=secure_postgres_password
REDIS_PASSWORD=secure_redis_password
```

## Troubleshooting Guide

### Common Issues
1. **Port conflicts** - Check if ports 8080, 5432, 11434 are available
2. **Docker permissions** - Ensure user is in docker group
3. **Memory issues** - Ollama requires 4GB+ RAM
4. **Network connectivity** - Check firewall and DNS settings

### Debug Commands
```bash
# Check service status
docker-compose ps

# View logs
docker-compose logs -f crm-app
docker-compose logs -f ollama
docker-compose logs -f supabase-db

# Test connectivity
curl http://localhost:8080/health
curl http://localhost:11434/api/version
```

## Backup Strategy

### Automated Backups
- [ ] **Database backup** script configured
- [ ] **Application files** backup
- [ ] **Docker volumes** backup
- [ ] **Environment files** secure storage

### Backup Commands
```bash
# Database backup
docker exec spqr_supabase-db pg_dump -U postgres > backup_$(date +%Y%m%d).sql

# Full system backup
tar -czf spqr_backup_$(date +%Y%m%d).tar.gz /home/<USER>/spqr-production
```

## Maintenance Tasks

### Daily
- [ ] Check application health
- [ ] Monitor resource usage
- [ ] Review error logs

### Weekly
- [ ] Update AI models
- [ ] Database maintenance
- [ ] Security updates

### Monthly
- [ ] Full system backup
- [ ] Performance review
- [ ] Security audit

## Emergency Contacts & Documentation

### Key Resources
- **Server Access**: ssh vince@**************
- **Application URL**: http://**************:8080
- **Monitoring**: http://**************:3001
- **Documentation**: `/docs` folder
- **API Keys**: `@api_keys-and-passwords` file

### Support Escalation
1. Check logs and monitoring
2. Restart affected services
3. Contact system administrator
4. Escalate to development team

---

**Deployment Status**: ⏳ Ready for deployment
**Last Updated**: $(date)
**Deployed By**: Augment Agent
**Environment**: Production Server (**************)
