
#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function resetDatabase() {
  try {
    console.log('🧹 Resetting database...\n');

    const tables = [
      'activity_feed',
      'safety_reports', 
      'documents',
      'tasks',
      'messages',
      'conversations',
      'approval_requests',
      'ai_agents',
      'projects',
      'companies',
      'profiles'
    ];

    for (const table of tables) {
      console.log(`🗑️  Dropping table: ${table}`);
      try {
        await supabase.rpc('exec_sql', { sql: `DROP TABLE IF EXISTS public.${table} CASCADE;` });
      } catch (error) {
        console.warn(`⚠️  Warning dropping ${table}:`, error.message.split('\n')[0]);
      }
    }

    console.log('\n✅ Database reset completed');
    console.log('Run npm run setup-db to recreate the schema and data');

  } catch (error) {
    console.error('❌ Database reset failed:', error.message);
    process.exit(1);
  }
}

resetDatabase();
