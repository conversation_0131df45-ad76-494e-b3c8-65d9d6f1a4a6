#!/bin/bash

# SPQR Server Environment Setup Script
# Run this on the target server: vince@**************

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Update system
update_system() {
    print_status "Updating system packages..."
    sudo apt update && sudo apt upgrade -y
    print_success "System updated"
}

# Install Docker
install_docker() {
    if command -v docker &> /dev/null; then
        print_success "Docker already installed"
        return
    fi
    
    print_status "Installing Docker..."
    curl -fsSL https://get.docker.com -o get-docker.sh
    sudo sh get-docker.sh
    sudo usermod -aG docker $USER
    rm get-docker.sh
    print_success "Docker installed"
}

# Install Docker Compose
install_docker_compose() {
    if command -v docker-compose &> /dev/null; then
        print_success "Docker Compose already installed"
        return
    fi
    
    print_status "Installing Docker Compose..."
    sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
    print_success "Docker Compose installed"
}

# Install Node.js
install_nodejs() {
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_success "Node.js already installed: $NODE_VERSION"
        return
    fi
    
    print_status "Installing Node.js 18..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt-get install -y nodejs
    print_success "Node.js installed"
}

# Install Ollama
install_ollama() {
    if command -v ollama &> /dev/null; then
        print_success "Ollama already installed"
        return
    fi
    
    print_status "Installing Ollama..."
    curl -fsSL https://ollama.ai/install.sh | sh
    print_success "Ollama installed"
}

# Download AI models
download_ai_models() {
    print_status "Downloading AI models..."
    
    # Start Ollama service
    sudo systemctl enable ollama
    sudo systemctl start ollama
    
    # Wait for service to start
    sleep 5
    
    # Download models
    ollama pull llama3.1:8b
    ollama pull nomic-embed-text
    
    print_success "AI models downloaded"
}

# Install additional tools
install_tools() {
    print_status "Installing additional tools..."
    sudo apt install -y \
        curl \
        wget \
        git \
        htop \
        nano \
        unzip \
        jq \
        nginx \
        certbot \
        python3-certbot-nginx
    print_success "Additional tools installed"
}

# Configure firewall
configure_firewall() {
    print_status "Configuring firewall..."
    
    # Install ufw if not present
    sudo apt install -y ufw
    
    # Reset firewall
    sudo ufw --force reset
    
    # Default policies
    sudo ufw default deny incoming
    sudo ufw default allow outgoing
    
    # Allow SSH
    sudo ufw allow ssh
    
    # Allow SPQR application ports
    sudo ufw allow 8080/tcp comment "SPQR Main Application"
    sudo ufw allow 8443/tcp comment "SPQR HTTPS"
    sudo ufw allow 11434/tcp comment "Ollama AI Service"
    
    # Allow monitoring (optional)
    sudo ufw allow 3001/tcp comment "Grafana Monitoring"
    sudo ufw allow 9090/tcp comment "Prometheus"
    
    # Enable firewall
    sudo ufw --force enable
    
    print_success "Firewall configured"
}

# Create application directory
create_app_directory() {
    print_status "Creating application directory..."
    mkdir -p /home/<USER>/spqr-production
    cd /home/<USER>/spqr-production
    print_success "Application directory created"
}

# Configure system limits
configure_system_limits() {
    print_status "Configuring system limits..."
    
    # Increase file descriptor limits
    echo "* soft nofile 65536" | sudo tee -a /etc/security/limits.conf
    echo "* hard nofile 65536" | sudo tee -a /etc/security/limits.conf
    
    # Configure Docker daemon
    sudo mkdir -p /etc/docker
    cat << EOF | sudo tee /etc/docker/daemon.json
{
    "log-driver": "json-file",
    "log-opts": {
        "max-size": "10m",
        "max-file": "3"
    },
    "storage-driver": "overlay2"
}
EOF
    
    print_success "System limits configured"
}

# Setup log rotation
setup_log_rotation() {
    print_status "Setting up log rotation..."
    
    cat << EOF | sudo tee /etc/logrotate.d/spqr
/home/<USER>/spqr-production/logs/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    create 644 vince vince
}
EOF
    
    print_success "Log rotation configured"
}

# Create systemd service for SPQR
create_systemd_service() {
    print_status "Creating systemd service..."
    
    cat << EOF | sudo tee /etc/systemd/system/spqr.service
[Unit]
Description=SPQR CRM+AI System
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=/home/<USER>/spqr-production
ExecStart=/usr/local/bin/docker-compose up -d
ExecStop=/usr/local/bin/docker-compose down
TimeoutStartSec=0
User=vince
Group=docker

[Install]
WantedBy=multi-user.target
EOF
    
    sudo systemctl daemon-reload
    sudo systemctl enable spqr.service
    
    print_success "Systemd service created"
}

# Verify installation
verify_installation() {
    print_status "Verifying installation..."
    
    echo "System Information:"
    echo "==================="
    echo "OS: $(lsb_release -d | cut -f2)"
    echo "Kernel: $(uname -r)"
    echo "Memory: $(free -h | grep Mem | awk '{print $2}')"
    echo "Disk: $(df -h / | tail -1 | awk '{print $4}') available"
    echo ""
    
    echo "Installed Software:"
    echo "==================="
    echo "Docker: $(docker --version 2>/dev/null || echo 'Not installed')"
    echo "Docker Compose: $(docker-compose --version 2>/dev/null || echo 'Not installed')"
    echo "Node.js: $(node --version 2>/dev/null || echo 'Not installed')"
    echo "Ollama: $(ollama --version 2>/dev/null || echo 'Not installed')"
    echo ""
    
    echo "Services Status:"
    echo "================"
    echo "Docker: $(systemctl is-active docker)"
    echo "Ollama: $(systemctl is-active ollama)"
    echo ""
    
    print_success "Installation verification completed"
}

# Main setup function
main() {
    echo "🚀 SPQR Server Environment Setup"
    echo "================================="
    echo "Target Server: $(hostname)"
    echo "User: $(whoami)"
    echo "Date: $(date)"
    echo ""
    
    update_system
    install_docker
    install_docker_compose
    install_nodejs
    install_ollama
    download_ai_models
    install_tools
    configure_firewall
    create_app_directory
    configure_system_limits
    setup_log_rotation
    create_systemd_service
    verify_installation
    
    echo ""
    echo "🎉 Server setup completed successfully!"
    echo ""
    echo "📋 Next Steps:"
    echo "1. Logout and login again (for Docker group membership)"
    echo "2. Run the deployment script: ./deploy-server.sh"
    echo "3. Configure environment variables in .env.production"
    echo "4. Test the application"
    echo ""
    echo "⚠️  Important Notes:"
    echo "- Firewall is enabled with necessary ports open"
    echo "- Docker and Ollama services are running"
    echo "- System is ready for SPQR deployment"
    echo ""
    print_success "Setup completed! Please reboot the server to ensure all changes take effect."
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root. Please run as the vince user."
   exit 1
fi

# Run main function
main "$@"
