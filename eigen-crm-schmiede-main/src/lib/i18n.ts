
export type Language = 'en' | 'es' | 'de' | 'fr' | 'it' | 'pt';

export interface Translation {
  [key: string]: string | Translation;
}

export const translations: Record<Language, Translation> = {
  en: {
    common: {
      save: 'Save',
      cancel: 'Cancel',
      edit: 'Edit',
      delete: 'Delete',
      create: 'Create',
      update: 'Update',
      export: 'Export',
      import: 'Import',
      preview: 'Preview',
      loading: 'Loading...',
      search: 'Search',
      filter: 'Filter',
      all: 'All'
    },
    dashboard: {
      title: 'AI Revenue Command Center',
      subtitle: 'Your autonomous AI business partner working 24/7 to accelerate revenue',
      activeAgents: 'AI Agents Active',
      conversations: 'AI Conversations Today',
      refreshInsights: 'Refresh AI Insights',
      analyzing: 'Analyzing...'
    },
    instructions: {
      title: 'AI Instructions & Knowledge Base',
      subtitle: 'Manage AI behavior and knowledge for your CRM system',
      addInstruction: 'Add Instruction',
      allInstructions: 'All Instructions',
      general: 'General Instructions',
      company: 'Company-Specific',
      process: 'Process Workflows',
      commands: 'Custom Commands',
      created: 'Instruction created successfully',
      updated: 'Instruction updated successfully',
      deleted: 'Instruction deleted successfully',
      exported: 'Instructions exported successfully',
      form: {
        title: 'Title',
        titlePlaceholder: 'Enter instruction title',
        category: 'Category',
        categoryPlaceholder: 'Select category',
        content: 'Instruction Content',
        contentPlaceholder: 'Describe how the AI should behave...',
        tags: 'Tags (comma-separated)',
        tagsPlaceholder: 'communication, tone, prospects'
      }
    },
    agents: {
      title: 'Autonomous AI Agent Network',
      subtitle: 'Your 24/7 AI business partner ecosystem',
      viewThoughts: 'View Thoughts',
      activityFeed: 'AI Activity Feed',
      latestActions: 'Latest autonomous AI actions'
    }
  },
  es: {
    common: {
      save: 'Guardar',
      cancel: 'Cancelar',
      edit: 'Editar',
      delete: 'Eliminar',
      create: 'Crear',
      update: 'Actualizar',
      export: 'Exportar',
      import: 'Importar',
      preview: 'Vista previa',
      loading: 'Cargando...',
      search: 'Buscar',
      filter: 'Filtrar',
      all: 'Todos'
    },
    dashboard: {
      title: 'Centro de Comando de Ingresos IA',
      subtitle: 'Su socio comercial autónomo de IA trabajando 24/7 para acelerar los ingresos',
      activeAgents: 'Agentes IA Activos',
      conversations: 'Conversaciones IA Hoy',
      refreshInsights: 'Actualizar Insights IA',
      analyzing: 'Analizando...'
    },
    instructions: {
      title: 'Instrucciones IA y Base de Conocimiento',
      subtitle: 'Gestiona el comportamiento y conocimiento de la IA para tu sistema CRM',
      addInstruction: 'Agregar Instrucción',
      allInstructions: 'Todas las Instrucciones',
      general: 'Instrucciones Generales',
      company: 'Específico de Empresa',
      process: 'Flujos de Proceso',
      commands: 'Comandos Personalizados',
      created: 'Instrucción creada exitosamente',
      updated: 'Instrucción actualizada exitosamente',
      deleted: 'Instrucción eliminada exitosamente',
      exported: 'Instrucciones exportadas exitosamente',
      form: {
        title: 'Título',
        titlePlaceholder: 'Ingrese el título de la instrucción',
        category: 'Categoría',
        categoryPlaceholder: 'Seleccionar categoría',
        content: 'Contenido de la Instrucción',
        contentPlaceholder: 'Describe cómo debe comportarse la IA...',
        tags: 'Etiquetas (separadas por comas)',
        tagsPlaceholder: 'comunicación, tono, prospectos'
      }
    },
    agents: {
      title: 'Red de Agentes IA Autónomos',
      subtitle: 'Su ecosistema de socios comerciales IA 24/7',
      viewThoughts: 'Ver Pensamientos',
      activityFeed: 'Feed de Actividad IA',
      latestActions: 'Últimas acciones autónomas de IA'
    }
  },
  de: {
    common: {
      save: 'Speichern',
      cancel: 'Abbrechen',
      edit: 'Bearbeiten',
      delete: 'Löschen',
      create: 'Erstellen',
      update: 'Aktualisieren',
      export: 'Exportieren',
      import: 'Importieren',
      preview: 'Vorschau',
      loading: 'Laden...',
      search: 'Suchen',
      filter: 'Filtern',
      all: 'Alle'
    },
    dashboard: {
      title: 'KI-Umsatz-Kommandozentrale',
      subtitle: 'Ihr autonomer KI-Geschäftspartner arbeitet 24/7, um den Umsatz zu beschleunigen',
      activeAgents: 'Aktive KI-Agenten',
      conversations: 'KI-Gespräche Heute',
      refreshInsights: 'KI-Insights Aktualisieren',
      analyzing: 'Analysiere...'
    },
    instructions: {
      title: 'KI-Anweisungen & Wissensbasis',
      subtitle: 'Verwalten Sie KI-Verhalten und Wissen für Ihr CRM-System',
      addInstruction: 'Anweisung Hinzufügen',
      allInstructions: 'Alle Anweisungen',
      general: 'Allgemeine Anweisungen',
      company: 'Unternehmensspezifisch',
      process: 'Prozess-Workflows',
      commands: 'Benutzerdefinierte Befehle',
      created: 'Anweisung erfolgreich erstellt',
      updated: 'Anweisung erfolgreich aktualisiert',
      deleted: 'Anweisung erfolgreich gelöscht',
      exported: 'Anweisungen erfolgreich exportiert',
      form: {
        title: 'Titel',
        titlePlaceholder: 'Anweisungstitel eingeben',
        category: 'Kategorie',
        categoryPlaceholder: 'Kategorie auswählen',
        content: 'Anweisungsinhalt',
        contentPlaceholder: 'Beschreiben Sie, wie sich die KI verhalten soll...',
        tags: 'Tags (durch Kommas getrennt)',
        tagsPlaceholder: 'kommunikation, ton, interessenten'
      }
    },
    agents: {
      title: 'Autonomes KI-Agenten-Netzwerk',
      subtitle: 'Ihr 24/7 KI-Geschäftspartner-Ökosystem',
      viewThoughts: 'Gedanken Anzeigen',
      activityFeed: 'KI-Aktivitäts-Feed',
      latestActions: 'Neueste autonome KI-Aktionen'
    }
  },
  fr: {
    common: {
      save: 'Enregistrer',
      cancel: 'Annuler',
      edit: 'Modifier',
      delete: 'Supprimer',
      create: 'Créer',
      update: 'Mettre à jour',
      export: 'Exporter',
      import: 'Importer',
      preview: 'Aperçu',
      loading: 'Chargement...',
      search: 'Rechercher',
      filter: 'Filtrer',
      all: 'Tous'
    },
    dashboard: {
      title: 'Centre de Commande des Revenus IA',
      subtitle: 'Votre partenaire commercial autonome IA travaillant 24h/24 pour accélérer les revenus',
      activeAgents: 'Agents IA Actifs',
      conversations: 'Conversations IA Aujourd\'hui',
      refreshInsights: 'Actualiser les Insights IA',
      analyzing: 'Analyse...'
    },
    instructions: {
      title: 'Instructions IA et Base de Connaissances',
      subtitle: 'Gérez le comportement et les connaissances de l\'IA pour votre système CRM',
      addInstruction: 'Ajouter une Instruction',
      allInstructions: 'Toutes les Instructions',
      general: 'Instructions Générales',
      company: 'Spécifique à l\'Entreprise',
      process: 'Flux de Processus',
      commands: 'Commandes Personnalisées',
      created: 'Instruction créée avec succès',
      updated: 'Instruction mise à jour avec succès',
      deleted: 'Instruction supprimée avec succès',
      exported: 'Instructions exportées avec succès',
      form: {
        title: 'Titre',
        titlePlaceholder: 'Entrez le titre de l\'instruction',
        category: 'Catégorie',
        categoryPlaceholder: 'Sélectionner une catégorie',
        content: 'Contenu de l\'Instruction',
        contentPlaceholder: 'Décrivez comment l\'IA doit se comporter...',
        tags: 'Tags (séparés par des virgules)',
        tagsPlaceholder: 'communication, ton, prospects'
      }
    },
    agents: {
      title: 'Réseau d\'Agents IA Autonomes',
      subtitle: 'Votre écosystème de partenaires commerciaux IA 24h/24',
      viewThoughts: 'Voir les Pensées',
      activityFeed: 'Flux d\'Activité IA',
      latestActions: 'Dernières actions autonomes de l\'IA'
    }
  },
  it: {
    common: {
      save: 'Salva',
      cancel: 'Annulla',
      edit: 'Modifica',
      delete: 'Elimina',
      create: 'Crea',
      update: 'Aggiorna',
      export: 'Esporta',
      import: 'Importa',
      preview: 'Anteprima',
      loading: 'Caricamento...',
      search: 'Cerca',
      filter: 'Filtra',
      all: 'Tutti'
    },
    dashboard: {
      title: 'Centro di Comando Ricavi IA',
      subtitle: 'Il tuo partner commerciale autonomo IA che lavora 24/7 per accelerare i ricavi',
      activeAgents: 'Agenti IA Attivi',
      conversations: 'Conversazioni IA Oggi',
      refreshInsights: 'Aggiorna Insights IA',
      analyzing: 'Analizzando...'
    },
    instructions: {
      title: 'Istruzioni IA e Base di Conoscenza',
      subtitle: 'Gestisci il comportamento e la conoscenza dell\'IA per il tuo sistema CRM',
      addInstruction: 'Aggiungi Istruzione',
      allInstructions: 'Tutte le Istruzioni',
      general: 'Istruzioni Generali',
      company: 'Specifico dell\'Azienda',
      process: 'Flussi di Processo',
      commands: 'Comandi Personalizzati',
      created: 'Istruzione creata con successo',
      updated: 'Istruzione aggiornata con successo',
      deleted: 'Istruzione eliminata con successo',
      exported: 'Istruzioni esportate con successo',
      form: {
        title: 'Titolo',
        titlePlaceholder: 'Inserisci il titolo dell\'istruzione',
        category: 'Categoria',
        categoryPlaceholder: 'Seleziona categoria',
        content: 'Contenuto dell\'Istruzione',
        contentPlaceholder: 'Descrivi come l\'IA dovrebbe comportarsi...',
        tags: 'Tag (separati da virgole)',
        tagsPlaceholder: 'comunicazione, tono, prospect'
      }
    },
    agents: {
      title: 'Rete di Agenti IA Autonomi',
      subtitle: 'Il tuo ecosistema di partner commerciali IA 24/7',
      viewThoughts: 'Visualizza Pensieri',
      activityFeed: 'Feed Attività IA',
      latestActions: 'Ultime azioni autonome dell\'IA'
    }
  },
  pt: {
    common: {
      save: 'Salvar',
      cancel: 'Cancelar',
      edit: 'Editar',
      delete: 'Excluir',
      create: 'Criar',
      update: 'Atualizar',
      export: 'Exportar',
      import: 'Importar',
      preview: 'Visualizar',
      loading: 'Carregando...',
      search: 'Pesquisar',
      filter: 'Filtrar',
      all: 'Todos'
    },
    dashboard: {
      title: 'Centro de Comando de Receitas IA',
      subtitle: 'Seu parceiro comercial autônomo de IA trabalhando 24/7 para acelerar receitas',
      activeAgents: 'Agentes IA Ativos',
      conversations: 'Conversas IA Hoje',
      refreshInsights: 'Atualizar Insights IA',
      analyzing: 'Analisando...'
    },
    instructions: {
      title: 'Instruções IA e Base de Conhecimento',
      subtitle: 'Gerencie o comportamento e conhecimento da IA para seu sistema CRM',
      addInstruction: 'Adicionar Instrução',
      allInstructions: 'Todas as Instruções',
      general: 'Instruções Gerais',
      company: 'Específico da Empresa',
      process: 'Fluxos de Processo',
      commands: 'Comandos Personalizados',
      created: 'Instrução criada com sucesso',
      updated: 'Instrução atualizada com sucesso',
      deleted: 'Instrução excluída com sucesso',
      exported: 'Instruções exportadas com sucesso',
      form: {
        title: 'Título',
        titlePlaceholder: 'Digite o título da instrução',
        category: 'Categoria',
        categoryPlaceholder: 'Selecionar categoria',
        content: 'Conteúdo da Instrução',
        contentPlaceholder: 'Descreva como a IA deve se comportar...',
        tags: 'Tags (separadas por vírgulas)',
        tagsPlaceholder: 'comunicação, tom, prospects'
      }
    },
    agents: {
      title: 'Rede de Agentes IA Autônomos',
      subtitle: 'Seu ecossistema de parceiros comerciais IA 24/7',
      viewThoughts: 'Ver Pensamentos',
      activityFeed: 'Feed de Atividade IA',
      latestActions: 'Últimas ações autônomas da IA'
    }
  }
};

export const getTranslation = (lang: Language, key: string): string => {
  const keys = key.split('.');
  let translation: any = translations[lang];
  
  for (const k of keys) {
    translation = translation?.[k];
  }
  
  return typeof translation === 'string' ? translation : key;
};
