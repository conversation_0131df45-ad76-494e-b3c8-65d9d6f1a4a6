
import { createClient } from '@supabase/supabase-js'

export interface Database {
  public: {
    Tables: {
      companies: {
        Row: {
          id: string
          name: string
          industry?: string
          size?: string
          location?: string
          website?: string
          phone?: string
          email?: string
          description?: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          industry?: string
          size?: string
          location?: string
          website?: string
          phone?: string
          email?: string
          description?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          industry?: string
          size?: string
          location?: string
          website?: string
          phone?: string
          email?: string
          description?: string
          created_at?: string
          updated_at?: string
        }
      }
      contacts: {
        Row: {
          id: string
          first_name: string
          last_name: string
          email?: string
          phone?: string
          position?: string
          company_id?: string
          type?: 'client' | 'contractor' | 'employee' | 'vendor'
          status?: 'active' | 'inactive'
          notes?: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          first_name: string
          last_name: string
          email?: string
          phone?: string
          position?: string
          company_id?: string
          type?: 'client' | 'contractor' | 'employee' | 'vendor'
          status?: 'active' | 'inactive'
          notes?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          first_name?: string
          last_name?: string
          email?: string
          phone?: string
          position?: string
          company_id?: string
          type?: 'client' | 'contractor' | 'employee' | 'vendor'
          status?: 'active' | 'inactive'
          notes?: string
          created_at?: string
          updated_at?: string
        }
      }
      opportunities: {
        Row: {
          id: string
          title: string
          description?: string
          value: number
          stage: string
          probability: number
          expected_close_date?: string
          company_id?: string
          contact_id?: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description?: string
          value: number
          stage: string
          probability: number
          expected_close_date?: string
          company_id?: string
          contact_id?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string
          value?: number
          stage?: string
          probability?: number
          expected_close_date?: string
          company_id?: string
          contact_id?: string
          created_at?: string
          updated_at?: string
        }
      }
      conversations: {
        Row: {
          id: string
          user_message: string
          ai_response?: string
          context?: any
          created_at: string
        }
        Insert: {
          id?: string
          user_message: string
          ai_response?: string
          context?: any
          created_at?: string
        }
        Update: {
          id?: string
          user_message?: string
          ai_response?: string
          context?: any
          created_at?: string
        }
      }
      ai_agents: {
        Row: {
          id: string
          name: string
          type: string
          status: string
          configuration: any
          last_activity: string
          created_by?: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          type: string
          status: string
          configuration: any
          last_activity?: string
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          type?: string
          status?: string
          configuration?: any
          last_activity?: string
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
      }
    }
  }
}

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY

export const isSupabaseConfigured = () => {
  return !!(supabaseUrl && supabaseKey && supabaseUrl !== 'your-supabase-url' && supabaseKey !== 'your-supabase-anon-key')
}

export const supabase = createClient<Database>(
  supabaseUrl || 'https://placeholder.supabase.co',
  supabaseKey || 'placeholder-key'
)
