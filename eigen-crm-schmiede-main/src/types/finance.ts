
export interface Invoice {
  id: string;
  client: string;
  project: string;
  amount: number;
  status: 'paid' | 'pending' | 'overdue' | 'draft';
  dueDate: string;
  issueDate: string;
  description: string;
}

export interface Expense {
  id: string;
  category: string;
  description: string;
  amount: number;
  date: string;
  project: string;
  vendor: string;
  status: 'approved' | 'pending' | 'rejected';
}

export interface FinanceMetrics {
  totalRevenue: number;
  paidInvoices: number;
  pendingInvoices: number;
  totalExpenses: number;
  netProfit: number;
}
