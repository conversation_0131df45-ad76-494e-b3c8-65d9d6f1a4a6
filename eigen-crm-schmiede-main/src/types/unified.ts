
// Unified type definitions for the entire application
export type TaskType = 
  | 'conversation'
  | 'analysis'
  | 'generation'
  | 'project_planning'
  | 'process_automation'
  | 'document_generation'
  | 'executive_summary'
  | 'market_analysis'
  | 'revenue_forecast'
  | 'business_intelligence'
  | 'competitor_analysis'
  | 'trend_analysis'
  | 'opportunity_discovery'
  | 'research'
  | 'code_generation'
  | 'creative'
  | 'translation'

export type LLMProvider = 'cloud' | 'ollama'
export type RoutingProfile = 'privacy' | 'efficiency' | 'cost' | 'balanced'
export type DataSensitivity = 'low' | 'medium' | 'high'

export interface UnifiedLLMRequest {
  task: TaskType
  prompt: string
  context?: Record<string, any>
  model?: string
  temperature?: number
  maxTokens?: number
  preferredProvider?: LLMProvider
  dataSensitivity?: DataSensitivity
  preferredProfile?: RoutingProfile
  maxCost?: number
}

export interface UnifiedLLMResponse {
  response: string
  confidence?: number
  provider?: string
  model?: string
  processingTime?: number
  usage?: {
    promptTokens: number
    completionTokens: number
    totalTokens: number
  }
  metadata?: Record<string, any>
}

export interface UnifiedServiceResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  metadata?: Record<string, any>
}

export interface UnifiedProject {
  id: string
  name: string
  description: string
  status: 'planning' | 'active' | 'on-hold' | 'completed' | 'cancelled'
  priority: 'low' | 'medium' | 'high' | 'critical'
  startDate: string
  endDate: string
  estimatedHours: number
  actualHours?: number
  budget: number
  spentBudget?: number
  progress: number
  team: UnifiedTeamMember[]
  tasks: UnifiedTask[]
  risks: UnifiedRisk[]
  milestones: UnifiedMilestone[]
  createdAt: string
  updatedAt: string
}

export interface UnifiedTask {
  id: string
  title: string
  description: string
  status: 'todo' | 'in-progress' | 'review' | 'completed' | 'blocked'
  priority: 'low' | 'medium' | 'high'
  assignedTo?: string
  estimatedHours: number
  actualHours?: number
  dependencies: string[]
  dueDate: string
  tags: string[]
  projectId?: string
  createdAt: string
  updatedAt: string
}

export interface UnifiedTeamMember {
  id: string
  name: string
  role: string
  email: string
  hourlyRate?: number
  availability: number
  skills: string[]
  avatar?: string
}

export interface UnifiedRisk {
  id: string
  description: string
  impact: 'low' | 'medium' | 'high'
  probability: 'low' | 'medium' | 'high'
  mitigation: string
  status: 'identified' | 'mitigating' | 'resolved'
  assignedTo?: string
  dueDate?: string
}

export interface UnifiedMilestone {
  id: string
  title: string
  description: string
  dueDate: string
  status: 'pending' | 'completed' | 'delayed'
  dependencies: string[]
  progress: number
}

export interface UnifiedAgent {
  id: string
  name: string
  type: string
  status: 'idle' | 'running' | 'error' | 'maintenance'
  capabilities: string[]
  currentTask?: string
  lastActivity?: string
  performance: {
    tasksCompleted: number
    successRate: number
    averageResponseTime: number
  }
  configuration: Record<string, any>
}

export interface UnifiedAutomationScript {
  id: string
  name: string
  description: string
  category: string
  status: 'active' | 'inactive' | 'running' | 'error'
  trigger: string
  actions: any[]
  schedule?: {
    enabled: boolean
    frequency: string
    nextRun?: string
  }
  performance: {
    executions: number
    successRate: number
    lastRun?: string
  }
}

export interface UnifiedProviderConfig {
  id: string
  name: string
  type: LLMProvider
  models: string[]
  isActive: boolean
  isLocal: boolean
  capabilities: TaskType[]
  rateLimits: {
    requestsPerMinute: number
    requestsPerDay: number
    tokensPerMinute: number
  }
  costPerRequest: number
  averageResponseTime: number
  reliability: number
  privacyScore: number
  healthStatus: 'healthy' | 'degraded' | 'down'
}

export interface UnifiedMetrics {
  totalRequests: number
  successRate: number
  averageResponseTime: number
  costToday: number
  activeProviders: number
  systemHealth: number
}
