
export interface LeadScore {
  leadId: string
  score: number
  confidence: number
  factors: string[]
  nextBestAction: string
  priority: 'hot' | 'warm' | 'cold'
}

export interface DealVelocity {
  dealId: string
  velocity: number
  predictedCloseDate: string
  riskFactors: string[]
  accelerationOpportunities: string[]
}

export interface RevenueInsight {
  id: string
  type: 'opportunity' | 'risk' | 'recommendation'
  title: string
  description: string
  impact: 'high' | 'medium' | 'low'
  confidence: number
  category: string
  actionable: boolean
  actionItems?: string[]
}
