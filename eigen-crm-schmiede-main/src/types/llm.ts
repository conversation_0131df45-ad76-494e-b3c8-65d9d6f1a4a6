
export type TaskType = 
  | 'conversation'
  | 'analysis'
  | 'generation'
  | 'project_planning'
  | 'process_automation'
  | 'document_generation'
  | 'executive_summary'
  | 'market_analysis'
  | 'revenue_forecast'
  | 'business_intelligence'
  | 'competitor_analysis'
  | 'trend_analysis'
  | 'opportunity_discovery'
  | 'research'
  | 'code_generation'
  | 'creative'
  | 'translation'

export interface LLMRequest {
  task: TaskType
  prompt: string
  context?: Record<string, any>
  model?: string
  temperature?: number
  maxTokens?: number
  preferredProvider?: string
}

export interface LLMResponse {
  response: string
  confidence?: number
  provider?: string
  model?: string
  processingTime?: number
  usage?: {
    promptTokens: number
    completionTokens: number
    totalTokens: number
  }
  metadata?: Record<string, any>
}
