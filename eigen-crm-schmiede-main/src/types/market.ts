
export interface MarketTrend {
  id: string
  title: string
  description: string
  trend: 'up' | 'down' | 'stable'
  confidence: number
  industry?: string
  growth?: number
  impact?: 'high' | 'medium' | 'low'
  timeframe?: string
  opportunity?: string
}

export interface CompetitorInsight {
  id: string
  competitor: string
  marketShare: number
  strengths: string[]
  weaknesses: string[]
  pricingStrategy: string
  recentMoves: string[]
  threatLevel: 'high' | 'medium' | 'low'
}

export interface OpportunityDiscovery {
  id: string
  opportunity: string
  market: string
  size: string
  confidence: number
  requirements: string[]
  timeline: string
  competition: string
}
