
export interface Integration {
  id: string;
  name: string;
  description: string;
  status: 'connected' | 'disconnected' | 'error' | 'testing';
  icon: any;
  lastSync?: Date;
  version?: string;
  responseTime?: number;
  errorMessage?: string;
  configUrl?: string;
}

export interface ConnectionTest {
  integrationId: string;
  isRunning: boolean;
  result?: 'success' | 'failed';
  responseTime?: number;
  error?: string;
}

export interface ConnectionTestResult {
  success: boolean;
  responseTime: number;
  error?: string;
}
