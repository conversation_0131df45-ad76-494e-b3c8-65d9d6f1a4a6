
export interface APIKeys {
  perplexity?: string;
  vapi?: string;
  openai?: string;
  anthropic?: string;
  gemini?: string;
}

export interface DatabaseConfig {
  host?: string;
  port?: number;
  database?: string;
  ssl?: boolean;
  poolSize?: number;
}

export interface NotificationConfig {
  email?: boolean;
  push?: boolean;
  slack?: boolean;
  webhooks?: string[];
  frequency?: 'immediate' | 'hourly' | 'daily';
}

export interface RAGConfig {
  chunkSize?: number;
  overlap?: number;
  indexingEnabled?: boolean;
  vectorDimensions?: number;
  embeddingModel?: string;
}

export interface LLMConfig {
  primaryProvider?: string;
  fallbackProvider?: string;
  temperature?: number;
  maxTokens?: number;
  timeout?: number;
}

export interface AutonomyConfig {
  level?: 'manual' | 'assisted' | 'autonomous';
  approvalRequired?: boolean;
  autoExecute?: boolean;
  riskThreshold?: number;
}

export interface SettingsState {
  apiKeys: APIKeys;
  database: DatabaseConfig;
  notifications: NotificationConfig;
  rag: RAGConfig;
  llm: LLMConfig;
  autonomy: AutonomyConfig;
  lastSync?: Date;
}
