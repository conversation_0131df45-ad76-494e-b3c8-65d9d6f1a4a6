import { logger } from './Logger';

interface AuditEvent {
  id: string;
  timestamp: Date;
  userId?: string;
  action: string;
  resource: string;
  resourceId?: string;
  details: Record<string, any>;
  ip?: string;
  userAgent?: string;
  sessionId?: string;
  outcome: 'success' | 'failure' | 'partial';
  risk: 'low' | 'medium' | 'high';
}

class AuditLogger {
  private events: AuditEvent[] = [];
  private maxEvents = 5000;

  public logAuditEvent(event: Omit<AuditEvent, 'id' | 'timestamp' | 'ip' | 'userAgent'>) {
    const auditEvent: AuditEvent = {
      ...event,
      id: this.generateEventId(),
      timestamp: new Date(),
      ip: this.getCurrentIP(),
      userAgent: navigator.userAgent
    };

    this.events.push(auditEvent);
    
    // Keep only the last N events
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(-this.maxEvents);
    }

    logger.info(`Audit: ${event.action} on ${event.resource}`, auditEvent);

    // In production, send to audit service
    if (process.env.NODE_ENV === 'production') {
      this.sendToAuditService(auditEvent);
    }
  }

  private generateEventId(): string {
    return `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getCurrentIP(): string {
    // In a real app, this would get the actual client IP
    return 'client-ip-placeholder';
  }

  private sendToAuditService(event: AuditEvent) {
    // In production, this would send to audit logging services
    console.log('Audit service:', event);
  }

  public getAuditEvents(filters?: {
    userId?: string;
    action?: string;
    resource?: string;
    startDate?: Date;
    endDate?: Date;
    risk?: string;
  }): AuditEvent[] {
    let filteredEvents = [...this.events];

    if (filters) {
      if (filters.userId) {
        filteredEvents = filteredEvents.filter(e => e.userId === filters.userId);
      }
      if (filters.action) {
        filteredEvents = filteredEvents.filter(e => e.action.includes(filters.action));
      }
      if (filters.resource) {
        filteredEvents = filteredEvents.filter(e => e.resource === filters.resource);
      }
      if (filters.startDate) {
        filteredEvents = filteredEvents.filter(e => e.timestamp >= filters.startDate!);
      }
      if (filters.endDate) {
        filteredEvents = filteredEvents.filter(e => e.timestamp <= filters.endDate!);
      }
      if (filters.risk) {
        filteredEvents = filteredEvents.filter(e => e.risk === filters.risk);
      }
    }

    return filteredEvents.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  public getAuditSummary(): Record<string, any> {
    const summary = {
      totalEvents: this.events.length,
      byAction: {} as Record<string, number>,
      byResource: {} as Record<string, number>,
      byOutcome: {} as Record<string, number>,
      byRisk: {} as Record<string, number>,
      recentActivity: this.events.slice(-10)
    };

    this.events.forEach(event => {
      summary.byAction[event.action] = (summary.byAction[event.action] || 0) + 1;
      summary.byResource[event.resource] = (summary.byResource[event.resource] || 0) + 1;
      summary.byOutcome[event.outcome] = (summary.byOutcome[event.outcome] || 0) + 1;
      summary.byRisk[event.risk] = (summary.byRisk[event.risk] || 0) + 1;
    });

    return summary;
  }

  public clearAuditEvents() {
    this.events = [];
  }

  // Convenience methods for common audit events
  public logUserAction(action: string, userId: string, details: Record<string, any> = {}) {
    this.logAuditEvent({
      action,
      resource: 'user',
      resourceId: userId,
      userId,
      details,
      outcome: 'success',
      risk: 'low'
    });
  }

  public logDataAccess(resource: string, resourceId: string, userId?: string, details: Record<string, any> = {}) {
    this.logAuditEvent({
      action: 'data_access',
      resource,
      resourceId,
      userId,
      details,
      outcome: 'success',
      risk: 'medium'
    });
  }

  public logSecurityEvent(action: string, details: Record<string, any> = {}, risk: 'low' | 'medium' | 'high' = 'high') {
    this.logAuditEvent({
      action,
      resource: 'security',
      details,
      outcome: 'failure',
      risk
    });
  }

  public logSystemEvent(action: string, details: Record<string, any> = {}) {
    this.logAuditEvent({
      action,
      resource: 'system',
      details,
      outcome: 'success',
      risk: 'low'
    });
  }
}

export const auditLogger = new AuditLogger();
