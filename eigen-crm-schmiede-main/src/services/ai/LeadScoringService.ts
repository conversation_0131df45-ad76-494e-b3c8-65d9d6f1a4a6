import { enhancedProviderManager, TaskRequirements } from './EnhancedProviderManager';
import { supabase } from '../supabase';
import { logger } from '../Logger';

export interface LeadData {
  contactId?: string;
  companyId?: string;
  opportunityId?: string;
  contact?: {
    name: string;
    email: string;
    phone?: string;
    position?: string;
    department?: string;
    seniority?: string;
  };
  company?: {
    name: string;
    industry?: string;
    size?: string;
    revenue?: number;
    location?: string;
    website?: string;
  };
  interactions?: Array<{
    type: string;
    date: Date;
    channel: string;
    outcome?: string;
  }>;
  opportunity?: {
    value?: number;
    stage?: string;
    probability?: number;
    source?: string;
  };
}

export interface LeadScore {
  overallScore: number;
  demographicScore: number;
  behavioralScore: number;
  engagementScore: number;
  firmographicScore: number;
  conversionProbability: number;
  predictedValue: number;
  optimalContactTime?: Date;
  recommendedActions: string[];
  aiInsights: Record<string, any>;
}

export interface ScoringFactor {
  name: string;
  category: 'demographic' | 'behavioral' | 'engagement' | 'firmographic';
  weight: number;
  scoringLogic: Record<string, any>;
}

export class LeadScoringService {
  private defaultFactors: ScoringFactor[] = [
    {
      name: 'Job Title Relevance',
      category: 'demographic',
      weight: 1.5,
      scoringLogic: {
        'C-Level': 100,
        'VP/Director': 85,
        'Manager': 70,
        'Senior': 60,
        'Junior': 40,
        'Other': 30
      }
    },
    {
      name: 'Company Size',
      category: 'firmographic',
      weight: 1.2,
      scoringLogic: {
        'Enterprise (1000+)': 100,
        'Large (500-999)': 85,
        'Medium (100-499)': 70,
        'Small (50-99)': 55,
        'Startup (<50)': 40
      }
    },
    {
      name: 'Industry Match',
      category: 'firmographic',
      weight: 1.3,
      scoringLogic: {
        'Technology': 90,
        'Financial Services': 85,
        'Healthcare': 80,
        'Manufacturing': 75,
        'Retail': 70,
        'Other': 50
      }
    },
    {
      name: 'Email Engagement',
      category: 'behavioral',
      weight: 1.4,
      scoringLogic: {
        'High (>50% open rate)': 100,
        'Medium (25-50% open rate)': 70,
        'Low (<25% open rate)': 40,
        'No engagement': 10
      }
    },
    {
      name: 'Website Activity',
      category: 'engagement',
      weight: 1.6,
      scoringLogic: {
        'Multiple visits, long sessions': 100,
        'Multiple visits, short sessions': 75,
        'Single visit, long session': 60,
        'Single visit, short session': 40,
        'No visits': 0
      }
    }
  ];

  async calculateLeadScore(leadData: LeadData): Promise<LeadScore> {
    try {
      // Get scoring factors from database or use defaults
      const factors = await this.getScoringFactors();

      // Calculate individual category scores
      const demographicScore = await this.calculateDemographicScore(leadData, factors);
      const behavioralScore = await this.calculateBehavioralScore(leadData, factors);
      const engagementScore = await this.calculateEngagementScore(leadData, factors);
      const firmographicScore = await this.calculateFirmographicScore(leadData, factors);

      // Calculate weighted overall score
      const overallScore = Math.round(
        (demographicScore * 0.25 +
         behavioralScore * 0.25 +
         engagementScore * 0.3 +
         firmographicScore * 0.2)
      );

      // Use AI to predict conversion probability and value
      const aiPredictions = await this.getAIPredictions(leadData, {
        demographicScore,
        behavioralScore,
        engagementScore,
        firmographicScore,
        overallScore
      });

      // Generate recommendations using AI
      const recommendations = await this.generateRecommendations(leadData, {
        demographicScore,
        behavioralScore,
        engagementScore,
        firmographicScore,
        overallScore
      });

      const leadScore: LeadScore = {
        overallScore: Math.min(100, Math.max(0, overallScore)),
        demographicScore,
        behavioralScore,
        engagementScore,
        firmographicScore,
        conversionProbability: aiPredictions.conversionProbability,
        predictedValue: aiPredictions.predictedValue,
        optimalContactTime: aiPredictions.optimalContactTime,
        recommendedActions: recommendations,
        aiInsights: aiPredictions.insights
      };

      // Save to database
      await this.saveLeadScore(leadData, leadScore);

      logger.info('Lead score calculated', {
        contactId: leadData.contactId,
        companyId: leadData.companyId,
        overallScore: leadScore.overallScore,
        conversionProbability: leadScore.conversionProbability
      });

      return leadScore;

    } catch (error) {
      logger.error('Error calculating lead score', { error, leadData });
      throw new Error(`Lead scoring failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async calculateDemographicScore(leadData: LeadData, factors: ScoringFactor[]): Promise<number> {
    const demographicFactors = factors.filter(f => f.category === 'demographic');
    let totalScore = 0;
    let totalWeight = 0;

    for (const factor of demographicFactors) {
      let score = 0;

      if (factor.name === 'Job Title Relevance' && leadData.contact?.position) {
        score = this.scoreByJobTitle(leadData.contact.position, factor.scoringLogic);
      }

      totalScore += score * factor.weight;
      totalWeight += factor.weight;
    }

    return totalWeight > 0 ? Math.round(totalScore / totalWeight) : 0;
  }

  private async calculateBehavioralScore(leadData: LeadData, factors: ScoringFactor[]): Promise<number> {
    const behavioralFactors = factors.filter(f => f.category === 'behavioral');
    let totalScore = 0;
    let totalWeight = 0;

    for (const factor of behavioralFactors) {
      let score = 0;

      if (factor.name === 'Email Engagement' && leadData.interactions) {
        score = this.scoreEmailEngagement(leadData.interactions, factor.scoringLogic);
      }

      totalScore += score * factor.weight;
      totalWeight += factor.weight;
    }

    return totalWeight > 0 ? Math.round(totalScore / totalWeight) : 0;
  }

  private async calculateEngagementScore(leadData: LeadData, factors: ScoringFactor[]): Promise<number> {
    const engagementFactors = factors.filter(f => f.category === 'engagement');
    let totalScore = 0;
    let totalWeight = 0;

    for (const factor of engagementFactors) {
      let score = 0;

      if (factor.name === 'Website Activity' && leadData.interactions) {
        score = this.scoreWebsiteActivity(leadData.interactions, factor.scoringLogic);
      }

      totalScore += score * factor.weight;
      totalWeight += factor.weight;
    }

    return totalWeight > 0 ? Math.round(totalScore / totalWeight) : 0;
  }

  private async calculateFirmographicScore(leadData: LeadData, factors: ScoringFactor[]): Promise<number> {
    const firmographicFactors = factors.filter(f => f.category === 'firmographic');
    let totalScore = 0;
    let totalWeight = 0;

    for (const factor of firmographicFactors) {
      let score = 0;

      if (factor.name === 'Company Size' && leadData.company?.size) {
        score = factor.scoringLogic[leadData.company.size] || 0;
      } else if (factor.name === 'Industry Match' && leadData.company?.industry) {
        score = factor.scoringLogic[leadData.company.industry] || 0;
      }

      totalScore += score * factor.weight;
      totalWeight += factor.weight;
    }

    return totalWeight > 0 ? Math.round(totalScore / totalWeight) : 0;
  }

  private scoreByJobTitle(position: string, scoringLogic: Record<string, number>): number {
    const title = position.toLowerCase();

    if (title.includes('ceo') || title.includes('cto') || title.includes('cfo') || title.includes('chief')) {
      return scoringLogic['C-Level'] || 0;
    } else if (title.includes('vp') || title.includes('director') || title.includes('vice president')) {
      return scoringLogic['VP/Director'] || 0;
    } else if (title.includes('manager') || title.includes('head of')) {
      return scoringLogic['Manager'] || 0;
    } else if (title.includes('senior') || title.includes('lead')) {
      return scoringLogic['Senior'] || 0;
    } else if (title.includes('junior') || title.includes('associate')) {
      return scoringLogic['Junior'] || 0;
    }

    return scoringLogic['Other'] || 0;
  }

  private scoreEmailEngagement(interactions: any[], scoringLogic: Record<string, number>): number {
    const emailInteractions = interactions.filter(i => i.type === 'email');
    if (emailInteractions.length === 0) return scoringLogic['No engagement'] || 0;

    const openRate = emailInteractions.filter(i => i.outcome === 'opened').length / emailInteractions.length;

    if (openRate > 0.5) return scoringLogic['High (>50% open rate)'] || 0;
    if (openRate > 0.25) return scoringLogic['Medium (25-50% open rate)'] || 0;
    if (openRate > 0) return scoringLogic['Low (<25% open rate)'] || 0;

    return scoringLogic['No engagement'] || 0;
  }

  private scoreWebsiteActivity(interactions: any[], scoringLogic: Record<string, number>): number {
    const websiteVisits = interactions.filter(i => i.type === 'website_visit');
    if (websiteVisits.length === 0) return scoringLogic['No visits'] || 0;

    const multipleVisits = websiteVisits.length > 1;
    const hasLongSessions = websiteVisits.some(v => v.duration && v.duration > 300); // 5+ minutes

    if (multipleVisits && hasLongSessions) return scoringLogic['Multiple visits, long sessions'] || 0;
    if (multipleVisits) return scoringLogic['Multiple visits, short sessions'] || 0;
    if (hasLongSessions) return scoringLogic['Single visit, long session'] || 0;

    return scoringLogic['Single visit, short session'] || 0;
  }

  private async getAIPredictions(leadData: LeadData, scores: any): Promise<any> {
    const requirements: TaskRequirements = {
      taskType: 'analysis',
      privacyLevel: 'high',
      maxCost: 0.1,
      maxResponseTime: 5000
    };

    const prompt = `
Analyze this lead data and provide predictions:

Lead Information:
- Contact: ${leadData.contact?.name} (${leadData.contact?.position})
- Company: ${leadData.company?.name} (${leadData.company?.industry}, ${leadData.company?.size})
- Scores: Overall=${scores.overallScore}, Demographic=${scores.demographicScore}, Behavioral=${scores.behavioralScore}, Engagement=${scores.engagementScore}, Firmographic=${scores.firmographicScore}

Please provide:
1. Conversion probability (0-1)
2. Predicted deal value (USD)
3. Optimal contact time (next 7 days)
4. Key insights about this lead

Respond in JSON format:
{
  "conversionProbability": 0.0,
  "predictedValue": 0,
  "optimalContactTime": "2024-01-01T10:00:00Z",
  "insights": {
    "strengths": [],
    "concerns": [],
    "opportunities": []
  }
}
`;

    try {
      const response = await enhancedProviderManager.generateResponse(prompt, requirements);
      const aiResponse = JSON.parse(response.response);

      return {
        conversionProbability: Math.min(1, Math.max(0, aiResponse.conversionProbability)),
        predictedValue: Math.max(0, aiResponse.predictedValue),
        optimalContactTime: aiResponse.optimalContactTime ? new Date(aiResponse.optimalContactTime) : undefined,
        insights: aiResponse.insights || {}
      };
    } catch (error) {
      logger.error('AI prediction failed', { error });
      // Fallback to rule-based predictions
      return {
        conversionProbability: scores.overallScore / 100 * 0.8,
        predictedValue: (leadData.opportunity?.value || 10000) * (scores.overallScore / 100),
        optimalContactTime: undefined,
        insights: {}
      };
    }
  }

  private async generateRecommendations(leadData: LeadData, scores: any): Promise<string[]> {
    const recommendations = [];

    if (scores.engagementScore < 50) {
      recommendations.push('Increase engagement through personalized content');
    }

    if (scores.behavioralScore < 60) {
      recommendations.push('Implement targeted email nurturing campaign');
    }

    if (scores.overallScore > 80) {
      recommendations.push('Priority lead - schedule immediate follow-up');
    } else if (scores.overallScore > 60) {
      recommendations.push('Qualified lead - add to active pipeline');
    } else {
      recommendations.push('Nurture lead with educational content');
    }

    return recommendations;
  }

  private async getScoringFactors(): Promise<ScoringFactor[]> {
    try {
      const { data, error } = await supabase
        .from('lead_scoring_factors')
        .select('*')
        .eq('is_active', true);

      if (error) throw error;

      return data?.map(factor => ({
        name: factor.name,
        category: factor.category,
        weight: factor.weight,
        scoringLogic: factor.scoring_logic
      })) || this.defaultFactors;
    } catch (error) {
      logger.warn('Failed to load scoring factors from database, using defaults', { error });
      return this.defaultFactors;
    }
  }

  private async saveLeadScore(leadData: LeadData, score: LeadScore): Promise<void> {
    try {
      const { error } = await supabase
        .from('lead_scores')
        .upsert({
          contact_id: leadData.contactId,
          company_id: leadData.companyId,
          opportunity_id: leadData.opportunityId,
          overall_score: score.overallScore,
          demographic_score: score.demographicScore,
          behavioral_score: score.behavioralScore,
          engagement_score: score.engagementScore,
          firmographic_score: score.firmographicScore,
          conversion_probability: score.conversionProbability,
          predicted_value: score.predictedValue,
          optimal_contact_time: score.optimalContactTime?.toISOString(),
          recommended_actions: score.recommendedActions,
          ai_insights: score.aiInsights,
          last_calculated: new Date().toISOString()
        });

      if (error) throw error;
    } catch (error) {
      logger.error('Failed to save lead score', { error, leadData });
    }
  }

  async batchCalculateScores(leadIds: string[]): Promise<void> {
    logger.info('Starting batch lead scoring', { count: leadIds.length });

    for (const leadId of leadIds) {
      try {
        // Fetch lead data
        const leadData = await this.fetchLeadData(leadId);
        if (leadData) {
          await this.calculateLeadScore(leadData);
        }
      } catch (error) {
        logger.error('Failed to score lead in batch', { leadId, error });
      }
    }

    logger.info('Batch lead scoring completed', { count: leadIds.length });
  }

  private async fetchLeadData(leadId: string): Promise<LeadData | null> {
    try {
      // Fetch contact data
      const { data: contact, error: contactError } = await supabase
        .from('contacts')
        .select(`
          id,
          name,
          email,
          phone,
          position,
          department,
          company:companies(
            id,
            name,
            industry,
            size,
            revenue,
            location,
            website
          )
        `)
        .eq('id', leadId)
        .single();

      if (contactError) throw contactError;

      // Fetch interactions
      const { data: interactions, error: interactionsError } = await supabase
        .from('interactions')
        .select('*')
        .eq('contact_id', leadId)
        .order('created_at', { ascending: false })
        .limit(50);

      if (interactionsError) throw interactionsError;

      // Fetch opportunities
      const { data: opportunities, error: opportunitiesError } = await supabase
        .from('opportunities')
        .select('*')
        .eq('contact_id', leadId)
        .order('created_at', { ascending: false })
        .limit(1);

      if (opportunitiesError) throw opportunitiesError;

      return {
        contactId: contact.id,
        companyId: contact.company?.id,
        contact: {
          name: contact.name,
          email: contact.email,
          phone: contact.phone,
          position: contact.position,
          department: contact.department
        },
        company: contact.company ? {
          name: contact.company.name,
          industry: contact.company.industry,
          size: contact.company.size,
          revenue: contact.company.revenue,
          location: contact.company.location,
          website: contact.company.website
        } : undefined,
        interactions: interactions?.map(i => ({
          type: i.type,
          date: new Date(i.created_at),
          channel: i.channel,
          outcome: i.outcome
        })) || [],
        opportunity: opportunities?.[0] ? {
          value: opportunities[0].value,
          stage: opportunities[0].stage,
          probability: opportunities[0].probability,
          source: opportunities[0].source
        } : undefined
      };
    } catch (error) {
      logger.error('Failed to fetch lead data', { leadId, error });
      return null;
    }
  }
}

export const leadScoringService = new LeadScoringService();
