import { ProviderConfig, ProviderResponse, ProviderType } from '@/types/provider';
import { defaultProviderConfigs } from '../provider/ProviderConfigs';
import { ollamaService } from './OllamaService';
import { openaiService } from './OpenAIService';
import { anthropicService } from './AnthropicService';
import { huggingFaceService } from './HuggingFaceService';
import { googleAIService } from './GoogleAIService';
import { openRouterService } from './OpenRouterService';
import { logger } from '../Logger';

export interface TaskRequirements {
  taskType: 'conversation' | 'analysis' | 'creative' | 'code_generation' | 'research' | 'embedding';
  privacyLevel: 'low' | 'medium' | 'high' | 'critical';
  maxCost?: number;
  maxResponseTime?: number;
  qualityThreshold?: number;
  dataSize?: 'small' | 'medium' | 'large';
}

export interface ProviderSelection {
  primary: ProviderConfig;
  fallback?: ProviderConfig;
  reasoning: string;
}

export class EnhancedProviderManager {
  private providers: Map<string, ProviderConfig> = new Map();
  private services: Map<ProviderType, any> = new Map();
  private usageStats: Map<string, any> = new Map();

  constructor() {
    this.initializeProviders();
    this.initializeServices();
  }

  private initializeProviders(): void {
    defaultProviderConfigs.forEach(config => {
      this.providers.set(config.id, config);
    });
  }

  private initializeServices(): void {
    this.services.set('ollama', ollamaService);
    this.services.set('openai', openaiService);
    this.services.set('anthropic', anthropicService);
    this.services.set('huggingface', huggingFaceService);
    this.services.set('google', googleAIService);
    this.services.set('openrouter', openRouterService);
  }

  async selectOptimalProvider(requirements: TaskRequirements): Promise<ProviderSelection> {
    const availableProviders = Array.from(this.providers.values())
      .filter(provider => provider.isActive)
      .filter(provider => this.isProviderConfigured(provider))
      .filter(provider => provider.capabilities.includes(requirements.taskType));

    if (availableProviders.length === 0) {
      throw new Error(`No available providers for task type: ${requirements.taskType}`);
    }

    // Score each provider based on requirements
    const scoredProviders = availableProviders.map(provider => ({
      provider,
      score: this.calculateProviderScore(provider, requirements)
    }));

    // Sort by score (highest first)
    scoredProviders.sort((a, b) => b.score - a.score);

    const primary = scoredProviders[0].provider;
    const fallback = scoredProviders.length > 1 ? scoredProviders[1].provider : undefined;

    const reasoning = this.generateSelectionReasoning(primary, requirements, scoredProviders[0].score);

    return { primary, fallback, reasoning };
  }

  private calculateProviderScore(provider: ProviderConfig, requirements: TaskRequirements): number {
    let score = 0;

    // Privacy score weight (critical for sensitive data)
    if (requirements.privacyLevel === 'critical') {
      score += provider.privacyScore * 20;
    } else if (requirements.privacyLevel === 'high') {
      score += provider.privacyScore * 15;
    } else if (requirements.privacyLevel === 'medium') {
      score += provider.privacyScore * 10;
    } else {
      score += provider.privacyScore * 5;
    }

    // Cost efficiency
    if (requirements.maxCost) {
      if (provider.costPerRequest <= requirements.maxCost) {
        score += (requirements.maxCost - provider.costPerRequest) * 100;
      } else {
        score -= 50; // Penalty for exceeding budget
      }
    } else {
      score += (2.0 - provider.costPerRequest) * 10; // Prefer lower cost
    }

    // Response time
    if (requirements.maxResponseTime) {
      if (provider.averageResponseTime <= requirements.maxResponseTime) {
        score += (requirements.maxResponseTime - provider.averageResponseTime) / 100;
      } else {
        score -= 30; // Penalty for being too slow
      }
    } else {
      score += (5000 - provider.averageResponseTime) / 100; // Prefer faster responses
    }

    // Reliability
    score += provider.reliability * 50;

    // Local preference for high privacy
    if (provider.isLocal && requirements.privacyLevel === 'critical') {
      score += 100;
    }

    // Task-specific bonuses
    if (requirements.taskType === 'code_generation' && provider.capabilities.includes('code_generation')) {
      score += 20;
    }
    if (requirements.taskType === 'creative' && provider.capabilities.includes('creative')) {
      score += 20;
    }
    if (requirements.taskType === 'research' && provider.capabilities.includes('research')) {
      score += 20;
    }

    // Rate limit consideration
    const usage = this.usageStats.get(provider.id);
    if (usage && this.isRateLimited(provider, usage)) {
      score -= 100; // Heavy penalty for rate-limited providers
    }

    return Math.max(0, score);
  }

  private generateSelectionReasoning(provider: ProviderConfig, requirements: TaskRequirements, score: number): string {
    const reasons = [];

    if (provider.isLocal && requirements.privacyLevel === 'critical') {
      reasons.push('local processing for maximum privacy');
    }

    if (provider.costPerRequest < 0.5) {
      reasons.push('cost-effective');
    }

    if (provider.averageResponseTime < 2000) {
      reasons.push('fast response time');
    }

    if (provider.reliability > 0.9) {
      reasons.push('high reliability');
    }

    if (provider.capabilities.includes(requirements.taskType)) {
      reasons.push(`optimized for ${requirements.taskType}`);
    }

    return `Selected ${provider.name} (score: ${score.toFixed(1)}) for: ${reasons.join(', ')}`;
  }

  async generateResponse(
    prompt: string,
    requirements: TaskRequirements,
    options: any = {}
  ): Promise<ProviderResponse & { selection: ProviderSelection }> {
    const selection = await this.selectOptimalProvider(requirements);
    
    try {
      const response = await this.executeWithProvider(selection.primary, prompt, options);
      this.updateUsageStats(selection.primary.id, response);
      
      return {
        ...response,
        selection
      };
    } catch (error) {
      logger.error('Primary provider failed', { 
        provider: selection.primary.name, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });

      if (selection.fallback) {
        logger.info('Attempting fallback provider', { provider: selection.fallback.name });
        try {
          const response = await this.executeWithProvider(selection.fallback, prompt, options);
          this.updateUsageStats(selection.fallback.id, response);
          
          return {
            ...response,
            selection: {
              ...selection,
              primary: selection.fallback,
              reasoning: `Fallback to ${selection.fallback.name} after primary failure`
            }
          };
        } catch (fallbackError) {
          logger.error('Fallback provider also failed', { 
            provider: selection.fallback.name, 
            error: fallbackError instanceof Error ? fallbackError.message : 'Unknown error' 
          });
        }
      }

      throw error;
    }
  }

  private async executeWithProvider(
    provider: ProviderConfig,
    prompt: string,
    options: any
  ): Promise<ProviderResponse> {
    const service = this.services.get(provider.type);
    if (!service) {
      throw new Error(`No service available for provider type: ${provider.type}`);
    }

    const model = provider.models[0]; // Use first available model
    return await service.generateResponse(model, prompt, options);
  }

  private isProviderConfigured(provider: ProviderConfig): boolean {
    const service = this.services.get(provider.type);
    return service?.isConfigured() || false;
  }

  private isRateLimited(provider: ProviderConfig, usage: any): boolean {
    const now = new Date();
    const rateLimits = provider.rateLimits;

    if (rateLimits.requestsPerMinute && usage.requestsThisMinute >= rateLimits.requestsPerMinute) {
      return true;
    }

    if (rateLimits.requestsPerDay && usage.requestsToday >= rateLimits.requestsPerDay) {
      return true;
    }

    return false;
  }

  private updateUsageStats(providerId: string, response: ProviderResponse): void {
    const now = new Date();
    const usage = this.usageStats.get(providerId) || {
      requestsToday: 0,
      requestsThisMinute: 0,
      totalCost: 0,
      lastRequest: now,
      lastMinuteReset: now
    };

    // Reset minute counter if needed
    if (now.getTime() - usage.lastMinuteReset.getTime() > 60000) {
      usage.requestsThisMinute = 0;
      usage.lastMinuteReset = now;
    }

    // Reset daily counter if needed
    if (now.toDateString() !== usage.lastRequest.toDateString()) {
      usage.requestsToday = 0;
    }

    usage.requestsToday++;
    usage.requestsThisMinute++;
    usage.totalCost += response.cost || 0;
    usage.lastRequest = now;

    this.usageStats.set(providerId, usage);
  }

  getProviderStats(): Array<{ provider: ProviderConfig; usage: any; configured: boolean }> {
    return Array.from(this.providers.values()).map(provider => ({
      provider,
      usage: this.usageStats.get(provider.id) || {},
      configured: this.isProviderConfigured(provider)
    }));
  }

  async testAllProviders(): Promise<Array<{ provider: ProviderConfig; success: boolean; error?: string }>> {
    const results = [];
    
    for (const provider of this.providers.values()) {
      if (!this.isProviderConfigured(provider)) {
        results.push({
          provider,
          success: false,
          error: 'Not configured'
        });
        continue;
      }

      try {
        await this.executeWithProvider(provider, 'Test prompt: Hello, world!', { maxTokens: 10 });
        results.push({
          provider,
          success: true
        });
      } catch (error) {
        results.push({
          provider,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return results;
  }
}

export const enhancedProviderManager = new EnhancedProviderManager();
