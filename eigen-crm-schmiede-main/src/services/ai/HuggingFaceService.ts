import { ProviderResponse } from '@/types/provider';
import { logger } from '../Logger';

export interface HuggingFaceRequest {
  inputs: string;
  parameters?: {
    max_new_tokens?: number;
    temperature?: number;
    top_p?: number;
    do_sample?: boolean;
    return_full_text?: boolean;
  };
  options?: {
    wait_for_model?: boolean;
    use_cache?: boolean;
  };
}

export interface HuggingFaceResponse {
  generated_text?: string;
  error?: string;
}

export class HuggingFaceService {
  private apiKey: string;
  private baseUrl: string = 'https://api-inference.huggingface.co/models';

  constructor() {
    this.apiKey = import.meta.env.VITE_HUGGINGFACE_API_KEY || '';
    if (!this.apiKey) {
      logger.warn('Hugging Face API key not configured');
    }
  }

  async generateResponse(
    model: string,
    prompt: string,
    options: {
      maxTokens?: number;
      temperature?: number;
      topP?: number;
    } = {}
  ): Promise<ProviderResponse> {
    const startTime = Date.now();
    
    try {
      if (!this.apiKey) {
        throw new Error('Hugging Face API key not configured');
      }

      const request: HuggingFaceRequest = {
        inputs: prompt,
        parameters: {
          max_new_tokens: options.maxTokens || 512,
          temperature: options.temperature || 0.7,
          top_p: options.topP || 0.9,
          do_sample: true,
          return_full_text: false
        },
        options: {
          wait_for_model: true,
          use_cache: false
        }
      };

      const response = await fetch(`${this.baseUrl}/${model}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Hugging Face API error: ${response.status} - ${errorText}`);
      }

      const data: HuggingFaceResponse[] = await response.json();
      
      if (!Array.isArray(data) || data.length === 0) {
        throw new Error('Invalid response format from Hugging Face API');
      }

      const result = data[0];
      
      if (result.error) {
        throw new Error(`Hugging Face model error: ${result.error}`);
      }

      const responseTime = Date.now() - startTime;
      const generatedText = result.generated_text || '';
      
      // Estimate tokens (rough approximation: 1 token ≈ 4 characters)
      const tokensUsed = Math.ceil(generatedText.length / 4);
      
      // Estimate cost (very rough - HF inference is mostly free/cheap)
      const cost = tokensUsed * 0.00001; // $0.00001 per token estimate

      logger.info('Hugging Face response generated', {
        model,
        responseTime,
        tokensUsed,
        cost
      });

      return {
        providerId: `huggingface-${model.split('/').pop()}`,
        response: generatedText,
        model,
        cost,
        responseTime,
        tokensUsed
      };

    } catch (error) {
      const responseTime = Date.now() - startTime;
      logger.error('Hugging Face API error', { error, model, responseTime });
      
      throw new Error(`Hugging Face service error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async listAvailableModels(): Promise<string[]> {
    // Common Hugging Face models for text generation
    return [
      'meta-llama/Llama-2-7b-chat-hf',
      'meta-llama/Llama-2-13b-chat-hf',
      'mistralai/Mistral-7B-Instruct-v0.1',
      'mistralai/Mixtral-8x7B-Instruct-v0.1',
      'microsoft/DialoGPT-large',
      'facebook/blenderbot-400M-distill',
      'google/flan-t5-large',
      'bigscience/bloom-560m',
      'EleutherAI/gpt-neo-2.7B'
    ];
  }

  async checkModelStatus(model: string): Promise<{
    loaded: boolean;
    estimatedTime?: number;
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/${model}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          inputs: "test",
          options: { wait_for_model: false }
        })
      });

      if (response.status === 503) {
        const data = await response.json();
        return {
          loaded: false,
          estimatedTime: data.estimated_time || 60
        };
      }

      return { loaded: true };
    } catch (error) {
      logger.error('Error checking Hugging Face model status', { error, model });
      return { loaded: false };
    }
  }

  async generateEmbedding(text: string, model: string = 'sentence-transformers/all-MiniLM-L6-v2'): Promise<number[]> {
    try {
      const response = await fetch(`${this.baseUrl}/${model}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          inputs: text,
          options: { wait_for_model: true }
        })
      });

      if (!response.ok) {
        throw new Error(`Embedding API error: ${response.status}`);
      }

      const embedding = await response.json();
      return Array.isArray(embedding) ? embedding : embedding.embeddings || [];
    } catch (error) {
      logger.error('Hugging Face embedding error', { error, model });
      throw error;
    }
  }

  isConfigured(): boolean {
    return !!this.apiKey;
  }

  getApiKeyStatus(): { configured: boolean; masked?: string } {
    if (!this.apiKey) {
      return { configured: false };
    }
    
    return {
      configured: true,
      masked: `hf_${this.apiKey.slice(3, 8)}...${this.apiKey.slice(-4)}`
    };
  }
}

export const huggingFaceService = new HuggingFaceService();
