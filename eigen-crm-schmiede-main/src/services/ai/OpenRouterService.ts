import { ProviderResponse } from '@/types/provider';
import { logger } from '../Logger';

export interface OpenRouterRequest {
  model: string;
  messages: Array<{
    role: 'system' | 'user' | 'assistant';
    content: string;
  }>;
  max_tokens?: number;
  temperature?: number;
  top_p?: number;
  frequency_penalty?: number;
  presence_penalty?: number;
  stream?: boolean;
}

export interface OpenRouterResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export class OpenRouterService {
  private apiKey: string;
  private baseUrl: string = 'https://openrouter.ai/api/v1';

  constructor() {
    this.apiKey = import.meta.env.VITE_OPENROUTER_API_KEY || '';
    if (!this.apiKey) {
      logger.warn('OpenRouter API key not configured');
    }
  }

  async generateResponse(
    model: string,
    prompt: string,
    options: {
      maxTokens?: number;
      temperature?: number;
      topP?: number;
      systemPrompt?: string;
    } = {}
  ): Promise<ProviderResponse> {
    const startTime = Date.now();
    
    try {
      if (!this.apiKey) {
        throw new Error('OpenRouter API key not configured');
      }

      const messages = [];
      
      if (options.systemPrompt) {
        messages.push({
          role: 'system' as const,
          content: options.systemPrompt
        });
      }
      
      messages.push({
        role: 'user' as const,
        content: prompt
      });

      const request: OpenRouterRequest = {
        model,
        messages,
        max_tokens: options.maxTokens || 1024,
        temperature: options.temperature || 0.7,
        top_p: options.topP || 0.9,
        stream: false
      };

      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://spqr-crm.com',
          'X-Title': 'SPQR CRM+AI System'
        },
        body: JSON.stringify(request)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`OpenRouter API error: ${response.status} - ${errorText}`);
      }

      const data: OpenRouterResponse = await response.json();
      
      if (!data.choices || data.choices.length === 0) {
        throw new Error('No response choices from OpenRouter');
      }

      const choice = data.choices[0];
      const responseTime = Date.now() - startTime;
      const generatedText = choice.message.content;
      
      // Calculate cost based on usage
      const promptTokens = data.usage.prompt_tokens;
      const completionTokens = data.usage.completion_tokens;
      const totalTokens = data.usage.total_tokens;
      
      // OpenRouter pricing varies by model - rough estimate
      const cost = this.estimateCost(model, promptTokens, completionTokens);

      logger.info('OpenRouter response generated', {
        model,
        responseTime,
        promptTokens,
        completionTokens,
        totalTokens,
        cost
      });

      return {
        providerId: `openrouter-${model.split('/').pop()}`,
        response: generatedText,
        model,
        cost,
        responseTime,
        tokensUsed: totalTokens
      };

    } catch (error) {
      const responseTime = Date.now() - startTime;
      logger.error('OpenRouter API error', { error, model, responseTime });
      
      throw new Error(`OpenRouter service error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private estimateCost(model: string, promptTokens: number, completionTokens: number): number {
    // Rough cost estimates for popular models on OpenRouter
    const costMap: Record<string, { input: number; output: number }> = {
      'anthropic/claude-3.5-sonnet': { input: 0.003, output: 0.015 },
      'anthropic/claude-3-haiku': { input: 0.00025, output: 0.00125 },
      'meta-llama/llama-3.1-70b-instruct': { input: 0.0009, output: 0.0009 },
      'meta-llama/llama-3.1-8b-instruct': { input: 0.00018, output: 0.00018 },
      'openai/gpt-4o': { input: 0.005, output: 0.015 },
      'openai/gpt-4o-mini': { input: 0.00015, output: 0.0006 },
      'google/gemini-pro-1.5': { input: 0.00125, output: 0.005 },
      'mistralai/mixtral-8x7b-instruct': { input: 0.00024, output: 0.00024 }
    };

    const pricing = costMap[model] || { input: 0.001, output: 0.002 }; // Default pricing
    
    return (promptTokens * pricing.input / 1000) + (completionTokens * pricing.output / 1000);
  }

  async listAvailableModels(): Promise<string[]> {
    try {
      const response = await fetch(`${this.baseUrl}/models`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
        }
      });
      
      if (!response.ok) {
        throw new Error(`Failed to fetch models: ${response.status}`);
      }

      const data = await response.json();
      return data.data.map((model: any) => model.id);
    } catch (error) {
      logger.error('Error fetching OpenRouter models', { error });
      // Return popular models if API call fails
      return [
        'anthropic/claude-3.5-sonnet',
        'anthropic/claude-3-haiku',
        'meta-llama/llama-3.1-70b-instruct',
        'meta-llama/llama-3.1-8b-instruct',
        'openai/gpt-4o',
        'openai/gpt-4o-mini',
        'google/gemini-pro-1.5',
        'mistralai/mixtral-8x7b-instruct'
      ];
    }
  }

  async getModelInfo(model: string): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/models/${model}`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
        }
      });
      
      if (!response.ok) {
        throw new Error(`Failed to fetch model info: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      logger.error('Error fetching OpenRouter model info', { error, model });
      return null;
    }
  }

  isConfigured(): boolean {
    return !!this.apiKey;
  }

  getApiKeyStatus(): { configured: boolean; masked?: string } {
    if (!this.apiKey) {
      return { configured: false };
    }
    
    return {
      configured: true,
      masked: `sk-or-v1-${this.apiKey.slice(9, 13)}...${this.apiKey.slice(-4)}`
    };
  }
}

export const openRouterService = new OpenRouterService();
