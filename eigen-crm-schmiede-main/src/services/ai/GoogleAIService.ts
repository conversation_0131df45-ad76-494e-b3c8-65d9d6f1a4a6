import { ProviderResponse } from '@/types/provider';
import { logger } from '../Logger';

export interface GoogleAIRequest {
  contents: Array<{
    parts: Array<{
      text: string;
    }>;
  }>;
  generationConfig?: {
    temperature?: number;
    topK?: number;
    topP?: number;
    maxOutputTokens?: number;
    stopSequences?: string[];
  };
  safetySettings?: Array<{
    category: string;
    threshold: string;
  }>;
}

export interface GoogleAIResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
    };
    finishReason: string;
    index: number;
    safetyRatings: Array<{
      category: string;
      probability: string;
    }>;
  }>;
  usageMetadata: {
    promptTokenCount: number;
    candidatesTokenCount: number;
    totalTokenCount: number;
  };
}

export class GoogleAIService {
  private apiKey: string;
  private baseUrl: string = 'https://generativelanguage.googleapis.com/v1beta';

  constructor() {
    this.apiKey = import.meta.env.VITE_GOOGLE_AI_API_KEY || '';
    if (!this.apiKey) {
      logger.warn('Google AI API key not configured');
    }
  }

  async generateResponse(
    model: string,
    prompt: string,
    options: {
      maxTokens?: number;
      temperature?: number;
      topP?: number;
      topK?: number;
    } = {}
  ): Promise<ProviderResponse> {
    const startTime = Date.now();
    
    try {
      if (!this.apiKey) {
        throw new Error('Google AI API key not configured');
      }

      const request: GoogleAIRequest = {
        contents: [
          {
            parts: [
              {
                text: prompt
              }
            ]
          }
        ],
        generationConfig: {
          temperature: options.temperature || 0.7,
          topK: options.topK || 40,
          topP: options.topP || 0.95,
          maxOutputTokens: options.maxTokens || 1024,
          stopSequences: []
        },
        safetySettings: [
          {
            category: "HARM_CATEGORY_HARASSMENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_HATE_SPEECH",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_DANGEROUS_CONTENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          }
        ]
      };

      const response = await fetch(`${this.baseUrl}/models/${model}:generateContent?key=${this.apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Google AI API error: ${response.status} - ${errorText}`);
      }

      const data: GoogleAIResponse = await response.json();
      
      if (!data.candidates || data.candidates.length === 0) {
        throw new Error('No response candidates from Google AI');
      }

      const candidate = data.candidates[0];
      
      if (candidate.finishReason !== 'STOP' && candidate.finishReason !== 'MAX_TOKENS') {
        throw new Error(`Google AI response blocked: ${candidate.finishReason}`);
      }

      const responseTime = Date.now() - startTime;
      const generatedText = candidate.content.parts[0]?.text || '';
      
      // Calculate cost (rough estimate for Gemini Pro)
      const promptTokens = data.usageMetadata.promptTokenCount;
      const completionTokens = data.usageMetadata.candidatesTokenCount;
      const totalTokens = data.usageMetadata.totalTokenCount;
      
      // Gemini Pro pricing: $0.00025 per 1K input tokens, $0.0005 per 1K output tokens
      const cost = (promptTokens * 0.00025 / 1000) + (completionTokens * 0.0005 / 1000);

      logger.info('Google AI response generated', {
        model,
        responseTime,
        promptTokens,
        completionTokens,
        totalTokens,
        cost
      });

      return {
        providerId: `google-${model}`,
        response: generatedText,
        model,
        cost,
        responseTime,
        tokensUsed: totalTokens
      };

    } catch (error) {
      const responseTime = Date.now() - startTime;
      logger.error('Google AI API error', { error, model, responseTime });
      
      throw new Error(`Google AI service error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async listAvailableModels(): Promise<string[]> {
    try {
      const response = await fetch(`${this.baseUrl}/models?key=${this.apiKey}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch models: ${response.status}`);
      }

      const data = await response.json();
      return data.models
        .filter((model: any) => model.supportedGenerationMethods?.includes('generateContent'))
        .map((model: any) => model.name.replace('models/', ''));
    } catch (error) {
      logger.error('Error fetching Google AI models', { error });
      // Return default models if API call fails
      return [
        'gemini-pro',
        'gemini-pro-vision',
        'gemini-1.5-pro',
        'gemini-1.5-flash'
      ];
    }
  }

  async generateEmbedding(text: string, model: string = 'embedding-001'): Promise<number[]> {
    try {
      const response = await fetch(`${this.baseUrl}/models/${model}:embedContent?key=${this.apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: {
            parts: [{ text }]
          }
        })
      });

      if (!response.ok) {
        throw new Error(`Embedding API error: ${response.status}`);
      }

      const data = await response.json();
      return data.embedding?.values || [];
    } catch (error) {
      logger.error('Google AI embedding error', { error, model });
      throw error;
    }
  }

  isConfigured(): boolean {
    return !!this.apiKey;
  }

  getApiKeyStatus(): { configured: boolean; masked?: string } {
    if (!this.apiKey) {
      return { configured: false };
    }
    
    return {
      configured: true,
      masked: `AIza...${this.apiKey.slice(-4)}`
    };
  }
}

export const googleAIService = new GoogleAIService();
