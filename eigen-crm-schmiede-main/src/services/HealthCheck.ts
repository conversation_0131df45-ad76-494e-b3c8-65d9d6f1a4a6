
import { supabase, isSupabaseConfigured } from '@/lib/supabase';
import { logger } from './Logger';

interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  services: {
    database: 'up' | 'down' | 'unknown';
    authentication: 'up' | 'down' | 'unknown';
    storage: 'up' | 'down' | 'unknown';
  };
  performance: {
    responseTime: number;
    memoryUsage?: number;
  };
  errors: string[];
}

class HealthCheckService {
  private lastCheck?: HealthStatus;
  private checkInterval = 60000; // 1 minute
  private intervalId?: NodeJS.Timeout;

  constructor() {
    this.startPeriodicChecks();
  }

  private startPeriodicChecks() {
    // Run initial check
    this.performHealthCheck();
    
    // Set up periodic checks
    this.intervalId = setInterval(() => {
      this.performHealthCheck();
    }, this.checkInterval);
  }

  public async performHealthCheck(): Promise<HealthStatus> {
    const startTime = Date.now();
    const errors: string[] = [];
    
    const status: HealthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        database: 'unknown',
        authentication: 'unknown',
        storage: 'unknown'
      },
      performance: {
        responseTime: 0
      },
      errors
    };

    try {
      // Check database connectivity
      status.services.database = await this.checkDatabase();
      
      // Check authentication service
      status.services.authentication = await this.checkAuthentication();
      
      // Check storage service
      status.services.storage = await this.checkStorage();
      
      // Calculate response time
      status.performance.responseTime = Date.now() - startTime;
      
      // Check memory usage if available
      if ('memory' in performance) {
        status.performance.memoryUsage = (performance as any).memory.usedJSHeapSize;
      }
      
      // Determine overall status
      const serviceStatuses = Object.values(status.services);
      if (serviceStatuses.some(s => s === 'down')) {
        status.status = serviceStatuses.every(s => s === 'down') ? 'unhealthy' : 'degraded';
      }
      
      // Check performance thresholds
      if (status.performance.responseTime > 5000) {
        status.status = 'degraded';
        errors.push('High response time detected');
      }
      
    } catch (error) {
      status.status = 'unhealthy';
      errors.push(`Health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      logger.error('Health check failed', { error: error instanceof Error ? error.message : error });
    }

    this.lastCheck = status;
    
    if (status.status !== 'healthy') {
      logger.warn('System health degraded', status);
    }
    
    return status;
  }

  private async checkDatabase(): Promise<'up' | 'down' | 'unknown'> {
    if (!isSupabaseConfigured()) {
      return 'unknown';
    }

    try {
      const { error } = await supabase.from('companies').select('count').limit(1);
      return error ? 'down' : 'up';
    } catch (error) {
      logger.error('Database health check failed', { error });
      return 'down';
    }
  }

  private async checkAuthentication(): Promise<'up' | 'down' | 'unknown'> {
    if (!isSupabaseConfigured()) {
      return 'unknown';
    }

    try {
      const { error } = await supabase.auth.getSession();
      return error ? 'down' : 'up';
    } catch (error) {
      logger.error('Authentication health check failed', { error });
      return 'down';
    }
  }

  private async checkStorage(): Promise<'up' | 'down' | 'unknown'> {
    if (!isSupabaseConfigured()) {
      return 'unknown';
    }

    try {
      const { error } = await supabase.storage.listBuckets();
      return error ? 'down' : 'up';
    } catch (error) {
      logger.error('Storage health check failed', { error });
      return 'down';
    }
  }

  public getLastHealthCheck(): HealthStatus | undefined {
    return this.lastCheck;
  }

  public stopPeriodicChecks() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = undefined;
    }
  }
}

export const healthCheck = new HealthCheckService();
