
import { supabase, isSupabaseConfigured } from '@/lib/supabase';

interface Channel {
  id: string;
  name: string;
  description?: string;
  type: 'channel' | 'direct';
  is_private: boolean;
  unread?: number;
  created_by?: string;
  created_at: string;
  updated_at: string;
}

interface Message {
  id: string;
  channel_id: string;
  sender_id: string;
  content: string;
  type: 'text' | 'file' | 'system';
  file_url?: string;
  reactions?: string[];
  sender?: string;
  avatar?: string;
  timestamp?: string;
  created_at: string;
}

interface Announcement {
  id: string;
  title: string;
  content: string;
  author_id: string;
  author?: string;
  priority: 'low' | 'medium' | 'high';
  is_pinned: boolean;
  views: number;
  reactions: number;
  comments: number;
  timestamp?: string;
  created_at: string;
  updated_at: string;
}

export class CommunicationServiceDB {
  private static isConfigured = isSupabaseConfigured();

  static async getChannels(): Promise<Channel[]> {
    if (!this.isConfigured) {
      return this.getMockChannels();
    }

    try {
      const { data, error } = await supabase
        .from('channels')
        .select('*')
        .order('name');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching channels:', error);
      return this.getMockChannels();
    }
  }

  static async getMessages(channelId: string): Promise<Message[]> {
    if (!this.isConfigured) {
      return this.getMockMessages();
    }

    try {
      const { data, error } = await supabase
        .from('messages')
        .select(`
          *,
          sender:profiles!sender_id(full_name)
        `)
        .eq('channel_id', channelId)
        .order('created_at', { ascending: true });

      if (error) throw error;
      return (data || []).map(msg => ({
        ...msg,
        sender: msg.sender?.full_name || 'Unknown User',
        avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${msg.sender_id}`,
        timestamp: msg.created_at
      }));
    } catch (error) {
      console.error('Error fetching messages:', error);
      return this.getMockMessages();
    }
  }

  static async sendMessage(channelId: string, content: string, type: 'text' | 'file' = 'text'): Promise<Message | null> {
    if (!this.isConfigured) {
      console.log('Mock: Sending message:', { channelId, content, type });
      return {
        id: `mock-${Date.now()}`,
        channel_id: channelId,
        sender_id: 'current-user',
        content,
        type,
        sender: 'Current User',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=current-user',
        timestamp: new Date().toISOString(),
        created_at: new Date().toISOString()
      };
    }

    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('messages')
        .insert({
          channel_id: channelId,
          sender_id: user.id,
          content,
          type
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error sending message:', error);
      return null;
    }
  }

  static async getAnnouncements(): Promise<Announcement[]> {
    if (!this.isConfigured) {
      return this.getMockAnnouncements();
    }

    try {
      const { data, error } = await supabase
        .from('announcements')
        .select(`
          *,
          author:profiles!author_id(full_name)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return (data || []).map(ann => ({
        ...ann,
        author: ann.author?.full_name || 'Unknown Author',
        timestamp: ann.created_at
      }));
    } catch (error) {
      console.error('Error fetching announcements:', error);
      return this.getMockAnnouncements();
    }
  }

  static async createAnnouncement(announcement: { title: string; content: string; priority: string }): Promise<Announcement | null> {
    if (!this.isConfigured) {
      console.log('Mock: Creating announcement:', announcement);
      return {
        id: `mock-${Date.now()}`,
        title: announcement.title,
        content: announcement.content,
        author_id: 'current-user',
        author: 'Current User',
        priority: announcement.priority as 'low' | 'medium' | 'high',
        is_pinned: false,
        views: 0,
        reactions: 0,
        comments: 0,
        timestamp: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
    }

    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('announcements')
        .insert({
          title: announcement.title,
          content: announcement.content,
          author_id: user.id,
          priority: announcement.priority as 'low' | 'medium' | 'high'
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating announcement:', error);
      return null;
    }
  }

  private static getMockChannels(): Channel[] {
    return [
      {
        id: 'general',
        name: 'general',
        type: 'channel',
        is_private: false,
        unread: 3,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 'project-alpha',
        name: 'project-alpha',
        type: 'channel',
        is_private: false,
        unread: 1,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];
  }

  private static getMockMessages(): Message[] {
    return [
      {
        id: '1',
        channel_id: 'general',
        sender_id: 'user-1',
        content: 'Good morning team! Ready for today\'s project kickoff?',
        type: 'text',
        sender: 'John Smith',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=john',
        timestamp: '2024-02-20T09:00:00Z',
        created_at: '2024-02-20T09:00:00Z'
      }
    ];
  }

  private static getMockAnnouncements(): Announcement[] {
    return [
      {
        id: '1',
        title: 'Safety Training Mandatory',
        content: 'All team members must complete safety training by end of month.',
        author_id: 'admin-1',
        author: 'Safety Manager',
        priority: 'high',
        is_pinned: true,
        views: 45,
        reactions: 12,
        comments: 3,
        timestamp: '2024-02-19T14:30:00Z',
        created_at: '2024-02-19T14:30:00Z',
        updated_at: '2024-02-19T14:30:00Z'
      }
    ];
  }
}
