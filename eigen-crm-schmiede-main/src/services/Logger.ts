type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: string;
  context?: Record<string, any>;
  stack?: string;
}

class Logger {
  private logs: LogEntry[] = [];
  private maxLogs = 1000;
  private isProduction = process.env.NODE_ENV === 'production';

  constructor() {
    // Capture unhandled errors
    window.addEventListener('error', (event) => {
      this.error('Unhandled error', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack
      });
    });

    // Capture unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.error('Unhandled promise rejection', {
        reason: event.reason,
        promise: event.promise
      });
    });
  }

  private log(level: LogLevel, message: string, context?: Record<string, any>) {
    const entry: LogEntry = {
      level,
      message,
      timestamp: new Date().toISOString(),
      context,
      stack: level === 'error' ? new Error().stack : undefined
    };

    this.logs.push(entry);
    
    // Keep only the last N logs
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    // Console output only in development or for errors
    if (!this.isProduction || level === 'error') {
      const consoleMethod = console[level] || console.log;
      if (context) {
        consoleMethod(`[${level.toUpperCase()}] ${message}`, context);
      } else {
        consoleMethod(`[${level.toUpperCase()}] ${message}`);
      }
    }

    // In production, send critical logs to monitoring service
    if (this.isProduction && (level === 'error' || level === 'warn')) {
      this.sendToMonitoring(entry);
    }
  }

  private sendToMonitoring(entry: LogEntry) {
    // In a real app, this would send to logging services like Sentry
    if (this.isProduction) {
      // Only log to console in production for critical issues
      console.warn('Critical issue logged:', entry);
    }
  }

  public debug(message: string, context?: Record<string, any>) {
    if (!this.isProduction) {
      this.log('debug', message, context);
    }
  }

  public info(message: string, context?: Record<string, any>) {
    this.log('info', message, context);
  }

  public warn(message: string, context?: Record<string, any>) {
    this.log('warn', message, context);
  }

  public error(message: string, context?: Record<string, any>) {
    this.log('error', message, context);
  }

  public getLogs(level?: LogLevel): LogEntry[] {
    if (level) {
      return this.logs.filter(log => log.level === level);
    }
    return [...this.logs];
  }

  public clearLogs() {
    this.logs = [];
  }

  // Helper method for service operations
  public logServiceCall(service: string, method: string, success: boolean, context?: Record<string, any>) {
    const message = `${service}.${method} ${success ? 'succeeded' : 'failed'}`;
    if (success) {
      this.info(message, context);
    } else {
      this.error(message, context);
    }
  }
}

export const logger = new Logger();
