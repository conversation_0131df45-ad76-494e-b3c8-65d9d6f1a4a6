
export interface EmailSequence {
  id: string;
  name: string;
  trigger: string;
  emails: Array<{
    id: string;
    subject: string;
    content: string;
    delay: number;
  }>;
  performance: {
    openRate: number;
    clickRate: number;
    replyRate: number;
  };
}

export interface VoiceCall {
  id: string;
  contactId: string;
  type: string;
  status: 'scheduled' | 'in-progress' | 'completed' | 'failed';
  transcript?: string;
  duration?: number;
  createdAt: string;
}

// Stub service to fix build error
export class CommunicationService {
  static getInstance() {
    return new CommunicationService();
  }
  
  async sendMessage() {
    // Stub implementation
  }

  async createEmailSequence(name: string, trigger: string): Promise<EmailSequence> {
    return {
      id: Math.random().toString(36).substr(2, 9),
      name,
      trigger,
      emails: [
        {
          id: '1',
          subject: `Welcome to ${name}`,
          content: 'Thank you for your interest...',
          delay: 0
        },
        {
          id: '2',
          subject: 'Follow up',
          content: 'Just wanted to follow up...',
          delay: 24 * 60 * 60 * 1000 // 24 hours
        }
      ],
      performance: {
        openRate: Math.random() * 50 + 25,
        clickRate: Math.random() * 20 + 5,
        replyRate: Math.random() * 15 + 2
      }
    };
  }

  async makeVoiceCall(contactId: string, type: string): Promise<VoiceCall> {
    return {
      id: Math.random().toString(36).substr(2, 9),
      contactId,
      type,
      status: 'scheduled',
      createdAt: new Date().toISOString()
    };
  }
}

export const communicationService = new CommunicationService();
