
import type { DealVelocity } from '@/types/revenue'

export class DealVelocityService {
  async analyzeDealVelocity(deals: any[]): Promise<DealVelocity[]> {
    console.log('Analyzing deal velocity with predictive models...')
    
    return deals.map(deal => {
      const stageVelocity = this.calculateStageVelocity(deal)
      const engagementVelocity = this.calculateEngagementVelocity(deal)
      const competitionFactor = this.calculateCompetitionFactor(deal)
      
      const velocity = (stageVelocity + engagementVelocity) * competitionFactor
      const predictedCloseDate = this.predictCloseDate(deal, velocity)
      
      const riskFactors = []
      const accelerationOpportunities = []
      
      if (velocity < 0.5) riskFactors.push('Slow deal progression')
      if (engagementVelocity < 0.3) riskFactors.push('Low stakeholder engagement')
      if (competitionFactor < 0.7) riskFactors.push('High competitive pressure')
      
      if (stageVelocity > 0.8) accelerationOpportunities.push('Fast stage progression')
      if (engagementVelocity > 0.7) accelerationOpportunities.push('High engagement momentum')
      
      return {
        dealId: deal.id,
        velocity: Math.round(velocity * 100),
        predictedCloseDate,
        riskFactors,
        accelerationOpportunities
      }
    })
  }

  private calculateStageVelocity(deal: any): number {
    return Math.random()
  }

  private calculateEngagementVelocity(deal: any): number {
    return Math.random()
  }

  private calculateCompetitionFactor(deal: any): number {
    return 0.7 + Math.random() * 0.3
  }

  private predictCloseDate(deal: any, velocity: number): string {
    const daysToAdd = Math.round(30 / velocity)
    const predictedDate = new Date()
    predictedDate.setDate(predictedDate.getDate() + daysToAdd)
    return predictedDate.toISOString()
  }
}
