
import { supabase, isSupabaseConfigured } from '@/lib/supabase';
import type { Database } from '@/lib/supabase';

type Tables = Database['public']['Tables'];
type Contact = Tables['contacts']['Row'];

export class ContactService {
  private static isConfigured = isSupabaseConfigured();

  static async getContacts(): Promise<Contact[]> {
    if (!this.isConfigured) {
      return this.getMockContacts();
    }

    try {
      const { data, error } = await supabase
        .from('contacts')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching contacts:', error);
      return this.getMockContacts();
    }
  }

  static async createContact(contact: Tables['contacts']['Insert']): Promise<Contact | null> {
    if (!this.isConfigured) {
      console.log('Mock: Creating contact:', contact);
      return { 
        ...contact, 
        id: `mock-${Date.now()}`, 
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      } as Contact;
    }

    try {
      const { data, error } = await supabase
        .from('contacts')
        .insert(contact)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating contact:', error);
      return null;
    }
  }

  static async updateContact(id: string, updates: Tables['contacts']['Update']): Promise<Contact | null> {
    if (!this.isConfigured) {
      console.log('Mock: Updating contact:', { id, updates });
      return { 
        ...updates, 
        id, 
        updated_at: new Date().toISOString()
      } as Contact;
    }

    try {
      const { data, error } = await supabase
        .from('contacts')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating contact:', error);
      return null;
    }
  }

  static async deleteContact(id: string): Promise<boolean> {
    if (!this.isConfigured) {
      console.log('Mock: Deleting contact:', id);
      return true;
    }

    try {
      const { error } = await supabase
        .from('contacts')
        .delete()
        .eq('id', id);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error deleting contact:', error);
      return false;
    }
  }

  private static getMockContacts(): Contact[] {
    return [
      {
        id: 'mock-1',
        first_name: 'John',
        last_name: 'Smith',
        email: '<EMAIL>',
        phone: '******-0123',
        position: 'Project Manager',
        company_id: 'mock-1',
        type: 'client',
        status: 'active',
        notes: 'Experienced project manager with 10+ years in construction',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];
  }
}
