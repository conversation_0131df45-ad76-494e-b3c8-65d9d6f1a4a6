
import { supabase } from '@/lib/supabase'
import type { 
  UnifiedProject, 
  UnifiedTask, 
  UnifiedAgent, 
  UnifiedServiceResponse 
} from '@/types/unified'

export class UnifiedDatabaseService {
  private static instance: UnifiedDatabaseService

  static getInstance(): UnifiedDatabaseService {
    if (!UnifiedDatabaseService.instance) {
      UnifiedDatabaseService.instance = new UnifiedDatabaseService()
    }
    return UnifiedDatabaseService.instance
  }

  // Projects
  async getProjects(): Promise<UnifiedServiceResponse<UnifiedProject[]>> {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select(`
          *,
          company:companies(name),
          project_manager:profiles!project_manager_id(full_name)
        `)
        .order('created_at', { ascending: false })

      if (error) throw error

      return {
        success: true,
        data: data || []
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch projects'
      }
    }
  }

  async createProject(project: Omit<UnifiedProject, 'id' | 'createdAt' | 'updatedAt'>): Promise<UnifiedServiceResponse<UnifiedProject>> {
    try {
      const { data, error } = await supabase
        .from('projects')
        .insert({
          ...project,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single()

      if (error) throw error

      return {
        success: true,
        data
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create project'
      }
    }
  }

  async updateProject(id: string, updates: Partial<UnifiedProject>): Promise<UnifiedServiceResponse<UnifiedProject>> {
    try {
      const { data, error } = await supabase
        .from('projects')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single()

      if (error) throw error

      return {
        success: true,
        data
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update project'
      }
    }
  }

  // Tasks
  async getTasks(projectId?: string): Promise<UnifiedServiceResponse<UnifiedTask[]>> {
    try {
      let query = supabase
        .from('tasks')
        .select(`
          *,
          project:projects(name),
          assigned_to:profiles!assigned_to(full_name)
        `)

      if (projectId) {
        query = query.eq('project_id', projectId)
      }

      const { data, error } = await query.order('created_at', { ascending: false })

      if (error) throw error

      return {
        success: true,
        data: data || []
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch tasks'
      }
    }
  }

  async createTask(task: Omit<UnifiedTask, 'id' | 'createdAt' | 'updatedAt'>): Promise<UnifiedServiceResponse<UnifiedTask>> {
    try {
      const { data, error } = await supabase
        .from('tasks')
        .insert({
          ...task,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single()

      if (error) throw error

      return {
        success: true,
        data
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create task'
      }
    }
  }

  // Agents
  async getAgents(): Promise<UnifiedServiceResponse<UnifiedAgent[]>> {
    try {
      const { data, error } = await supabase
        .from('ai_agents')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error

      return {
        success: true,
        data: data || []
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch agents'
      }
    }
  }

  async updateAgentStatus(id: string, status: string): Promise<UnifiedServiceResponse<UnifiedAgent>> {
    try {
      const { data, error } = await supabase
        .from('ai_agents')
        .update({
          status,
          last_activity: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single()

      if (error) throw error

      return {
        success: true,
        data
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update agent status'
      }
    }
  }

  // Opportunities
  async getOpportunities(): Promise<UnifiedServiceResponse<any[]>> {
    try {
      const { data, error } = await supabase
        .from('opportunities')
        .select(`
          *,
          company:companies(name),
          contact:contacts(first_name, last_name)
        `)
        .order('created_at', { ascending: false })

      if (error) throw error

      return {
        success: true,
        data: data || []
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch opportunities'
      }
    }
  }

  // Companies
  async getCompanies(): Promise<UnifiedServiceResponse<any[]>> {
    try {
      const { data, error } = await supabase
        .from('companies')
        .select('*')
        .order('name')

      if (error) throw error

      return {
        success: true,
        data: data || []
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch companies'
      }
    }
  }

  // Contacts
  async getContacts(): Promise<UnifiedServiceResponse<any[]>> {
    try {
      const { data, error } = await supabase
        .from('contacts')
        .select(`
          *,
          company:companies(name)
        `)
        .order('created_at', { ascending: false })

      if (error) throw error

      return {
        success: true,
        data: data || []
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch contacts'
      }
    }
  }

  // Activity Feed
  async getActivityFeed(limit = 10): Promise<UnifiedServiceResponse<any[]>> {
    try {
      const { data, error } = await supabase
        .from('activity_feed')
        .select(`
          *,
          project:projects(name),
          user:profiles!user_id(full_name)
        `)
        .order('created_at', { ascending: false })
        .limit(limit)

      if (error) throw error

      return {
        success: true,
        data: data || []
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch activity feed'
      }
    }
  }

  async createActivityEntry(entry: {
    type: string
    title: string
    description: string
    status?: string
    value?: string
    project_id?: string
    user_id?: string
  }): Promise<UnifiedServiceResponse<any>> {
    try {
      const { data, error } = await supabase
        .from('activity_feed')
        .insert(entry)
        .select()
        .single()

      if (error) throw error

      return {
        success: true,
        data
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create activity entry'
      }
    }
  }

  // User Profile
  async getCurrentUserProfile(): Promise<UnifiedServiceResponse<any>> {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        return {
          success: false,
          error: 'No authenticated user'
        }
      }

      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      if (error && error.code !== 'PGRST116') throw error

      return {
        success: true,
        data
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch user profile'
      }
    }
  }
}

export const unifiedDatabase = UnifiedDatabaseService.getInstance()
