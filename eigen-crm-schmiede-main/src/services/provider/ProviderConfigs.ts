
import { ProviderConfig } from '@/types/provider';

export const defaultProviderConfigs: ProviderConfig[] = [
  // Local Ollama Providers (Highest Privacy)
  {
    id: 'ollama-llama31',
    name: 'Ollama Llama 3.1 8B',
    type: 'ollama',
    baseUrl: 'http://localhost:11434',
    models: ['llama3.1:8b', 'llama3.1:latest'],
    isLocal: true,
    privacyScore: 10,
    costPerRequest: 0,
    rateLimits: {
      requestsPerMinute: 60,
      requestsPerDay: 10000
    },
    capabilities: ['conversation', 'analysis', 'research', 'creative'],
    averageResponseTime: 2000,
    reliability: 0.95
  },
  {
    id: 'ollama-codellama',
    name: 'Ollama CodeLlama',
    type: 'ollama',
    baseUrl: 'http://localhost:11434',
    models: ['codellama', 'codellama:7b', 'codellama:13b'],
    isLocal: true,
    privacyScore: 10,
    costPerRequest: 0,
    rateLimits: {
      requestsPerMinute: 60,
      requestsPerDay: 10000
    },
    capabilities: ['code_generation'],
    averageResponseTime: 2500,
    reliability: 0.9
  },
  {
    id: 'openai-gpt4o-mini',
    name: 'OpenAI GPT-4o Mini',
    type: 'openai',
    models: ['gpt-4o-mini'],
    isLocal: false,
    privacyScore: 6,
    costPerRequest: 0.5,
    rateLimits: {
      requestsPerMinute: 500,
      requestsPerDay: 10000,
      tokensPerMinute: 200000,
      tokensPerDay: 1000000
    },
    capabilities: ['conversation', 'analysis', 'creative', 'code_generation'],
    averageResponseTime: 1200,
    reliability: 0.99
  },
  {
    id: 'openai-gpt4o',
    name: 'OpenAI GPT-4o',
    type: 'openai',
    models: ['gpt-4o'],
    isLocal: false,
    privacyScore: 6,
    costPerRequest: 2.5,
    rateLimits: {
      requestsPerMinute: 500,
      requestsPerDay: 10000,
      tokensPerMinute: 30000,
      tokensPerDay: 300000
    },
    capabilities: ['conversation', 'analysis', 'creative', 'code_generation', 'research'],
    averageResponseTime: 1500,
    reliability: 0.99
  },
  {
    id: 'perplexity-sonar',
    name: 'Perplexity Sonar',
    type: 'perplexity',
    models: ['llama-3.1-sonar-small-128k-online'],
    isLocal: false,
    privacyScore: 7,
    costPerRequest: 1.0,
    rateLimits: {
      requestsPerMinute: 20,
      requestsPerDay: 1000
    },
    capabilities: ['research', 'analysis'],
    averageResponseTime: 2000,
    reliability: 0.95
  },

  // Hugging Face Providers (Open Source Models)
  {
    id: 'huggingface-llama2',
    name: 'Hugging Face Llama 2 7B',
    type: 'huggingface',
    baseUrl: 'https://api-inference.huggingface.co',
    models: ['meta-llama/Llama-2-7b-chat-hf', 'meta-llama/Llama-2-13b-chat-hf'],
    isLocal: false,
    privacyScore: 8,
    costPerRequest: 0.1,
    rateLimits: {
      requestsPerMinute: 30,
      requestsPerDay: 1000
    },
    capabilities: ['conversation', 'analysis'],
    averageResponseTime: 3000,
    reliability: 0.85
  },
  {
    id: 'huggingface-mistral',
    name: 'Hugging Face Mistral 7B',
    type: 'huggingface',
    baseUrl: 'https://api-inference.huggingface.co',
    models: ['mistralai/Mistral-7B-Instruct-v0.1', 'mistralai/Mixtral-8x7B-Instruct-v0.1'],
    isLocal: false,
    privacyScore: 8,
    costPerRequest: 0.15,
    rateLimits: {
      requestsPerMinute: 30,
      requestsPerDay: 1000
    },
    capabilities: ['conversation', 'analysis', 'code_generation'],
    averageResponseTime: 2500,
    reliability: 0.88
  },

  // Google AI Studio
  {
    id: 'google-gemini-pro',
    name: 'Google Gemini Pro',
    type: 'google',
    baseUrl: 'https://generativelanguage.googleapis.com',
    models: ['gemini-pro', 'gemini-pro-vision'],
    isLocal: false,
    privacyScore: 6,
    costPerRequest: 0.5,
    rateLimits: {
      requestsPerMinute: 60,
      requestsPerDay: 1500,
      tokensPerMinute: 1000000,
      tokensPerDay: 5000000
    },
    capabilities: ['conversation', 'analysis', 'creative', 'research'],
    averageResponseTime: 1800,
    reliability: 0.96
  },

  // OpenRouter (Multi-Model Gateway)
  {
    id: 'openrouter-claude',
    name: 'OpenRouter Claude 3.5 Sonnet',
    type: 'openrouter',
    baseUrl: 'https://openrouter.ai/api/v1',
    models: ['anthropic/claude-3.5-sonnet', 'anthropic/claude-3-haiku'],
    isLocal: false,
    privacyScore: 7,
    costPerRequest: 1.5,
    rateLimits: {
      requestsPerMinute: 20,
      requestsPerDay: 1000
    },
    capabilities: ['conversation', 'analysis', 'creative', 'code_generation'],
    averageResponseTime: 2200,
    reliability: 0.94
  },
  {
    id: 'openrouter-llama',
    name: 'OpenRouter Llama 3.1 70B',
    type: 'openrouter',
    baseUrl: 'https://openrouter.ai/api/v1',
    models: ['meta-llama/llama-3.1-70b-instruct', 'meta-llama/llama-3.1-8b-instruct'],
    isLocal: false,
    privacyScore: 7,
    costPerRequest: 0.8,
    rateLimits: {
      requestsPerMinute: 20,
      requestsPerDay: 1000
    },
    capabilities: ['conversation', 'analysis', 'research'],
    averageResponseTime: 2500,
    reliability: 0.92
  },

  // NVIDIA NIM (High Performance)
  {
    id: 'nvidia-nim-llama',
    name: 'NVIDIA NIM Llama 3.1',
    type: 'nvidia',
    baseUrl: 'https://integrate.api.nvidia.com/v1',
    models: ['meta/llama-3.1-8b-instruct', 'meta/llama-3.1-70b-instruct'],
    isLocal: false,
    privacyScore: 7,
    costPerRequest: 0.3,
    rateLimits: {
      requestsPerMinute: 40,
      requestsPerDay: 2000
    },
    capabilities: ['conversation', 'analysis', 'code_generation'],
    averageResponseTime: 1500,
    reliability: 0.93
  },

  // Mistral AI
  {
    id: 'mistral-small',
    name: 'Mistral Small',
    type: 'mistral',
    baseUrl: 'https://api.mistral.ai/v1',
    models: ['mistral-small-latest', 'mistral-medium-latest'],
    isLocal: false,
    privacyScore: 7,
    costPerRequest: 0.6,
    rateLimits: {
      requestsPerMinute: 60,
      requestsPerDay: 2000,
      tokensPerMinute: 500000,
      tokensPerDay: 1000000000
    },
    capabilities: ['conversation', 'analysis', 'creative'],
    averageResponseTime: 1800,
    reliability: 0.94
  },
  {
    id: 'mistral-large',
    name: 'Mistral Large',
    type: 'mistral',
    baseUrl: 'https://api.mistral.ai/v1',
    models: ['mistral-large-latest'],
    isLocal: false,
    privacyScore: 7,
    costPerRequest: 2.0,
    rateLimits: {
      requestsPerMinute: 60,
      requestsPerDay: 2000,
      tokensPerMinute: 500000,
      tokensPerDay: 1000000000
    },
    capabilities: ['conversation', 'analysis', 'creative', 'code_generation', 'research'],
    averageResponseTime: 2000,
    reliability: 0.96
  },

  // Cohere
  {
    id: 'cohere-command',
    name: 'Cohere Command R+',
    type: 'cohere',
    baseUrl: 'https://api.cohere.ai/v1',
    models: ['command-r-plus', 'command-r', 'command'],
    isLocal: false,
    privacyScore: 7,
    costPerRequest: 1.2,
    rateLimits: {
      requestsPerMinute: 20,
      requestsPerDay: 1000
    },
    capabilities: ['conversation', 'analysis', 'research'],
    averageResponseTime: 2200,
    reliability: 0.91
  },

  // Cloudflare Workers AI
  {
    id: 'cloudflare-llama',
    name: 'Cloudflare Llama 3.1',
    type: 'cloudflare',
    baseUrl: 'https://api.cloudflare.com/client/v4/accounts/YOUR_ACCOUNT_ID/ai/run',
    models: ['@cf/meta/llama-3.1-8b-instruct', '@cf/meta/llama-3.1-70b-instruct'],
    isLocal: false,
    privacyScore: 8,
    costPerRequest: 0.05,
    rateLimits: {
      requestsPerMinute: 100,
      requestsPerDay: 10000
    },
    capabilities: ['conversation', 'analysis'],
    averageResponseTime: 2800,
    reliability: 0.87
  },
  {
    id: 'huggingface-free',
    name: 'HuggingFace Free Models',
    type: 'huggingface',
    models: ['microsoft/DialoGPT-medium', 'facebook/blenderbot-400M-distill'],
    isLocal: false,
    privacyScore: 8,
    costPerRequest: 0,
    rateLimits: {
      requestsPerMinute: 30,
      requestsPerDay: 1000
    },
    capabilities: ['conversation'],
    averageResponseTime: 3000,
    reliability: 0.85
  }
];

export class ProviderConfigManager {
  private configs: Map<string, ProviderConfig> = new Map();
  private storageKey = 'ai_provider_configs';

  constructor() {
    this.loadConfigs();
  }

  private loadConfigs(): void {
    try {
      const stored = localStorage.getItem(this.storageKey);
      if (stored) {
        const data = JSON.parse(stored);
        Object.entries(data).forEach(([key, value]) => {
          this.configs.set(key, value as ProviderConfig);
        });
      } else {
        // Initialize with defaults
        defaultProviderConfigs.forEach(config => {
          this.configs.set(config.id, config);
        });
        this.saveConfigs();
      }
    } catch (error) {
      console.warn('Failed to load provider configs:', error);
      // Fallback to defaults
      defaultProviderConfigs.forEach(config => {
        this.configs.set(config.id, config);
      });
    }
  }

  private saveConfigs(): void {
    try {
      const data = Object.fromEntries(this.configs);
      localStorage.setItem(this.storageKey, JSON.stringify(data));
    } catch (error) {
      console.warn('Failed to save provider configs:', error);
    }
  }

  getAllConfigs(): ProviderConfig[] {
    return Array.from(this.configs.values());
  }

  getConfig(providerId: string): ProviderConfig | undefined {
    return this.configs.get(providerId);
  }

  updateConfig(config: ProviderConfig): void {
    this.configs.set(config.id, config);
    this.saveConfigs();
  }

  removeConfig(providerId: string): void {
    this.configs.delete(providerId);
    this.saveConfigs();
  }

  getConfigsByCapability(capability: string): ProviderConfig[] {
    return Array.from(this.configs.values())
      .filter(config => config.capabilities.includes(capability as any));
  }

  getAvailableConfigs(): ProviderConfig[] {
    // In a real implementation, this would check actual availability
    return this.getAllConfigs();
  }
}
