
import { supabase, isSupabaseConfigured } from '@/lib/supabase';
import type { Database } from '@/lib/supabase';

interface SafetyReport {
  id: string;
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'open' | 'investigating' | 'resolved' | 'closed';
  location?: string;
  reported_by: string;
  assigned_to?: string;
  project_id?: string;
  incident_date: string;
  photos?: string[];
  actions_taken?: string;
  created_at: string;
  updated_at: string;
}

interface SafetyInspection {
  id: string;
  inspector_name: string;
  inspection_date: string;
  location: string;
  type: 'routine' | 'incident' | 'compliance';
  checklist_items: any[];
  passed: boolean;
  notes?: string;
  project_id?: string;
  created_at: string;
}

export class SafetyService {
  private static isConfigured = isSupabaseConfigured();

  static async getSafetyReports(projectId?: string): Promise<SafetyReport[]> {
    if (!this.isConfigured) {
      return this.getMockSafetyReports();
    }

    try {
      let query = supabase.from('safety_reports').select('*');
      
      if (projectId) {
        query = query.eq('project_id', projectId);
      }

      const { data, error } = await query.order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching safety reports:', error);
      return this.getMockSafetyReports();
    }
  }

  static async createSafetyReport(report: Omit<SafetyReport, 'id' | 'created_at' | 'updated_at'>): Promise<SafetyReport | null> {
    if (!this.isConfigured) {
      console.log('Mock: Creating safety report:', report);
      return { 
        ...report, 
        id: `mock-${Date.now()}`, 
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      } as SafetyReport;
    }

    try {
      const { data, error } = await supabase
        .from('safety_reports')
        .insert(report)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating safety report:', error);
      return null;
    }
  }

  static async updateSafetyReport(id: string, updates: Partial<SafetyReport>): Promise<SafetyReport | null> {
    if (!this.isConfigured) {
      console.log('Mock: Updating safety report:', { id, updates });
      return { 
        ...updates, 
        id, 
        updated_at: new Date().toISOString()
      } as SafetyReport;
    }

    try {
      const { data, error } = await supabase
        .from('safety_reports')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating safety report:', error);
      return null;
    }
  }

  static async getSafetyInspections(projectId?: string): Promise<SafetyInspection[]> {
    if (!this.isConfigured) {
      return this.getMockInspections();
    }

    try {
      let query = supabase.from('safety_inspections').select('*');
      
      if (projectId) {
        query = query.eq('project_id', projectId);
      }

      const { data, error } = await query.order('inspection_date', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching safety inspections:', error);
      return this.getMockInspections();
    }
  }

  private static getMockSafetyReports(): SafetyReport[] {
    return [
      {
        id: 'safety-1',
        title: 'Near Miss - Falling Object',
        description: 'Tool fell from scaffolding, narrowly missing worker below',
        severity: 'high',
        status: 'investigating',
        location: 'Building A - Floor 3',
        reported_by: 'John Smith',
        assigned_to: 'Safety Manager',
        project_id: 'project-1',
        incident_date: '2024-02-20T14:30:00Z',
        photos: [],
        actions_taken: 'Area cordoned off, safety briefing scheduled',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];
  }

  private static getMockInspections(): SafetyInspection[] {
    return [
      {
        id: 'inspection-1',
        inspector_name: 'Safety Officer Smith',
        inspection_date: '2024-02-20',
        location: 'Main Construction Site',
        type: 'routine',
        checklist_items: [
          { item: 'PPE Compliance', status: 'pass' },
          { item: 'Equipment Safety', status: 'pass' },
          { item: 'Site Cleanliness', status: 'fail', notes: 'Debris in walkway' }
        ],
        passed: false,
        notes: 'Overall good, minor cleanup needed',
        project_id: 'project-1',
        created_at: new Date().toISOString()
      }
    ];
  }
}
