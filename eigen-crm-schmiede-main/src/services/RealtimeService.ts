
import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { supabase, isSupabaseConfigured } from '@/lib/supabase';

interface RealtimeEvent {
  eventType: 'INSERT' | 'UPDATE' | 'DELETE';
  table: string;
  new?: any;
  old?: any;
}

export class RealtimeService {
  private static instance: RealtimeService;
  private subscriptions: Map<string, any> = new Map();
  private listeners: Map<string, ((event: RealtimeEvent) => void)[]> = new Map();

  static getInstance(): RealtimeService {
    if (!this.instance) {
      this.instance = new RealtimeService();
    }
    return this.instance;
  }

  async connect() {
    if (!isSupabaseConfigured()) {
      console.log('Supabase not configured, using mock real-time');
      this.simulateRealtimeEvents();
      return;
    }

    this.setupTableSubscriptions();
    console.log('RealTimeService connected to Supabase');
  }

  private setupTableSubscriptions() {
    const tables = [
      'companies', 'contacts', 'opportunities', 'projects', 'tasks',
      'vehicles', 'maintenance_records', 'channels', 'messages', 'announcements',
      'shipments', 'suppliers', 'safety_reports', 'quality_inspections',
      'employees', 'trainings'
    ];

    tables.forEach(table => {
      const subscription = supabase
        .channel(`public:${table}`)
        .on('postgres_changes', { event: '*', schema: 'public', table }, (payload) => {
          this.notifyListeners(table, {
            eventType: payload.eventType as 'INSERT' | 'UPDATE' | 'DELETE',
            table,
            new: payload.new,
            old: payload.old
          });
        })
        .subscribe();

      this.subscriptions.set(table, subscription);
    });
  }

  private simulateRealtimeEvents() {
    // Simulate real-time events for demo purposes
    setInterval(() => {
      const mockEvents = [
        { table: 'messages', eventType: 'INSERT' as const, new: { id: 'new-msg', content: 'New message' } },
        { table: 'vehicles', eventType: 'UPDATE' as const, new: { id: 'vehicle-1', status: 'available' } },
        { table: 'safety_reports', eventType: 'INSERT' as const, new: { id: 'safety-new', title: 'New safety report' } }
      ];

      const randomEvent = mockEvents[Math.floor(Math.random() * mockEvents.length)];
      this.notifyListeners(randomEvent.table, randomEvent);
    }, 30000); // Every 30 seconds
  }

  subscribe(table: string, callback: (event: RealtimeEvent) => void): () => void {
    if (!this.listeners.has(table)) {
      this.listeners.set(table, []);
    }
    this.listeners.get(table)!.push(callback);

    return () => {
      const callbacks = this.listeners.get(table) || [];
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    };
  }

  private notifyListeners(table: string, event: RealtimeEvent) {
    const callbacks = this.listeners.get(table) || [];
    callbacks.forEach(callback => {
      try {
        callback(event);
      } catch (error) {
        console.error(`Error in realtime listener for ${table}:`, error);
      }
    });
  }

  async disconnect() {
    this.subscriptions.forEach(subscription => {
      subscription.unsubscribe();
    });
    this.subscriptions.clear();
    this.listeners.clear();
    console.log('RealTimeService disconnected');
  }

  isConnected(): boolean {
    return this.subscriptions.size > 0;
  }

  send(message: any) {
    // For WebSocket-like functionality if needed
    console.log('Sending message via realtime:', message);
    return Promise.resolve();
  }
}

export const realTimeService = RealtimeService.getInstance();

// React hook for using real-time updates
export const useRealtimeUpdates = (tables: string[]) => {
  const queryClient = useQueryClient();

  useEffect(() => {
    const unsubscribers = tables.map(table => 
      realTimeService.subscribe(table, (event) => {
        console.log(`Real-time ${event.eventType} event for ${table}:`, event);
        
        // Invalidate relevant queries based on table
        switch (table) {
          case 'companies':
            queryClient.invalidateQueries({ queryKey: ['companies'] });
            break;
          case 'contacts':
            queryClient.invalidateQueries({ queryKey: ['contacts'] });
            break;
          case 'opportunities':
            queryClient.invalidateQueries({ queryKey: ['opportunities'] });
            break;
          case 'projects':
            queryClient.invalidateQueries({ queryKey: ['projects'] });
            break;
          case 'tasks':
            queryClient.invalidateQueries({ queryKey: ['tasks'] });
            break;
          case 'vehicles':
            queryClient.invalidateQueries({ queryKey: ['vehicles'] });
            break;
          case 'messages':
            queryClient.invalidateQueries({ queryKey: ['messages'] });
            break;
          case 'announcements':
            queryClient.invalidateQueries({ queryKey: ['announcements'] });
            break;
          case 'safety_reports':
            queryClient.invalidateQueries({ queryKey: ['safety_reports'] });
            break;
          case 'employees':
            queryClient.invalidateQueries({ queryKey: ['employees'] });
            break;
          default:
            queryClient.invalidateQueries({ queryKey: [table] });
        }
      })
    );

    return () => {
      unsubscribers.forEach(unsubscribe => unsubscribe());
    };
  }, [tables, queryClient]);

  useEffect(() => {
    const connectAndCleanup = async () => {
      await realTimeService.connect();
      return () => realTimeService.disconnect();
    };
    
    let cleanup: (() => void) | undefined;
    connectAndCleanup().then(cleanupFn => {
      cleanup = cleanupFn;
    });

    return () => {
      if (cleanup) cleanup();
    };
  }, []);
};
