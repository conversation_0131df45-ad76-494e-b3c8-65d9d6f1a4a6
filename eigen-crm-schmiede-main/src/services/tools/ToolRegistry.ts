
import { BaseTool } from './BaseTool'
import type { ToolDefinition, ToolExecution, ToolExecutionRequest, ToolExecutionResult } from '@/types/tools'

export class ToolRegistry {
  private tools: Map<string, BaseTool> = new Map()
  private executions: ToolExecution[] = []
  private listeners: Map<string, (execution: ToolExecution) => void> = new Map()

  registerTool(tool: BaseTool): void {
    const definition = tool.getDefinition()
    this.tools.set(definition.id, tool)
    console.log(`Tool registered: ${definition.name} (${definition.id})`)
  }

  unregisterTool(toolId: string): boolean {
    const removed = this.tools.delete(toolId)
    if (removed) {
      console.log(`Tool unregistered: ${toolId}`)
    }
    return removed
  }

  getTool(toolId: string): BaseTool | undefined {
    return this.tools.get(toolId)
  }

  getAllTools(): ToolDefinition[] {
    return Array.from(this.tools.values()).map(tool => tool.getDefinition())
  }

  getToolsByCategory(category: string): ToolDefinition[] {
    return this.getAllTools().filter(tool => tool.category === category)
  }

  async executeTool(request: ToolExecutionRequest): Promise<ToolExecutionResult> {
    const tool = this.getTool(request.toolId)
    if (!tool) {
      throw new Error(`Tool not found: ${request.toolId}`)
    }

    const execution: ToolExecution = {
      id: Date.now().toString(),
      toolId: request.toolId,
      agentId: request.agentId,
      parameters: request.parameters,
      timestamp: new Date().toISOString(),
      status: 'pending'
    }

    this.executions.push(execution)
    this.notifyListeners(execution)

    try {
      execution.status = 'running'
      this.notifyListeners(execution)

      const startTime = Date.now()
      const result = await tool.execute(request)
      const duration = Date.now() - startTime

      execution.status = result.success ? 'success' : 'error'
      execution.result = result.data
      execution.error = result.error
      execution.duration = duration

      this.notifyListeners(execution)
      return result
    } catch (error) {
      execution.status = 'error'
      execution.error = error instanceof Error ? error.message : 'Unknown error'
      execution.duration = Date.now() - new Date(execution.timestamp).getTime()
      
      this.notifyListeners(execution)
      throw error
    }
  }

  getExecutions(limit?: number): ToolExecution[] {
    const sorted = this.executions.sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    )
    return limit ? sorted.slice(0, limit) : sorted
  }

  onExecution(listenerId: string, callback: (execution: ToolExecution) => void): void {
    this.listeners.set(listenerId, callback)
  }

  offExecution(listenerId: string): void {
    this.listeners.delete(listenerId)
  }

  private notifyListeners(execution: ToolExecution): void {
    this.listeners.forEach(callback => callback(execution))
  }
}

export const toolRegistry = new ToolRegistry()
