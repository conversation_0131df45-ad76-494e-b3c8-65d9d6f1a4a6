
import { BaseTool } from '../BaseTool'
import type { ToolDefinition, ToolExecutionRequest, ToolExecutionResult } from '@/types/tools'

export class N8nWebhookTool extends BaseTool {
  constructor(id: string, name: string, webhookUrl: string) {
    const definition: ToolDefinition = {
      id,
      name,
      description: 'Execute n8n workflow via webhook',
      category: 'workflow',
      version: '1.0.0',
      parameters: [
        {
          name: 'data',
          type: 'object',
          required: true,
          description: 'Data to send to the n8n workflow'
        },
        {
          name: 'wait_for_response',
          type: 'boolean',
          required: false,
          default: false,
          description: 'Whether to wait for workflow response'
        }
      ],
      status: 'active',
      usageCount: 0,
      configuration: { webhookUrl }
    }
    
    super(definition)
  }

  async execute(request: ToolExecutionRequest): Promise<ToolExecutionResult> {
    const startTime = Date.now()
    const errors = this.validateParameters(request.parameters)
    
    if (errors.length > 0) {
      return {
        success: false,
        error: errors.join(', '),
        duration: Date.now() - startTime
      }
    }

    try {
      const webhookUrl = this.definition.configuration?.webhookUrl
      if (!webhookUrl) {
        throw new Error('Webhook URL not configured')
      }

      const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...request.parameters.data,
          agentId: request.agentId,
          timestamp: new Date().toISOString()
        })
      })

      let result
      if (request.parameters.wait_for_response) {
        result = await response.json()
      } else {
        result = { status: 'triggered', statusCode: response.status }
      }

      this.definition.usageCount++
      this.definition.lastUsed = new Date().toISOString()

      return {
        success: response.ok,
        data: result,
        duration: Date.now() - startTime,
        metadata: { 
          statusCode: response.status,
          webhookUrl: webhookUrl 
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        duration: Date.now() - startTime
      }
    }
  }

  async validate(parameters: Record<string, any>): Promise<boolean> {
    const errors = this.validateParameters(parameters)
    return errors.length === 0
  }

  async configure(config: Record<string, any>): Promise<boolean> {
    if (!config.webhookUrl || typeof config.webhookUrl !== 'string') {
      return false
    }
    
    this.definition.configuration = { ...this.definition.configuration, ...config }
    return true
  }
}
