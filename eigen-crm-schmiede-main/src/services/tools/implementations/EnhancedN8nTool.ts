import { BaseTool } from '../BaseTool'
import type { ToolDefinition, ToolExecutionRequest, ToolExecutionResult, ToolParameter } from '@/types/tools'

export interface N8nToolConfig {
  webhookUrl: string
  name: string
  description: string
  context: string
  parameters: Array<{
    name: string
    type: 'string' | 'number' | 'boolean' | 'object' | 'array'
    required: boolean
    description: string
    default?: any
  }>
  authentication?: {
    type: 'api_key' | 'bearer' | 'basic'
    key?: string
    username?: string
    password?: string
  }
}

export class EnhancedN8nTool extends BaseTool {
  private config: N8nToolConfig

  constructor(id: string, config: N8nToolConfig) {
    const definition: ToolDefinition = {
      id,
      name: config.name,
      description: config.description,
      category: 'workflow',
      version: '2.0.0',
      parameters: config.parameters as ToolParameter[],
      status: 'active',
      usageCount: 0,
      configuration: {
        webhookUrl: config.webhookUrl,
        context: config.context,
        authentication: config.authentication
      }
    }
    
    super(definition)
    this.config = config
  }

  async validate(parameters: Record<string, any>): Promise<boolean> {
    const errors = this.validateParameters(parameters)
    return errors.length === 0
  }

  async configure(config: Record<string, any>): Promise<boolean> {
    try {
      if (config.webhookUrl) {
        this.config.webhookUrl = config.webhookUrl
        this.definition.configuration!.webhookUrl = config.webhookUrl
      }
      if (config.context) {
        this.config.context = config.context
        this.definition.configuration!.context = config.context
      }
      if (config.authentication) {
        this.config.authentication = config.authentication
        this.definition.configuration!.authentication = config.authentication
      }
      return true
    } catch (error) {
      console.error('Failed to configure N8n tool:', error)
      return false
    }
  }

  async execute(request: ToolExecutionRequest): Promise<ToolExecutionResult> {
    const startTime = Date.now()
    const errors = this.validateParameters(request.parameters)
    
    if (errors.length > 0) {
      return {
        success: false,
        error: errors.join(', '),
        duration: Date.now() - startTime
      }
    }

    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }

      // Add authentication headers if configured
      if (this.config.authentication) {
        switch (this.config.authentication.type) {
          case 'api_key':
            headers['X-API-Key'] = this.config.authentication.key || ''
            break
          case 'bearer':
            headers['Authorization'] = `Bearer ${this.config.authentication.key || ''}`
            break
          case 'basic':
            const credentials = btoa(`${this.config.authentication.username}:${this.config.authentication.password}`)
            headers['Authorization'] = `Basic ${credentials}`
            break
        }
      }

      const payload = {
        ...request.parameters,
        agentId: request.agentId,
        timestamp: new Date().toISOString(),
        context: this.config.context,
        toolMetadata: {
          toolId: this.definition.id,
          toolName: this.definition.name,
          version: this.definition.version
        }
      }

      const response = await fetch(this.config.webhookUrl, {
        method: 'POST',
        headers,
        body: JSON.stringify(payload)
      })

      let result
      try {
        result = await response.json()
      } catch {
        result = { 
          status: response.ok ? 'success' : 'error',
          statusCode: response.status,
          statusText: response.statusText
        }
      }

      this.definition.usageCount++
      this.definition.lastUsed = new Date().toISOString()

      return {
        success: response.ok,
        data: result,
        duration: Date.now() - startTime,
        metadata: { 
          statusCode: response.status,
          responseHeaders: Object.fromEntries(response.headers.entries())
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error occurred',
        duration: Date.now() - startTime
      }
    }
  }

  async discoverSchema(): Promise<any> {
    try {
      const response = await fetch(`${this.config.webhookUrl}/schema`, {
        method: 'GET',
        headers: { 'Accept': 'application/json' }
      })
      
      if (response.ok) {
        return await response.json()
      }
    } catch (error) {
      console.log('Schema discovery not available for this webhook')
    }
    
    return null
  }

  updateContext(newContext: string): void {
    this.config.context = newContext
    this.definition.configuration!.context = newContext
  }
}
