
import { toolRegistry } from './ToolRegistry'
import type { ToolExecutionRequest, ToolExecutionResult } from '@/types/tools'

export class ToolExecutor {
  async execute(request: ToolExecutionRequest): Promise<ToolExecutionResult> {
    console.log(`Executing tool: ${request.toolId} for agent: ${request.agentId}`)
    
    try {
      const result = await toolRegistry.executeTool(request)
      console.log(`Tool execution completed: ${request.toolId}`, result)
      return result
    } catch (error) {
      console.error(`Tool execution failed: ${request.toolId}`, error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Tool execution failed',
        duration: 0
      }
    }
  }

  async executeMultiple(requests: ToolExecutionRequest[]): Promise<ToolExecutionResult[]> {
    console.log(`Executing ${requests.length} tools`)
    return Promise.all(requests.map(request => this.execute(request)))
  }

  getAvailableTools(category?: string) {
    return category 
      ? toolRegistry.getToolsByCategory(category)
      : toolRegistry.getAllTools()
  }

  getToolDefinition(toolId: string) {
    const tool = toolRegistry.getTool(toolId)
    return tool?.getDefinition()
  }
}

export const toolExecutor = new ToolExecutor()
