
import type { Agent } from '@/stores/agentStore';

// Stub service to fix build error
export class AgentInitializer {
  static getInstance() {
    return new AgentInitializer();
  }
  
  async initialize() {
    // Stub implementation
  }

  static createDefaultAgents(): Agent[] {
    return [
      {
        id: '1',
        name: 'Research Agent',
        type: 'research',
        status: 'idle',
        lastActivity: null,
        configuration: {},
        autonomyLevel: 'supervised',
        pendingApprovals: []
      },
      {
        id: '2',
        name: 'Lead Qualifier',
        type: 'lead_qualifier',
        status: 'running',
        lastActivity: new Date().toISOString(),
        configuration: {},
        autonomyLevel: 'autonomous',
        pendingApprovals: []
      }
    ];
  }
}
