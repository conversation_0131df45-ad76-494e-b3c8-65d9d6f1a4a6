
interface EnvironmentConfig {
  app: {
    name: string;
    version: string;
    environment: 'development' | 'production';
    debug: boolean;
  };
  database: {
    url: string;
    anonKey: string;
    type: 'local' | 'cloud';
  };
  ai: {
    provider: 'ollama' | 'openai' | 'anthropic' | 'google' | 'huggingface' | 'openrouter' | 'nvidia' | 'mistral' | 'cohere' | 'cloudflare' | 'auto';
    ollamaUrl?: string;
    ollamaModel?: string;
    openaiApiKey?: string;
    anthropicApiKey?: string;
    googleApiKey?: string;
    huggingfaceApiKey?: string;
    openrouterApiKey?: string;
    nvidiaApiKey?: string;
    mistralApiKey?: string;
    cohereApiKey?: string;
    cloudflareApiKey?: string;
    vapiApiKey?: string;
  };
  features: {
    voice: boolean;
    realtime: boolean;
    analytics: boolean;
    testing: boolean;
  };
  security: {
    encryptionKey: string;
  };
  performance: {
    cacheDuration: number;
    maxUploadSize: number;
    apiTimeout: number;
  };
}

class ConfigManager {
  private config: EnvironmentConfig;
  private validationErrors: string[] = [];

  constructor() {
    this.config = this.loadConfiguration();
    this.validateConfiguration();
  }

  private loadConfiguration(): EnvironmentConfig {
    return {
      app: {
        name: import.meta.env.VITE_APP_NAME || 'Eigen CRM Schmiede',
        version: import.meta.env.VITE_APP_VERSION || '1.0.0',
        environment: (import.meta.env.VITE_APP_ENV as 'development' | 'production') || 'development',
        debug: import.meta.env.VITE_ENABLE_DEBUG === 'true'
      },
      database: {
        url: import.meta.env.VITE_SUPABASE_URL || '',
        anonKey: import.meta.env.VITE_SUPABASE_ANON_KEY || '',
        type: import.meta.env.VITE_SUPABASE_URL?.includes('localhost') ? 'local' : 'cloud'
      },
      ai: {
        provider: (import.meta.env.VITE_AI_PROVIDER as any) || 'ollama',
        ollamaUrl: import.meta.env.VITE_OLLAMA_URL || 'http://localhost:11434',
        ollamaModel: import.meta.env.VITE_OLLAMA_MODEL || 'llama3.1:8b',
        openaiApiKey: import.meta.env.VITE_OPENAI_API_KEY,
        anthropicApiKey: import.meta.env.VITE_ANTHROPIC_API_KEY,
        googleApiKey: import.meta.env.VITE_GOOGLE_AI_API_KEY,
        huggingfaceApiKey: import.meta.env.VITE_HUGGINGFACE_API_KEY,
        openrouterApiKey: import.meta.env.VITE_OPENROUTER_API_KEY,
        nvidiaApiKey: import.meta.env.VITE_NVIDIA_NIM_API_KEY,
        mistralApiKey: import.meta.env.VITE_MISTRAL_API_KEY,
        cohereApiKey: import.meta.env.VITE_COHERE_API_KEY,
        cloudflareApiKey: import.meta.env.VITE_CLOUDFLARE_API_KEY,
        vapiApiKey: import.meta.env.VITE_VAPI_API_KEY
      },
      features: {
        voice: import.meta.env.VITE_ENABLE_VOICE === 'true',
        realtime: import.meta.env.VITE_ENABLE_REALTIME === 'true',
        analytics: import.meta.env.VITE_ENABLE_ANALYTICS === 'true',
        testing: import.meta.env.VITE_ENABLE_AI_TESTING === 'true'
      },
      security: {
        encryptionKey: import.meta.env.VITE_ENCRYPTION_KEY || ''
      },
      performance: {
        cacheDuration: parseInt(import.meta.env.VITE_CACHE_DURATION || '300000'),
        maxUploadSize: parseInt(import.meta.env.VITE_MAX_UPLOAD_SIZE || '10485760'),
        apiTimeout: parseInt(import.meta.env.VITE_API_TIMEOUT || '30000')
      }
    };
  }

  private validateConfiguration(): void {
    this.validationErrors = [];

    // Validate required database configuration
    if (!this.config.database.url) {
      this.validationErrors.push('VITE_SUPABASE_URL is required');
    }
    if (!this.config.database.anonKey) {
      this.validationErrors.push('VITE_SUPABASE_ANON_KEY is required');
    }

    // Validate AI configuration based on provider
    if (this.config.ai.provider === 'openai' && !this.config.ai.openaiApiKey) {
      this.validationErrors.push('VITE_OPENAI_API_KEY is required when using OpenAI provider');
    }
    if (this.config.ai.provider === 'anthropic' && !this.config.ai.anthropicApiKey) {
      this.validationErrors.push('VITE_ANTHROPIC_API_KEY is required when using Anthropic provider');
    }

    // Validate security configuration for production
    if (this.config.app.environment === 'production') {
      if (!this.config.security.encryptionKey || this.config.security.encryptionKey.length < 32) {
        this.validationErrors.push('VITE_ENCRYPTION_KEY must be at least 32 characters in production');
      }
    }

    // Log validation results
    if (this.validationErrors.length > 0) {
      console.warn('Configuration validation errors:', this.validationErrors);
    } else {
      console.info('Configuration validation passed');
    }
  }

  public getConfig(): EnvironmentConfig {
    return { ...this.config };
  }

  public getValidationErrors(): string[] {
    return [...this.validationErrors];
  }

  public isValid(): boolean {
    return this.validationErrors.length === 0;
  }

  public isDevelopment(): boolean {
    return this.config.app.environment === 'development';
  }

  public isProduction(): boolean {
    return this.config.app.environment === 'production';
  }

  public isLocalDatabase(): boolean {
    return this.config.database.type === 'local';
  }

  public isCloudDatabase(): boolean {
    return this.config.database.type === 'cloud';
  }

  public switchEnvironment(type: 'local' | 'cloud'): void {
    // This would typically involve updating environment variables
    // For now, we'll log the intention
    console.info(`Environment switch requested: ${type}`);

    if (type === 'local') {
      console.info('To switch to local environment:');
      console.info('1. Start local Supabase: supabase start');
      console.info('2. Update VITE_SUPABASE_URL to http://localhost:54321');
      console.info('3. Update VITE_SUPABASE_ANON_KEY to local anon key');
    } else {
      console.info('To switch to cloud environment:');
      console.info('1. Update VITE_SUPABASE_URL to your cloud Supabase URL');
      console.info('2. Update VITE_SUPABASE_ANON_KEY to your cloud anon key');
    }
  }
}

export const configManager = new ConfigManager();
