
import { supabase, isSupabaseConfigured } from '@/lib/supabase';
import type { Database } from '@/lib/supabase';

type Tables = Database['public']['Tables'];

interface Shipment {
  id: string;
  tracking_number: string;
  origin: string;
  destination: string;
  status: 'pending' | 'in_transit' | 'delivered' | 'delayed';
  items: any[];
  estimated_delivery: string;
  actual_delivery?: string;
  supplier_id?: string;
  project_id?: string;
  created_at: string;
  updated_at: string;
}

interface Supplier {
  id: string;
  name: string;
  contact_person?: string;
  email?: string;
  phone?: string;
  address?: string;
  rating?: number;
  status: 'active' | 'inactive' | 'pending';
  categories: string[];
  created_at: string;
  updated_at: string;
}

export class LogisticsService {
  private static isConfigured = isSupabaseConfigured();

  static async getShipments(): Promise<Shipment[]> {
    if (!this.isConfigured) {
      return this.getMockShipments();
    }

    try {
      const { data, error } = await supabase
        .from('shipments')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching shipments:', error);
      return this.getMockShipments();
    }
  }

  static async createShipment(shipment: Omit<Shipment, 'id' | 'created_at' | 'updated_at'>): Promise<Shipment | null> {
    if (!this.isConfigured) {
      console.log('Mock: Creating shipment:', shipment);
      return { 
        ...shipment, 
        id: `mock-${Date.now()}`, 
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      } as Shipment;
    }

    try {
      const { data, error } = await supabase
        .from('shipments')
        .insert(shipment)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating shipment:', error);
      return null;
    }
  }

  static async updateShipment(id: string, updates: Partial<Shipment>): Promise<Shipment | null> {
    if (!this.isConfigured) {
      console.log('Mock: Updating shipment:', { id, updates });
      return { 
        ...updates, 
        id, 
        updated_at: new Date().toISOString()
      } as Shipment;
    }

    try {
      const { data, error } = await supabase
        .from('shipments')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating shipment:', error);
      return null;
    }
  }

  static async getSuppliers(): Promise<Supplier[]> {
    if (!this.isConfigured) {
      return this.getMockSuppliers();
    }

    try {
      const { data, error } = await supabase
        .from('suppliers')
        .select('*')
        .order('name');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching suppliers:', error);
      return this.getMockSuppliers();
    }
  }

  static async createSupplier(supplier: Omit<Supplier, 'id' | 'created_at' | 'updated_at'>): Promise<Supplier | null> {
    if (!this.isConfigured) {
      console.log('Mock: Creating supplier:', supplier);
      return { 
        ...supplier, 
        id: `mock-${Date.now()}`, 
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      } as Supplier;
    }

    try {
      const { data, error } = await supabase
        .from('suppliers')
        .insert(supplier)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating supplier:', error);
      return null;
    }
  }

  private static getMockShipments(): Shipment[] {
    return [
      {
        id: 'shipment-1',
        tracking_number: 'TRK-001-2024',
        origin: 'Supplier Warehouse',
        destination: 'Downtown Site',
        status: 'in_transit',
        items: [
          { name: 'Steel Beams', quantity: 50, weight: '2500kg' },
          { name: 'Concrete Mix', quantity: 100, weight: '5000kg' }
        ],
        estimated_delivery: '2024-02-25',
        supplier_id: 'supplier-1',
        project_id: 'project-1',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];
  }

  private static getMockSuppliers(): Supplier[] {
    return [
      {
        id: 'supplier-1',
        name: 'Steel & Materials Co.',
        contact_person: 'Mike Johnson',
        email: '<EMAIL>',
        phone: '******-0123',
        address: '123 Industrial Ave, Steel City',
        rating: 4.8,
        status: 'active',
        categories: ['Steel', 'Metal', 'Construction Materials'],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];
  }
}
