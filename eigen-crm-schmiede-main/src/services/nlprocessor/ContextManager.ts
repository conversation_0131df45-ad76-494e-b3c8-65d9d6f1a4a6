
export interface ConversationContext {
  userId: string;
  sessionId: string;
  conversationHistory: ConversationMessage[];
  currentTopic?: string;
  userPreferences: UserPreferences;
  activeAgents: string[];
  recentEntities: Record<string, any>;
  timestamp: string;
}

export interface ConversationMessage {
  id: string;
  type: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  intent?: string;
  entities?: Record<string, any>;
  confidence?: number;
}

export interface UserPreferences {
  communicationStyle: 'formal' | 'casual' | 'concise' | 'detailed';
  preferredAgents: string[];
  notificationSettings: Record<string, boolean>;
  language: string;
  timezone: string;
}

export class ContextManager {
  private context: ConversationContext;
  private maxHistoryLength = 50;

  constructor(userId: string) {
    this.context = {
      userId,
      sessionId: this.generateSessionId(),
      conversationHistory: [],
      userPreferences: this.getDefaultPreferences(),
      activeAgents: [],
      recentEntities: {},
      timestamp: new Date().toISOString()
    };
  }

  addMessage(message: ConversationMessage): void {
    this.context.conversationHistory.push(message);
    
    // Keep history within limits
    if (this.context.conversationHistory.length > this.maxHistoryLength) {
      this.context.conversationHistory = this.context.conversationHistory.slice(-this.maxHistoryLength);
    }

    // Update recent entities
    if (message.entities) {
      this.context.recentEntities = { ...this.context.recentEntities, ...message.entities };
    }

    // Update current topic based on intent
    if (message.intent) {
      this.updateCurrentTopic(message.intent);
    }

    this.context.timestamp = new Date().toISOString();
  }

  getRelevantContext(lookback: number = 5): ConversationMessage[] {
    return this.context.conversationHistory.slice(-lookback);
  }

  getCurrentTopic(): string | undefined {
    return this.context.currentTopic;
  }

  getRecentEntities(): Record<string, any> {
    return this.context.recentEntities;
  }

  updateUserPreferences(preferences: Partial<UserPreferences>): void {
    this.context.userPreferences = { ...this.context.userPreferences, ...preferences };
  }

  private updateCurrentTopic(intent: string): void {
    const topicMap: Record<string, string> = {
      'research_company': 'company_research',
      'list_leads': 'lead_management',
      'qualify_lead': 'lead_management',
      'list_opportunities': 'deal_management',
      'make_call': 'communications',
      'send_email': 'communications'
    };

    this.context.currentTopic = topicMap[intent] || intent;
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getDefaultPreferences(): UserPreferences {
    return {
      communicationStyle: 'casual',
      preferredAgents: [],
      notificationSettings: {
        newLeads: true,
        dealUpdates: true,
        systemAlerts: true
      },
      language: 'en',
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
    };
  }
}
