
import { ContextManager } from './ContextManager';
import { IntentClassifier } from './IntentClassifier';
import { VoiceProcessor } from './VoiceProcessor';
import { EntityExtractor } from './EntityExtractor';
import { CommandExecutor } from './CommandExecutor';

export interface ProcessingResult {
  intent: string;
  entities: Record<string, any>;
  confidence: number;
  response: string;
  suggestions: string[];
  context: any;
}

export class EnhancedNLProcessor {
  private contextManager: ContextManager;
  private intentClassifier: IntentClassifier;
  private voiceProcessor: VoiceProcessor;
  private entityExtractor: EntityExtractor;
  private commandExecutor: CommandExecutor;

  constructor(userId: string = 'default_user') {
    this.contextManager = new ContextManager(userId);
    this.intentClassifier = new IntentClassifier();
    this.voiceProcessor = new VoiceProcessor();
    this.entityExtractor = new EntityExtractor();
    this.commandExecutor = new CommandExecutor();
  }

  async processMessage(message: string): Promise<ProcessingResult> {
    console.log('Enhanced NLP processing:', message);

    // Add user message to context
    this.contextManager.addMessage({
      id: Date.now().toString(),
      type: 'user',
      content: message,
      timestamp: new Date().toISOString()
    });

    // Classify intent with context
    const classification = this.intentClassifier.classify(message, this.contextManager);
    
    // Extract additional entities
    const additionalEntities = this.extractContextualEntities(message, classification);
    const allEntities = { ...classification.entities, ...additionalEntities };

    // Execute command
    const executionResult = await this.commandExecutor.executeCommand({
      intent: classification.intent,
      entities: allEntities,
      confidence: classification.confidence
    });

    // Generate suggestions
    const suggestions = this.generateSuggestions(classification.intent, allEntities);

    // Add assistant response to context
    this.contextManager.addMessage({
      id: (Date.now() + 1).toString(),
      type: 'assistant',
      content: executionResult.message,
      timestamp: new Date().toISOString(),
      intent: classification.intent,
      entities: allEntities,
      confidence: classification.confidence
    });

    return {
      intent: classification.intent,
      entities: allEntities,
      confidence: classification.confidence,
      response: executionResult.message,
      suggestions,
      context: this.contextManager.getRelevantContext()
    };
  }

  async processVoiceInput(): Promise<ProcessingResult | null> {
    if (!this.voiceProcessor.isSupported()) {
      throw new Error('Voice processing not supported in this browser');
    }

    return new Promise((resolve, reject) => {
      this.voiceProcessor.startListening(
        async (result) => {
          if (result.isFinal) {
            try {
              const processingResult = await this.processMessage(result.transcript);
              resolve(processingResult);
            } catch (error) {
              reject(error);
            }
          }
        },
        (error) => reject(new Error(error))
      );
    });
  }

  async speakResponse(text: string): Promise<void> {
    if (this.voiceProcessor.isSupported()) {
      await this.voiceProcessor.speak(text);
    }
  }

  updateUserPreferences(preferences: any): void {
    this.contextManager.updateUserPreferences(preferences);
  }

  getConversationHistory(): any[] {
    return this.contextManager.getRelevantContext();
  }

  private extractContextualEntities(message: string, classification: any): Record<string, any> {
    const entities: Record<string, any> = {};
    const recentEntities = this.contextManager.getRecentEntities();

    // If no specific target is mentioned, try to use context
    if (!classification.entities.target && recentEntities.company) {
      entities.contextual_target = recentEntities.company;
    }

    // Extract time references
    const timeMatches = message.match(/\b(today|tomorrow|yesterday|this week|next week|last week)\b/i);
    if (timeMatches) {
      entities.time_reference = timeMatches[1];
    }

    // Extract numbers and amounts
    const numberMatches = message.match(/\b(\d+(?:,\d{3})*(?:\.\d+)?)\s*(k|thousand|m|million|billion)?\b/gi);
    if (numberMatches) {
      entities.numbers = numberMatches;
    }

    return entities;
  }

  private generateSuggestions(intent: string, entities: Record<string, any>): string[] {
    const suggestions: Record<string, string[]> = {
      'research_company': [
        'Show me their competitors',
        'Find recent news about them',
        'Get their contact information',
        'Analyze their market position'
      ],
      'list_leads': [
        'Qualify these leads',
        'Export to CSV',
        'Schedule follow-up calls',
        'Filter by score'
      ],
      'qualify_lead': [
        'Schedule a call with them',
        'Add to nurture sequence',
        'Research their company',
        'Send introduction email'
      ],
      'make_call': [
        'Schedule follow-up',
        'Log call notes',
        'Send meeting recap',
        'Update deal status'
      ],
      'dashboard_control': [
        'Show recent activity',
        'Open deals pipeline',
        'View team performance',
        'Check notifications'
      ]
    };

    return suggestions[intent] || [
      'What can I help you with next?',
      'Would you like to see related information?',
      'Should I schedule any follow-ups?'
    ];
  }
}

export const enhancedNLProcessor = new EnhancedNLProcessor();
