
import { DatabaseService } from '../DatabaseService';

export interface ExtendedConversationContext {
  userId: string;
  sessionId: string;
  conversationHistory: ConversationMessage[];
  currentTopic?: string;
  userPreferences: UserPreferences;
  activeAgents: string[];
  recentEntities: Record<string, any>;
  projectContext?: ProjectContext;
  locationContext?: LocationContext;
  timestamp: string;
}

export interface ConversationMessage {
  id: string;
  type: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  intent?: string;
  entities?: Record<string, any>;
  confidence?: number;
  metadata?: {
    route?: string;
    action?: string;
    agent?: string;
  };
}

export interface UserPreferences {
  communicationStyle: 'formal' | 'casual' | 'concise' | 'detailed';
  preferredAgents: string[];
  notificationSettings: Record<string, boolean>;
  language: string;
  timezone: string;
  defaultViews: string[];
  quickActions: string[];
}

export interface ProjectContext {
  currentProject?: string;
  recentProjects: string[];
  activePhase?: string;
  teamMembers: string[];
  deadlines: Array<{
    task: string;
    date: string;
    priority: 'low' | 'medium' | 'high';
  }>;
}

export interface LocationContext {
  currentSite?: string;
  recentSites: string[];
  weather?: {
    temperature: number;
    conditions: string;
    alerts: string[];
  };
  coordinates?: {
    lat: number;
    lng: number;
  };
}

export class EnhancedContextManager {
  private context: ExtendedConversationContext;
  private maxHistoryLength = 100;
  private persistenceEnabled = true;

  constructor(userId: string) {
    this.context = {
      userId,
      sessionId: this.generateSessionId(),
      conversationHistory: [],
      userPreferences: this.getDefaultPreferences(),
      activeAgents: [],
      recentEntities: {},
      timestamp: new Date().toISOString()
    };

    this.loadPersistedContext();
  }

  async addMessage(message: ConversationMessage): Promise<void> {
    this.context.conversationHistory.push(message);
    
    // Keep history within limits
    if (this.context.conversationHistory.length > this.maxHistoryLength) {
      this.context.conversationHistory = this.context.conversationHistory.slice(-this.maxHistoryLength);
    }

    // Update recent entities with weighted importance
    if (message.entities) {
      this.updateRecentEntities(message.entities, message.confidence || 0.5);
    }

    // Update current topic and context
    if (message.intent) {
      this.updateCurrentTopic(message.intent);
      await this.updateContextualInformation(message);
    }

    this.context.timestamp = new Date().toISOString();
    
    // Persist conversation to database
    if (this.persistenceEnabled) {
      await this.persistConversation(message);
    }
  }

  getRelevantContext(lookback: number = 10): ConversationMessage[] {
    return this.context.conversationHistory.slice(-lookback);
  }

  getContextualSummary(): string {
    const recentMessages = this.getRelevantContext(5);
    const topics = [...new Set(recentMessages.map(m => m.intent).filter(Boolean))];
    const entities = Object.keys(this.context.recentEntities);
    
    return `Recent conversation topics: ${topics.join(', ')}. Key entities: ${entities.slice(0, 5).join(', ')}.`;
  }

  getCurrentTopic(): string | undefined {
    return this.context.currentTopic;
  }

  getRecentEntities(): Record<string, any> {
    return this.context.recentEntities;
  }

  getProjectContext(): ProjectContext | undefined {
    return this.context.projectContext;
  }

  updateProjectContext(projectData: Partial<ProjectContext>): void {
    this.context.projectContext = {
      ...this.context.projectContext,
      ...projectData
    };
  }

  updateLocationContext(locationData: Partial<LocationContext>): void {
    this.context.locationContext = {
      ...this.context.locationContext,
      ...locationData
    };
  }

  updateUserPreferences(preferences: Partial<UserPreferences>): void {
    this.context.userPreferences = { ...this.context.userPreferences, ...preferences };
    this.persistPreferences();
  }

  getPersonalizedResponse(baseResponse: string): string {
    const style = this.context.userPreferences.communicationStyle;
    
    switch (style) {
      case 'formal':
        return this.formalizeTone(baseResponse);
      case 'casual':
        return this.casualizeTone(baseResponse);
      case 'concise':
        return this.makeConcise(baseResponse);
      case 'detailed':
        return this.addDetails(baseResponse);
      default:
        return baseResponse;
    }
  }

  private updateRecentEntities(entities: Record<string, any>, confidence: number): void {
    const currentTime = Date.now();
    const decayFactor = 0.95; // Entities lose importance over time
    
    // Decay existing entities
    Object.keys(this.context.recentEntities).forEach(key => {
      if (this.context.recentEntities[key].timestamp) {
        const age = currentTime - this.context.recentEntities[key].timestamp;
        const ageHours = age / (1000 * 60 * 60);
        this.context.recentEntities[key].weight *= Math.pow(decayFactor, ageHours);
        
        // Remove very low weight entities
        if (this.context.recentEntities[key].weight < 0.1) {
          delete this.context.recentEntities[key];
        }
      }
    });

    // Add new entities
    Object.entries(entities).forEach(([key, value]) => {
      this.context.recentEntities[key] = {
        value,
        weight: confidence,
        timestamp: currentTime
      };
    });
  }

  private async updateContextualInformation(message: ConversationMessage): Promise<void> {
    // Update project context based on message content
    if (message.entities?.project || message.intent?.includes('project')) {
      // Fetch and update project information
      this.updateProjectContext({
        currentProject: message.entities?.project || this.context.projectContext?.currentProject
      });
    }

    // Update location context for site-related conversations
    if (message.entities?.location || message.intent?.includes('site')) {
      this.updateLocationContext({
        currentSite: message.entities?.location || this.context.locationContext?.currentSite
      });
    }
  }

  private updateCurrentTopic(intent: string): void {
    const topicMap: Record<string, string> = {
      'research_company': 'company_research',
      'list_leads': 'lead_management',
      'qualify_lead': 'lead_management',
      'list_opportunities': 'deal_management',
      'create_project': 'project_management',
      'update_project': 'project_management',
      'list_employees': 'hr_management',
      'hire_employee': 'hr_management',
      'schedule_training': 'hr_management',
      'list_vehicles': 'fleet_management',
      'track_vehicle': 'fleet_management',
      'schedule_maintenance': 'fleet_management',
      'make_call': 'communications',
      'send_email': 'communications',
      'send_message': 'communications',
      'report_incident': 'safety_management',
      'schedule_inspection': 'safety_management',
      'quality_inspection': 'quality_control',
      'upload_document': 'document_management'
    };

    this.context.currentTopic = topicMap[intent] || intent;
  }

  private async persistConversation(message: ConversationMessage): Promise<void> {
    try {
      await DatabaseService.saveConversation({
        user_message: message.type === 'user' ? message.content : '',
        ai_response: message.type === 'assistant' ? message.content : null,
        context: {
          intent: message.intent,
          entities: message.entities,
          confidence: message.confidence,
          topic: this.context.currentTopic,
          metadata: message.metadata
        }
      });
    } catch (error) {
      console.error('Failed to persist conversation:', error);
    }
  }

  private async loadPersistedContext(): Promise<void> {
    try {
      const recentConversations = await DatabaseService.getConversations(20);
      
      // Convert database conversations to context messages
      const contextMessages: ConversationMessage[] = [];
      
      recentConversations.forEach(conv => {
        if (conv.user_message) {
          contextMessages.push({
            id: `${conv.id}-user`,
            type: 'user',
            content: conv.user_message,
            timestamp: conv.created_at,
            intent: conv.context?.intent,
            entities: conv.context?.entities,
            confidence: conv.context?.confidence
          });
        }
        
        if (conv.ai_response) {
          contextMessages.push({
            id: `${conv.id}-ai`,
            type: 'assistant',
            content: conv.ai_response,
            timestamp: conv.created_at
          });
        }
      });

      this.context.conversationHistory = contextMessages.slice(-this.maxHistoryLength);
    } catch (error) {
      console.error('Failed to load persisted context:', error);
    }
  }

  private persistPreferences(): void {
    try {
      localStorage.setItem('user_preferences', JSON.stringify(this.context.userPreferences));
    } catch (error) {
      console.error('Failed to persist preferences:', error);
    }
  }

  private formalizeTone(text: string): string {
    return text
      .replace(/\bcan't\b/g, 'cannot')
      .replace(/\bwon't\b/g, 'will not')
      .replace(/\blet's\b/g, 'let us')
      .replace(/\bhey\b/gi, 'Hello')
      .replace(/\bokay\b/gi, 'Very well');
  }

  private casualizeTone(text: string): string {
    return text
      .replace(/\bcannot\b/g, "can't")
      .replace(/\bwill not\b/g, "won't")
      .replace(/\blet us\b/g, "let's")
      .replace(/\bHello\b/g, 'Hey')
      .replace(/\bVery well\b/g, 'Okay');
  }

  private makeConcise(text: string): string {
    return text
      .split('. ')
      .slice(0, 2)
      .join('. ')
      .replace(/\b(very|really|quite|extremely)\s+/gi, '')
      .replace(/\b(I think that|It appears that|It seems that)\s+/gi, '');
  }

  private addDetails(text: string): string {
    // Add contextual details based on current topic and entities
    const details = [];
    
    if (this.context.currentTopic) {
      details.push(`This relates to ${this.context.currentTopic.replace('_', ' ')}.`);
    }
    
    const recentEntities = Object.keys(this.context.recentEntities).slice(0, 3);
    if (recentEntities.length > 0) {
      details.push(`Related to: ${recentEntities.join(', ')}.`);
    }
    
    return `${text} ${details.join(' ')}`;
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getDefaultPreferences(): UserPreferences {
    try {
      const saved = localStorage.getItem('user_preferences');
      if (saved) {
        return { ...this.getBasePreferences(), ...JSON.parse(saved) };
      }
    } catch (error) {
      console.error('Failed to load saved preferences:', error);
    }
    
    return this.getBasePreferences();
  }

  private getBasePreferences(): UserPreferences {
    return {
      communicationStyle: 'casual',
      preferredAgents: ['research-agent', 'project-manager'],
      notificationSettings: {
        newLeads: true,
        dealUpdates: true,
        systemAlerts: true,
        projectDeadlines: true,
        safetyAlerts: true
      },
      language: 'en',
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      defaultViews: ['dashboard', 'projects'],
      quickActions: ['create_project', 'add_contact', 'schedule_meeting']
    };
  }
}
