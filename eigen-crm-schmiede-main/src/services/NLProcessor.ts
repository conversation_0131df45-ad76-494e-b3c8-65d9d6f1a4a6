
import { enhancedNLProcessor } from './nlprocessor/EnhancedNLProcessor';

export interface ParsedCommand {
  intent: string;
  entities: Record<string, any>;
  confidence: number;
  agent?: string;
  action?: string;
  response?: string;
  suggestions?: string[];
}

export class NLProcessor {
  async processMessage(message: string): Promise<ParsedCommand> {
    console.log('Processing message with enhanced NLP:', message);

    try {
      const result = await enhancedNLProcessor.processMessage(message);
      
      return {
        intent: result.intent,
        entities: result.entities,
        confidence: result.confidence,
        response: result.response,
        suggestions: result.suggestions
      };
    } catch (error) {
      console.error('Enhanced NLP processing failed, falling back:', error);
      
      // Fallback to basic processing
      return {
        intent: 'unknown',
        entities: { message },
        confidence: 0.1,
        response: 'I apologize, but I had trouble understanding that. Could you please rephrase?',
        suggestions: ['Try being more specific', 'Use simple commands', 'Ask for help']
      };
    }
  }

  async executeCommand(command: ParsedCommand): Promise<any> {
    // The enhanced processor already executes commands
    return {
      type: 'processed',
      data: command.response,
      message: command.response
    };
  }

  async processVoiceInput(): Promise<ParsedCommand | null> {
    try {
      const result = await enhancedNLProcessor.processVoiceInput();
      
      if (result) {
        return {
          intent: result.intent,
          entities: result.entities,
          confidence: result.confidence,
          response: result.response,
          suggestions: result.suggestions
        };
      }
      
      return null;
    } catch (error) {
      console.error('Voice processing failed:', error);
      throw error;
    }
  }

  async speakResponse(text: string): Promise<void> {
    return enhancedNLProcessor.speakResponse(text);
  }

  isVoiceSupported(): boolean {
    return enhancedNLProcessor['voiceProcessor'].isSupported();
  }
}

export const nlProcessor = new NLProcessor();
