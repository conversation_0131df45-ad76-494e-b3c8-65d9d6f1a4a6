import { supabase, isSupabaseConfigured } from '@/lib/supabase';
import { DatabaseService } from './DatabaseService';
import { ErrorService } from './ErrorService';
import { logger } from './Logger';

export interface ReportData {
  id: string;
  title: string;
  type: 'safety' | 'quality' | 'financial' | 'hr' | 'project' | 'fleet';
  data: any[];
  generatedAt: string;
  generatedBy: string;
  filters?: Record<string, any>;
}

export interface ReportMetrics {
  totalValue: number;
  previousValue: number;
  change: number;
  changePercent: number;
  trend: 'up' | 'down' | 'stable';
}

export class ReportingService {
  static async generateSafetyReport(dateRange?: { start: string; end: string }): Promise<ReportData> {
    try {
      const reports = await DatabaseService.getSafetyReports();
      
      const filteredReports = dateRange 
        ? reports.filter(r => r.created_at >= dateRange.start && r.created_at <= dateRange.end)
        : reports;

      return {
        id: `safety_${Date.now()}`,
        title: 'Safety Report',
        type: 'safety',
        data: filteredReports,
        generatedAt: new Date().toISOString(),
        generatedBy: 'system',
        filters: dateRange
      };
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'ReportingService', 'generateSafetyReport');
      logger.error('Failed to generate safety report', { error: serviceError });
      return this.getMockSafetyReport();
    }
  }

  static async generateQualityReport(dateRange?: { start: string; end: string }): Promise<ReportData> {
    try {
      const inspections = await DatabaseService.getQualityInspections();
      
      const filteredInspections = dateRange 
        ? inspections.filter(i => i.inspection_date >= dateRange.start && i.inspection_date <= dateRange.end)
        : inspections;

      return {
        id: `quality_${Date.now()}`,
        title: 'Quality Control Report',
        type: 'quality',
        data: filteredInspections,
        generatedAt: new Date().toISOString(),
        generatedBy: 'system',
        filters: dateRange
      };
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'ReportingService', 'generateQualityReport');
      logger.error('Failed to generate quality report', { error: serviceError });
      return this.getMockQualityReport();
    }
  }

  static async generateHRReport(): Promise<ReportData> {
    try {
      const employees = await DatabaseService.getEmployees();
      const trainings = await DatabaseService.getTrainings();

      const reportData = {
        employees,
        trainings,
        summary: {
          totalEmployees: employees.length,
          activeEmployees: employees.filter(e => e.status === 'active').length,
          trainingPrograms: trainings.length,
          completedTrainings: trainings.filter(t => t.status === 'completed').length
        }
      };

      return {
        id: `hr_${Date.now()}`,
        title: 'HR Report',
        type: 'hr',
        data: [reportData],
        generatedAt: new Date().toISOString(),
        generatedBy: 'system'
      };
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'ReportingService', 'generateHRReport');
      logger.error('Failed to generate HR report', { error: serviceError });
      return this.getMockHRReport();
    }
  }

  static async generateFleetReport(): Promise<ReportData> {
    try {
      const vehicles = await DatabaseService.getVehicles();
      const maintenanceRecords = await DatabaseService.getMaintenanceRecords();

      const reportData = {
        vehicles,
        maintenanceRecords,
        summary: {
          totalVehicles: vehicles.length,
          activeVehicles: vehicles.filter(v => v.status === 'available' || v.status === 'in_use').length,
          maintenanceDue: vehicles.filter(v => v.status === 'maintenance').length,
          totalMaintenanceRecords: maintenanceRecords.length
        }
      };

      return {
        id: `fleet_${Date.now()}`,
        title: 'Fleet Management Report',
        type: 'fleet',
        data: [reportData],
        generatedAt: new Date().toISOString(),
        generatedBy: 'system'
      };
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'ReportingService', 'generateFleetReport');
      logger.error('Failed to generate fleet report', { error: serviceError });
      return this.getMockFleetReport();
    }
  }

  static calculateMetrics(currentData: any[], previousData: any[], valueField: string): ReportMetrics {
    const currentValue = currentData.reduce((sum, item) => sum + (item[valueField] || 0), 0);
    const previousValue = previousData.reduce((sum, item) => sum + (item[valueField] || 0), 0);
    
    const change = currentValue - previousValue;
    const changePercent = previousValue > 0 ? (change / previousValue) * 100 : 0;
    
    let trend: 'up' | 'down' | 'stable' = 'stable';
    if (change > 0) trend = 'up';
    else if (change < 0) trend = 'down';

    return {
      totalValue: currentValue,
      previousValue,
      change,
      changePercent,
      trend
    };
  }

  private static calculateSafetyMetrics(reports: any[]) {
    const highSeverity = reports.filter(r => r.severity === 'high').length;
    const resolved = reports.filter(r => r.status === 'resolved').length;
    const pending = reports.filter(r => r.status === 'pending').length;

    return {
      totalReports: reports.length,
      highSeverityIncidents: highSeverity,
      resolvedIncidents: resolved,
      pendingIncidents: pending,
      resolutionRate: reports.length > 0 ? (resolved / reports.length) * 100 : 0
    };
  }

  private static getMockSafetyReport(): ReportData {
    return {
      id: 'mock_safety_report',
      title: 'Safety Report',
      type: 'safety',
      data: [
        { id: '1', title: 'PPE Inspection', severity: 'low', status: 'resolved' },
        { id: '2', title: 'Equipment Maintenance', severity: 'high', status: 'pending' }
      ],
      generatedAt: new Date().toISOString(),
      generatedBy: 'system'
    };
  }

  private static getMockQualityReport(): ReportData {
    return {
      id: 'mock_quality_report',
      title: 'Quality Control Report',
      type: 'quality',
      data: [
        { id: '1', area: 'Building A', passed: true, defects_found: 0 },
        { id: '2', area: 'Foundation', passed: false, defects_found: 3 }
      ],
      generatedAt: new Date().toISOString(),
      generatedBy: 'system'
    };
  }

  private static getMockHRReport(): ReportData {
    return {
      id: 'mock_hr_report',
      title: 'HR Report',
      type: 'hr',
      data: [{
        summary: {
          totalEmployees: 25,
          activeEmployees: 23,
          trainingPrograms: 8,
          completedTrainings: 15
        }
      }],
      generatedAt: new Date().toISOString(),
      generatedBy: 'system'
    };
  }

  private static getMockFleetReport(): ReportData {
    return {
      id: 'mock_fleet_report',
      title: 'Fleet Management Report',
      type: 'fleet',
      data: [{
        summary: {
          totalVehicles: 12,
          activeVehicles: 10,
          maintenanceDue: 2,
          totalMaintenanceRecords: 45
        }
      }],
      generatedAt: new Date().toISOString(),
      generatedBy: 'system'
    };
  }
}
