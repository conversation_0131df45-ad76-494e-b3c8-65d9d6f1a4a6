import { logger } from './Logger';
import { errorHandler } from './ErrorHandler';

interface SecurityEvent {
  type: 'suspicious_activity' | 'rate_limit_exceeded' | 'unauthorized_access' | 'data_exposure' | 'xss_attempt';
  severity: 'low' | 'medium' | 'high' | 'critical';
  source: string;
  details: Record<string, any>;
  timestamp: Date;
  userId?: string;
  ip?: string;
}

interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
}

class SecurityMonitor {
  private events: SecurityEvent[] = [];
  private rateLimits = new Map<string, { count: number; resetTime: number }>();
  private blockedIPs = new Set<string>();
  private suspiciousPatterns: RegExp[] = [
    /<script/i,
    /javascript:/i,
    /onerror=/i,
    /onload=/i,
    /eval\(/i,
    /document\.cookie/i
  ];

  constructor() {
    this.initializeSecurityMonitoring();
  }

  private initializeSecurityMonitoring() {
    // Monitor for potential XSS attempts
    this.setupXSSProtection();
    
    // Monitor navigation patterns
    this.setupNavigationMonitoring();
    
    // Monitor console access
    this.setupConsoleMonitoring();
  }

  private setupXSSProtection() {
    const originalAddEventListener = EventTarget.prototype.addEventListener;
    
    EventTarget.prototype.addEventListener = function(type, listener, options) {
      if (typeof listener === 'string') {
        securityMonitor.logSecurityEvent({
          type: 'xss_attempt',
          severity: 'high',
          source: 'EventListener',
          details: { eventType: type, listener }
        });
      }
      return originalAddEventListener.call(this, type, listener, options);
    };
  }

  private setupNavigationMonitoring() {
    let navigationCount = 0;
    const startTime = Date.now();

    const originalPushState = history.pushState;
    history.pushState = function(...args) {
      navigationCount++;
      
      // Detect rapid navigation (potential bot behavior)
      if (navigationCount > 50 && (Date.now() - startTime) < 10000) {
        securityMonitor.logSecurityEvent({
          type: 'suspicious_activity',
          severity: 'medium',
          source: 'Navigation',
          details: { rapidNavigation: true, count: navigationCount }
        });
      }
      
      return originalPushState.apply(this, args);
    };
  }

  private setupConsoleMonitoring() {
    if (process.env.NODE_ENV === 'production') {
      const originalLog = console.log;
      console.log = function(...args) {
        // In production, log any console access as potentially suspicious
        securityMonitor.logSecurityEvent({
          type: 'suspicious_activity',
          severity: 'low',
          source: 'Console',
          details: { consoleAccess: true, args: args.slice(0, 3) }
        });
        return originalLog.apply(this, args);
      };
    }
  }

  public checkRateLimit(identifier: string, config: RateLimitConfig): boolean {
    const now = Date.now();
    const limit = this.rateLimits.get(identifier);

    if (!limit || now > limit.resetTime) {
      this.rateLimits.set(identifier, {
        count: 1,
        resetTime: now + config.windowMs
      });
      return true;
    }

    if (limit.count >= config.maxRequests) {
      this.logSecurityEvent({
        type: 'rate_limit_exceeded',
        severity: 'medium',
        source: identifier,
        details: { count: limit.count, maxRequests: config.maxRequests }
      });
      return false;
    }

    limit.count++;
    return true;
  }

  public validateInput(input: string, context: string): boolean {
    for (const pattern of this.suspiciousPatterns) {
      if (pattern.test(input)) {
        this.logSecurityEvent({
          type: 'xss_attempt',
          severity: 'high',
          source: context,
          details: { input: input.substring(0, 100), pattern: pattern.source }
        });
        return false;
      }
    }
    return true;
  }

  public logSecurityEvent(event: Omit<SecurityEvent, 'timestamp'>) {
    const securityEvent: SecurityEvent = {
      ...event,
      timestamp: new Date(),
      ip: this.getCurrentIP()
    };

    this.events.push(securityEvent);
    
    // Keep only last 1000 events
    if (this.events.length > 1000) {
      this.events = this.events.slice(-1000);
    }

    logger.warn(`Security event: ${event.type}`, securityEvent);

    // Handle critical events immediately
    if (event.severity === 'critical') {
      this.handleCriticalEvent(securityEvent);
    }

    // In production, send to security monitoring service
    if (process.env.NODE_ENV === 'production') {
      this.sendToSecurityService(securityEvent);
    }
  }

  private getCurrentIP(): string {
    // In a real app, this would get the actual client IP
    return 'client-ip-placeholder';
  }

  private handleCriticalEvent(event: SecurityEvent) {
    // Block the source if it's a critical security event
    if (event.ip) {
      this.blockedIPs.add(event.ip);
    }

    // Report to error handler
    errorHandler.handleError(new Error(`Critical security event: ${event.type}`), {
      component: 'SecurityMonitor',
      action: 'criticalEvent',
      severity: 'critical',
      category: 'security'
    });
  }

  private sendToSecurityService(event: SecurityEvent) {
    // In production, this would send to security monitoring services
    console.log('Security monitoring:', event);
  }

  public getSecurityEvents(type?: SecurityEvent['type']): SecurityEvent[] {
    if (type) {
      return this.events.filter(event => event.type === type);
    }
    return [...this.events];
  }

  public getSecuritySummary(): Record<string, number> {
    const summary: Record<string, number> = {};
    
    this.events.forEach(event => {
      summary[event.type] = (summary[event.type] || 0) + 1;
    });
    
    return summary;
  }

  public isBlocked(ip: string): boolean {
    return this.blockedIPs.has(ip);
  }

  public clearSecurityEvents() {
    this.events = [];
  }
}

export const securityMonitor = new SecurityMonitor();
