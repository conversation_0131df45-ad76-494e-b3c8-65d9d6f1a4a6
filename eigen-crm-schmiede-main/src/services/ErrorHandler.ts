interface ErrorContext {
  component?: string;
  action?: string;
  userId?: string;
  timestamp: string;
  url: string;
  userAgent: string;
}

interface ErrorReport {
  message: string;
  stack?: string;
  context: ErrorContext;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'ui' | 'api' | 'validation' | 'permission' | 'network' | 'ai' | 'security';
}

class ErrorHandler {
  private errorQueue: ErrorReport[] = [];
  private maxQueueSize = 100;
  private retryAttempts = new Map<string, number>();
  private maxRetries = 3;

  constructor() {
    this.setupGlobalHandlers();
  }

  private setupGlobalHandlers() {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.handleError(event.reason, {
        component: 'Global',
        action: 'unhandledRejection',
        severity: 'high',
        category: 'api'
      });
    });

    // Handle global errors
    window.addEventListener('error', (event) => {
      this.handleError(event.error, {
        component: 'Global',
        action: 'globalError',
        severity: 'high',
        category: 'ui'
      });
    });
  }

  public handleError(
    error: Error | string, 
    options: {
      component?: string;
      action?: string;
      severity?: 'low' | 'medium' | 'high' | 'critical';
      category?: 'ui' | 'api' | 'validation' | 'permission' | 'network' | 'ai' | 'security';
      retry?: boolean;
    } = {}
  ) {
    const errorMessage = typeof error === 'string' ? error : error.message;
    const errorStack = typeof error === 'string' ? undefined : error.stack;

    const context: ErrorContext = {
      component: options.component || 'Unknown',
      action: options.action || 'Unknown',
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent
    };

    const report: ErrorReport = {
      message: errorMessage,
      stack: errorStack,
      context,
      severity: options.severity || 'medium',
      category: options.category || 'ui'
    };

    this.queueError(report);
    
    // Show user-friendly error message
    this.showUserError(report);

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Error handled:', report);
    }

    // Send to monitoring service in production
    if (process.env.NODE_ENV === 'production') {
      this.sendToMonitoring(report);
    }
  }

  private queueError(report: ErrorReport) {
    this.errorQueue.push(report);
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue.shift();
    }
  }

  private showUserError(report: ErrorReport) {
    const { toast } = require('@/hooks/use-toast');
    
    const userMessage = this.getUserFriendlyMessage(report);
    
    toast({
      title: "Something went wrong",
      description: userMessage,
      variant: report.severity === 'critical' ? 'destructive' : 'default',
      duration: report.severity === 'low' ? 3000 : 5000
    });
  }

  private getUserFriendlyMessage(report: ErrorReport): string {
    switch (report.category) {
      case 'network':
        return "Network connection issue. Please check your internet connection.";
      case 'api':
        return "Service temporarily unavailable. Please try again in a moment.";
      case 'validation':
        return "Please check your input and try again.";
      case 'permission':
        return "You don't have permission to perform this action.";
      case 'ai':
        return "AI service is temporarily unavailable. Please try again.";
      case 'security':
        return "Security issue detected. Please contact support if this persists.";
      default:
        return "An unexpected error occurred. Our team has been notified.";
    }
  }

  private sendToMonitoring(report: ErrorReport) {
    // In production, this would send to monitoring services like Sentry
    console.log('Monitoring report:', report);
  }

  public async retryOperation<T>(
    operation: () => Promise<T>,
    context: { component: string; action: string }
  ): Promise<T> {
    const key = `${context.component}-${context.action}`;
    const attempts = this.retryAttempts.get(key) || 0;

    try {
      const result = await operation();
      this.retryAttempts.delete(key);
      return result;
    } catch (error) {
      if (attempts < this.maxRetries) {
        this.retryAttempts.set(key, attempts + 1);
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempts) * 1000));
        return this.retryOperation(operation, context);
      } else {
        this.retryAttempts.delete(key);
        this.handleError(error as Error, {
          ...context,
          severity: 'high',
          category: 'api'
        });
        throw error;
      }
    }
  }

  public getErrorHistory(): ErrorReport[] {
    return [...this.errorQueue];
  }

  public clearErrorHistory() {
    this.errorQueue = [];
  }
}

export const errorHandler = new ErrorHandler();
