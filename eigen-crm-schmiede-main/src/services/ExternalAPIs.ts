import { useSettingsStore } from '@/stores/settingsStore';

// External API integrations for AI-powered CRM
export class PerplexityService {
  private getApiKey(): string | null {
    // Try settings store first, fallback to localStorage for backward compatibility
    const { apiKeys } = useSettingsStore.getState();
    return apiKeys.perplexity || localStorage.getItem('perplexity_api_key');
  }

  async searchCompany(companyName: string): Promise<any> {
    const apiKey = this.getApiKey();
    
    if (!apiKey) {
      console.log('No Perplexity API key found, using mock data');
      return this.mockCompanyData(companyName);
    }

    try {
      const response = await fetch('https://api.perplexity.ai/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: 'llama-3.1-sonar-small-128k-online',
          messages: [
            {
              role: 'user',
              content: `Research ${companyName}: provide company overview, industry, size, recent news, key contacts, and competitive landscape. Format as structured data.`
            }
          ]
        })
      });

      const data = await response.json();
      return this.parseCompanyData(data.choices[0].message.content);
    } catch (error) {
      console.log('Perplexity API error, using mock data:', error);
      return this.mockCompanyData(companyName);
    }
  }

  private mockCompanyData(companyName: string) {
    return {
      name: companyName,
      industry: 'Technology',
      size: '100-500 employees',
      headquarters: 'San Francisco, CA',
      website: `www.${companyName.toLowerCase().replace(/\s+/g, '')}.com`,
      description: `${companyName} is a leading technology company`,
      recent_news: [
        'Series B funding round completed',
        'New product feature released',
        'Strategic partnership announced'
      ],
      key_contacts: [
        { role: 'CEO', name: 'John Smith' },
        { role: 'CTO', name: 'Jane Doe' },
        { role: 'VP Sales', name: 'Mike Johnson' }
      ],
      competitors: ['Competitor A', 'Competitor B', 'Competitor C'],
      financial_info: {
        revenue_range: '$10M - $50M',
        funding_stage: 'Series B',
        investors: ['VC Firm A', 'VC Firm B']
      }
    }
  }

  private parseCompanyData(content: string) {
    // Simple parsing logic - in production this would be more sophisticated
    return {
      raw_research: content,
      summary: content.substring(0, 200) + '...',
      confidence: 0.85
    }
  }
}

export class VAPIService {
  private getApiKey(): string | null {
    // Try settings store first, fallback to localStorage for backward compatibility
    const { apiKeys } = useSettingsStore.getState();
    return apiKeys.vapi || localStorage.getItem('vapi_api_key');
  }

  async makeCall(phoneNumber: string, purpose: string): Promise<any> {
    const apiKey = this.getApiKey();
    
    if (!apiKey) {
      console.log('No VAPI API key found, using mock data');
      return this.mockCallData(phoneNumber, purpose);
    }

    try {
      const response = await fetch('https://api.vapi.ai/call', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          phoneNumber,
          assistant: {
            name: 'Sales Assistant',
            firstMessage: `Hello, I'm calling regarding ${purpose}`,
            model: {
              provider: 'openai',
              model: 'gpt-3.5-turbo'
            },
            voice: {
              provider: 'eleven-labs',
              voiceId: 'professional'
            }
          }
        })
      });

      return await response.json();
    } catch (error) {
      console.log('VAPI error, using mock data:', error);
      return this.mockCallData(phoneNumber, purpose);
    }
  }

  private mockCallData(phoneNumber: string, purpose: string) {
    return {
      call_id: Date.now().toString(),
      status: 'completed',
      phone_number: phoneNumber,
      purpose,
      duration: Math.floor(Math.random() * 20) + 5, // 5-25 minutes
      transcript: `Mock call transcript for ${purpose}`,
      summary: `Successfully connected with contact regarding ${purpose}`,
      sentiment: 'positive',
      next_steps: ['Send follow-up email', 'Schedule demo']
    }
  }

  async analyzeCall(callId: string): Promise<any> {
    // Mock call analysis
    return {
      call_id: callId,
      sentiment_score: Math.random(),
      key_topics: ['pricing', 'timeline', 'features'],
      action_items: ['Send proposal', 'Schedule demo', 'Follow up next week'],
      buyer_signals: ['expressed interest', 'asked about pricing', 'discussed timeline']
    }
  }
}

export const perplexityService = new PerplexityService();
export const vapiService = new VAPIService();
