
import React, { useState, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Plus, Search } from 'lucide-react';
import { FleetService } from '@/services/FleetService';
import { ErrorService } from '@/services/ErrorService';
import { VehicleCard } from './fleet/VehicleCard';
import { FleetStats } from './fleet/FleetStats';
import { useToast } from '@/hooks/use-toast';
import type { Vehicle } from '@/types/fleet';

export const Fleet = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newVehicle, setNewVehicle] = useState({
    name: '',
    type: 'truck' as const,
    make: '',
    model: '',
    year: new Date().getFullYear(),
    license_plate: '',
    status: 'available' as const
  });

  const queryClient = useQueryClient();
  const { toast } = useToast();

  // Fetch vehicles
  const { data: vehicles = [], isLoading: vehiclesLoading, error } = useQuery({
    queryKey: ['vehicles'],
    queryFn: FleetService.getVehicles,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Create vehicle mutation
  const createVehicleMutation = useMutation({
    mutationFn: (vehicle: Omit<Vehicle, 'id' | 'created_at' | 'updated_at'>) => 
      FleetService.createVehicle(vehicle),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['vehicles'] });
      setIsCreateDialogOpen(false);
      setNewVehicle({
        name: '',
        type: 'truck',
        make: '',
        model: '',
        year: new Date().getFullYear(),
        license_plate: '',
        status: 'available'
      });
      toast({
        title: "Vehicle Created",
        description: "New vehicle has been added to the fleet.",
      });
    },
    onError: (error) => {
      const serviceError = ErrorService.handleServiceError(error, 'FleetService', 'createVehicle');
      toast({
        title: "Error",
        description: ErrorService.getUserFriendlyMessage(serviceError),
        variant: "destructive",
      });
    },
  });

  // Update vehicle status mutation
  const updateVehicleMutation = useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: any }) => 
      FleetService.updateVehicle(id, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['vehicles'] });
      toast({
        title: "Vehicle Updated",
        description: "Vehicle status has been updated.",
      });
    },
    onError: (error) => {
      const serviceError = ErrorService.handleServiceError(error, 'FleetService', 'updateVehicle');
      toast({
        title: "Error",
        description: ErrorService.getUserFriendlyMessage(serviceError),
        variant: "destructive",
      });
    },
  });

  const handleCreateVehicle = () => {
    createVehicleMutation.mutate(newVehicle);
  };

  const handleStatusChange = (vehicleId: string, newStatus: string) => {
    updateVehicleMutation.mutate({ 
      id: vehicleId, 
      updates: { status: newStatus } 
    });
  };

  const filteredVehicles = useMemo(() => {
    return vehicles.filter(vehicle => {
      const matchesSearch = vehicle.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           vehicle.license_plate?.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = statusFilter === 'all' || vehicle.status === statusFilter;
      const matchesType = typeFilter === 'all' || vehicle.type === typeFilter;
      return matchesSearch && matchesStatus && matchesType;
    });
  }, [vehicles, searchTerm, statusFilter, typeFilter]);

  const stats = useMemo(() => ({
    total: vehicles.length,
    available: vehicles.filter(v => v.status === 'available').length,
    in_use: vehicles.filter(v => v.status === 'in_use').length,
    maintenance: vehicles.filter(v => v.status === 'maintenance').length,
    out_of_service: vehicles.filter(v => v.status === 'out_of_service').length
  }), [vehicles]);

  if (vehiclesLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="animate-pulse">Loading fleet...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="text-red-600">Error loading fleet data. Please try again.</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-slate-900">Fleet Management</h1>
            <p className="text-slate-600 mt-2">Monitor and manage your vehicle fleet</p>
          </div>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-blue-600 hover:bg-blue-700">
                <Plus className="h-4 w-4 mr-2" />
                Add Vehicle
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add New Vehicle</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="name">Vehicle Name</Label>
                  <Input
                    id="name"
                    value={newVehicle.name}
                    onChange={(e) => setNewVehicle({ ...newVehicle, name: e.target.value })}
                    placeholder="e.g., CAT 320 Excavator"
                  />
                </div>
                <div>
                  <Label htmlFor="type">Type</Label>
                  <Select value={newVehicle.type} onValueChange={(value: any) => setNewVehicle({ ...newVehicle, type: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="truck">Truck</SelectItem>
                      <SelectItem value="excavator">Excavator</SelectItem>
                      <SelectItem value="crane">Crane</SelectItem>
                      <SelectItem value="bulldozer">Bulldozer</SelectItem>
                      <SelectItem value="van">Van</SelectItem>
                      <SelectItem value="car">Car</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="make">Make</Label>
                    <Input
                      id="make"
                      value={newVehicle.make}
                      onChange={(e) => setNewVehicle({ ...newVehicle, make: e.target.value })}
                      placeholder="e.g., Ford"
                    />
                  </div>
                  <div>
                    <Label htmlFor="model">Model</Label>
                    <Input
                      id="model"
                      value={newVehicle.model}
                      onChange={(e) => setNewVehicle({ ...newVehicle, model: e.target.value })}
                      placeholder="e.g., F-250"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="license_plate">License Plate</Label>
                  <Input
                    id="license_plate"
                    value={newVehicle.license_plate}
                    onChange={(e) => setNewVehicle({ ...newVehicle, license_plate: e.target.value })}
                    placeholder="e.g., ABC-123"
                  />
                </div>
                <Button onClick={handleCreateVehicle} className="w-full">
                  Create Vehicle
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {/* Stats Cards */}
        <FleetStats stats={stats} />

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-wrap gap-4">
              <div className="flex-1 min-w-[200px]">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                  <Input
                    placeholder="Search vehicles..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="available">Available</SelectItem>
                  <SelectItem value="in_use">In Use</SelectItem>
                  <SelectItem value="maintenance">Maintenance</SelectItem>
                  <SelectItem value="out_of_service">Out of Service</SelectItem>
                </SelectContent>
              </Select>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="truck">Truck</SelectItem>
                  <SelectItem value="excavator">Excavator</SelectItem>
                  <SelectItem value="crane">Crane</SelectItem>
                  <SelectItem value="bulldozer">Bulldozer</SelectItem>
                  <SelectItem value="van">Van</SelectItem>
                  <SelectItem value="car">Car</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Vehicles Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredVehicles.map((vehicle) => (
            <VehicleCard
              key={vehicle.id}
              vehicle={vehicle}
              onStatusChange={handleStatusChange}
            />
          ))}
        </div>
      </div>
    </div>
  );
};
