
import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { 
  LayoutDashboard, 
  Users, 
  CheckSquare, 
  Calendar,
  Settings,
  Menu,
  X,
  Briefcase,
  FileText,
  BarChart3,
  Shield,
  Truck
} from 'lucide-react';
import { cn } from '@/lib/utils';

const navigation = [
  { name: 'Dashboard', icon: LayoutDashboard, href: '/dashboard', path: 'dashboard' },
  { name: 'Projects', icon: Briefcase, href: '/projects', path: 'projects' },
  { name: 'People', icon: Users, href: '/people', path: 'people' },
  { name: 'Tasks', icon: CheckSquare, href: '/tasks', path: 'tasks' },
  { name: 'Calendar', icon: Calendar, href: '/calendar', path: 'calendar' },
  { name: 'Documents', icon: FileText, href: '/documents', path: 'documents' },
  { name: 'Fleet', icon: Truck, href: '/fleet', path: 'fleet' },
  { name: 'Reports', icon: BarChart3, href: '/reports', path: 'reports' },
  { name: 'Security', icon: Shield, href: '/security', path: 'security' },
  { name: 'Settings', icon: Settings, href: '/settings', path: 'settings' },
];

export const Sidebar = () => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();

  const currentPath = location.pathname.split('/')[1] || 'dashboard';

  const handleNavigation = (href: string) => {
    navigate(href);
  };

  return (
    <div className={cn(
      "flex flex-col bg-gradient-to-b from-slate-900 via-slate-800 to-slate-900 border-r border-slate-700/50 transition-all duration-300 shadow-2xl",
      isCollapsed ? "w-16" : "w-80"
    )}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-slate-700/50">
        {!isCollapsed && (
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 via-purple-500 to-indigo-500 rounded-lg flex items-center justify-center shadow-lg">
              <span className="text-white font-bold text-sm">CRM</span>
            </div>
            <span className="font-semibold text-white text-lg">Construction CRM</span>
          </div>
        )}
        <button
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="p-2 rounded-lg text-slate-300 hover:text-white hover:bg-slate-700/50 transition-all duration-200"
        >
          {isCollapsed ? <Menu size={20} /> : <X size={20} />}
        </button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
        {navigation.map((item) => (
          <button
            key={item.name}
            onClick={() => handleNavigation(item.href)}
            className={cn(
              "w-full flex items-center rounded-xl px-4 py-3 text-sm font-medium transition-all duration-200 group hover:shadow-lg",
              isCollapsed ? "justify-center" : "",
              currentPath === item.path 
                ? "bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg" 
                : "text-slate-300 hover:text-white hover:bg-slate-700/50"
            )}
          >
            <item.icon className="h-5 w-5 flex-shrink-0 group-hover:scale-110 transition-transform duration-200" />
            {!isCollapsed && <span className="ml-3 truncate text-left">{item.name}</span>}
          </button>
        ))}
      </nav>

      {/* User Profile */}
      {!isCollapsed && (
        <div className="p-4 border-t border-slate-700/50 bg-slate-800/50">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center shadow-lg">
              <span className="text-white text-sm font-medium">JD</span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-white truncate">
                John Doe
              </p>
              <p className="text-xs text-slate-400 truncate">
                <EMAIL>
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
