
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Calendar, Clock, Flag, MessageSquare, Paperclip, MoreHorizontal } from 'lucide-react';

export const TaskList = () => {
  const tasks = [
    {
      id: 1,
      title: 'Foundation Inspection Planning',
      project: 'Downtown Office Complex',
      priority: 'high',
      status: 'todo',
      dueDate: '2024-01-15',
      assignee: { name: '<PERSON>', avatar: '' },
      tags: ['inspection', 'foundation'],
      comments: 3,
      attachments: 2,
      progress: 0
    },
    {
      id: 2,
      title: 'Material Procurement - Steel Beams',
      project: 'Residential Tower',
      priority: 'medium',
      status: 'todo',
      dueDate: '2024-01-18',
      assignee: { name: '<PERSON>', avatar: '' },
      tags: ['procurement', 'materials'],
      comments: 1,
      attachments: 0,
      progress: 0
    },
    {
      id: 3,
      title: 'Safety Protocol Review',
      project: 'Highway Bridge',
      priority: 'high',
      status: 'progress',
      dueDate: '2024-01-12',
      assignee: { name: 'Mike Wilson', avatar: '' },
      tags: ['safety', 'protocols'],
      comments: 5,
      attachments: 3,
      progress: 65
    },
    {
      id: 4,
      title: 'Electrical System Design',
      project: 'Shopping Mall',
      priority: 'medium',
      status: 'progress',
      dueDate: '2024-01-20',
      assignee: { name: 'Emily Davis', avatar: '' },
      tags: ['electrical', 'design'],
      comments: 2,
      attachments: 1,
      progress: 40
    },
    {
      id: 5,
      title: 'Budget Analysis Q1',
      project: 'Corporate Campus',
      priority: 'low',
      status: 'review',
      dueDate: '2024-01-14',
      assignee: { name: 'David Brown', avatar: '' },
      tags: ['budget', 'analysis'],
      comments: 0,
      attachments: 4,
      progress: 90
    }
  ];

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'low': return 'text-green-600 bg-green-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'todo': return 'text-gray-600 bg-gray-100';
      case 'progress': return 'text-blue-600 bg-blue-100';
      case 'review': return 'text-orange-600 bg-orange-100';
      case 'done': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-12 gap-4 p-4 bg-gray-50 rounded-lg text-sm font-medium text-gray-600">
        <div className="col-span-4">Task</div>
        <div className="col-span-2">Project</div>
        <div className="col-span-1">Priority</div>
        <div className="col-span-1">Status</div>
        <div className="col-span-2">Assignee</div>
        <div className="col-span-1">Due Date</div>
        <div className="col-span-1">Actions</div>
      </div>

      {tasks.map((task) => (
        <Card key={task.id} className="hover:shadow-md transition-shadow">
          <CardContent className="p-4">
            <div className="grid grid-cols-12 gap-4 items-center">
              <div className="col-span-4">
                <div className="space-y-2">
                  <h3 className="font-medium text-sm">{task.title}</h3>
                  <div className="flex flex-wrap gap-1">
                    {task.tags.map((tag) => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                  {task.progress > 0 && (
                    <div className="w-full bg-gray-200 rounded-full h-1.5">
                      <div 
                        className="bg-blue-600 h-1.5 rounded-full transition-all duration-300" 
                        style={{ width: `${task.progress}%` }}
                      />
                    </div>
                  )}
                </div>
              </div>

              <div className="col-span-2">
                <span className="text-sm text-muted-foreground">{task.project}</span>
              </div>

              <div className="col-span-1">
                <Badge className={`text-xs ${getPriorityColor(task.priority)}`}>
                  {task.priority}
                </Badge>
              </div>

              <div className="col-span-1">
                <Badge className={`text-xs ${getStatusColor(task.status)}`}>
                  {task.status}
                </Badge>
              </div>

              <div className="col-span-2">
                <div className="flex items-center gap-2">
                  <Avatar className="w-6 h-6">
                    <AvatarImage src={task.assignee.avatar} />
                    <AvatarFallback className="text-xs">
                      {task.assignee.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <span className="text-sm">{task.assignee.name}</span>
                </div>
              </div>

              <div className="col-span-1">
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Calendar className="w-3 h-3" />
                  {task.dueDate}
                </div>
              </div>

              <div className="col-span-1">
                <div className="flex items-center gap-2">
                  <div className="flex items-center gap-1 text-muted-foreground">
                    {task.comments > 0 && (
                      <div className="flex items-center gap-1">
                        <MessageSquare className="w-3 h-3" />
                        <span className="text-xs">{task.comments}</span>
                      </div>
                    )}
                    {task.attachments > 0 && (
                      <div className="flex items-center gap-1">
                        <Paperclip className="w-3 h-3" />
                        <span className="text-xs">{task.attachments}</span>
                      </div>
                    )}
                  </div>
                  <Button variant="ghost" size="sm">
                    <MoreHorizontal className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
