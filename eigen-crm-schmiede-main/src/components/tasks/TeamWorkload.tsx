
import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Users, TrendingUp, Clock, CheckCircle } from 'lucide-react';

export const TeamWorkload = () => {
  const teamMembers = [
    {
      id: 1,
      name: '<PERSON>',
      role: 'Project Manager',
      avatar: '',
      workload: 85,
      activeTasks: 8,
      completedTasks: 24,
      efficiency: 92,
      status: 'overloaded'
    },
    {
      id: 2,
      name: '<PERSON>',
      role: 'Site Engineer',
      avatar: '',
      workload: 70,
      activeTasks: 6,
      completedTasks: 18,
      efficiency: 88,
      status: 'optimal'
    },
    {
      id: 3,
      name: '<PERSON>',
      role: 'Safety Officer',
      avatar: '',
      workload: 45,
      activeTasks: 4,
      completedTasks: 15,
      efficiency: 95,
      status: 'available'
    },
    {
      id: 4,
      name: '<PERSON>',
      role: 'Electrical Engineer',
      avatar: '',
      workload: 60,
      activeTasks: 5,
      completedTasks: 12,
      efficiency: 87,
      status: 'optimal'
    },
    {
      id: 5,
      name: '<PERSON> <PERSON>',
      role: 'Budget Analyst',
      avatar: '',
      workload: 75,
      activeTasks: 7,
      completedTasks: 20,
      efficiency: 90,
      status: 'optimal'
    },
    {
      id: 6,
      name: 'Lisa Garcia',
      role: 'Quality Inspector',
      avatar: '',
      workload: 55,
      activeTasks: 4,
      completedTasks: 16,
      efficiency: 93,
      status: 'optimal'
    }
  ];

  const getWorkloadColor = (workload: number) => {
    if (workload >= 80) return 'text-red-600';
    if (workload >= 60) return 'text-yellow-600';
    return 'text-green-600';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'overloaded': return 'bg-red-100 text-red-800';
      case 'optimal': return 'bg-green-100 text-green-800';
      case 'available': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const overallStats = {
    avgWorkload: Math.round(teamMembers.reduce((sum, member) => sum + member.workload, 0) / teamMembers.length),
    totalActiveTasks: teamMembers.reduce((sum, member) => sum + member.activeTasks, 0),
    avgEfficiency: Math.round(teamMembers.reduce((sum, member) => sum + member.efficiency, 0) / teamMembers.length),
    availableMembers: teamMembers.filter(member => member.status === 'available').length
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium">Team Size</span>
            </div>
            <div className="text-2xl font-bold mt-2">{teamMembers.length}</div>
            <p className="text-xs text-muted-foreground">Active members</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium">Avg Workload</span>
            </div>
            <div className="text-2xl font-bold mt-2">{overallStats.avgWorkload}%</div>
            <p className="text-xs text-muted-foreground">Current capacity</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-orange-600" />
              <span className="text-sm font-medium">Active Tasks</span>
            </div>
            <div className="text-2xl font-bold mt-2">{overallStats.totalActiveTasks}</div>
            <p className="text-xs text-muted-foreground">In progress</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-purple-600" />
              <span className="text-sm font-medium">Efficiency</span>
            </div>
            <div className="text-2xl font-bold mt-2">{overallStats.avgEfficiency}%</div>
            <p className="text-xs text-muted-foreground">Team average</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Team Member Workloads</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {teamMembers.map((member) => (
              <div key={member.id} className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <Avatar className="w-10 h-10">
                      <AvatarImage src={member.avatar} />
                      <AvatarFallback>
                        {member.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium">{member.name}</div>
                      <div className="text-sm text-muted-foreground">{member.role}</div>
                    </div>
                  </div>
                  <Badge className={getStatusColor(member.status)}>
                    {member.status}
                  </Badge>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <div className="text-sm text-muted-foreground mb-1">Workload</div>
                    <div className="flex items-center gap-2">
                      <Progress value={member.workload} className="flex-1" />
                      <span className={`text-sm font-medium ${getWorkloadColor(member.workload)}`}>
                        {member.workload}%
                      </span>
                    </div>
                  </div>

                  <div>
                    <div className="text-sm text-muted-foreground mb-1">Active Tasks</div>
                    <div className="text-lg font-semibold">{member.activeTasks}</div>
                  </div>

                  <div>
                    <div className="text-sm text-muted-foreground mb-1">Completed</div>
                    <div className="text-lg font-semibold">{member.completedTasks}</div>
                  </div>

                  <div>
                    <div className="text-sm text-muted-foreground mb-1">Efficiency</div>
                    <div className="text-lg font-semibold">{member.efficiency}%</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
