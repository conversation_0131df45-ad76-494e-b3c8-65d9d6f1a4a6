
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Calendar, Clock, Flag, MessageSquare, Paperclip } from 'lucide-react';

export const TaskBoard = () => {
  const columns = [
    { id: 'todo', title: 'To Do', count: 8, color: 'bg-gray-100' },
    { id: 'progress', title: 'In Progress', count: 12, color: 'bg-blue-100' },
    { id: 'review', title: 'Review', count: 5, color: 'bg-yellow-100' },
    { id: 'done', title: 'Done', count: 23, color: 'bg-green-100' }
  ];

  const tasks = {
    todo: [
      {
        id: 1,
        title: 'Foundation Inspection Planning',
        project: 'Downtown Office Complex',
        priority: 'high',
        dueDate: '2024-01-15',
        assignee: { name: '<PERSON>', avatar: '' },
        tags: ['inspection', 'foundation'],
        comments: 3,
        attachments: 2
      },
      {
        id: 2,
        title: 'Material Procurement - Steel Beams',
        project: 'Residential Tower',
        priority: 'medium',
        dueDate: '2024-01-18',
        assignee: { name: '<PERSON>', avatar: '' },
        tags: ['procurement', 'materials'],
        comments: 1,
        attachments: 0
      }
    ],
    progress: [
      {
        id: 3,
        title: 'Safety Protocol Review',
        project: 'Highway Bridge',
        priority: 'high',
        dueDate: '2024-01-12',
        assignee: { name: 'Mike Wilson', avatar: '' },
        tags: ['safety', 'protocols'],
        comments: 5,
        attachments: 3
      },
      {
        id: 4,
        title: 'Electrical System Design',
        project: 'Shopping Mall',
        priority: 'medium',
        dueDate: '2024-01-20',
        assignee: { name: 'Emily Davis', avatar: '' },
        tags: ['electrical', 'design'],
        comments: 2,
        attachments: 1
      }
    ],
    review: [
      {
        id: 5,
        title: 'Budget Analysis Q1',
        project: 'Corporate Campus',
        priority: 'low',
        dueDate: '2024-01-14',
        assignee: { name: 'David Brown', avatar: '' },
        tags: ['budget', 'analysis'],
        comments: 0,
        attachments: 4
      }
    ],
    done: [
      {
        id: 6,
        title: 'Site Survey Completion',
        project: 'Retail Complex',
        priority: 'medium',
        dueDate: '2024-01-10',
        assignee: { name: 'Lisa Garcia', avatar: '' },
        tags: ['survey', 'site'],
        comments: 8,
        attachments: 6
      }
    ]
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {columns.map((column) => (
        <div key={column.id} className="space-y-4">
          <div className={`p-4 rounded-lg ${column.color}`}>
            <div className="flex items-center justify-between">
              <h3 className="font-semibold text-gray-800">{column.title}</h3>
              <Badge variant="secondary">{column.count}</Badge>
            </div>
          </div>
          
          <div className="space-y-3">
            {tasks[column.id as keyof typeof tasks]?.map((task) => (
              <Card key={task.id} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <CardTitle className="text-sm font-medium leading-tight">
                      {task.title}
                    </CardTitle>
                    <div className={`w-3 h-3 rounded-full ${getPriorityColor(task.priority)}`} />
                  </div>
                  <p className="text-xs text-muted-foreground">{task.project}</p>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="flex flex-wrap gap-1 mb-3">
                    {task.tags.map((tag) => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Avatar className="w-6 h-6">
                        <AvatarImage src={task.assignee.avatar} />
                        <AvatarFallback className="text-xs">
                          {task.assignee.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <span className="text-xs text-muted-foreground">
                        {task.assignee.name}
                      </span>
                    </div>
                    
                    <div className="flex items-center gap-1 text-muted-foreground">
                      {task.comments > 0 && (
                        <div className="flex items-center gap-1">
                          <MessageSquare className="w-3 h-3" />
                          <span className="text-xs">{task.comments}</span>
                        </div>
                      )}
                      {task.attachments > 0 && (
                        <div className="flex items-center gap-1">
                          <Paperclip className="w-3 h-3" />
                          <span className="text-xs">{task.attachments}</span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between mt-2 pt-2 border-t">
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <Calendar className="w-3 h-3" />
                      {task.dueDate}
                    </div>
                    <Clock className="w-3 h-3 text-muted-foreground" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};
