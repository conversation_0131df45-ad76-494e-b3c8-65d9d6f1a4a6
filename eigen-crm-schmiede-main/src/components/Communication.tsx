
import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { CommunicationHeader } from './communication/CommunicationHeader';
import { CommunicationStats } from './communication/CommunicationStats';
import { CommunicationTabs } from './communication/CommunicationTabs';
import { DatabaseService } from '@/services/DatabaseService';
import { useToast } from '@/hooks/use-toast';

export const Communication = () => {
  const [activeTab, setActiveTab] = useState('messages');
  const [selectedChannel, setSelectedChannel] = useState('general');
  const queryClient = useQueryClient();
  const { toast } = useToast();

  // Fetch channels
  const { data: channelsData, isLoading: channelsLoading } = useQuery({
    queryKey: ['channels'],
    queryFn: DatabaseService.getChannels,
  });

  // Fetch messages for selected channel
  const { data: messagesResponse, isLoading: messagesLoading } = useQuery({
    queryKey: ['messages', selectedChannel],
    queryFn: () => DatabaseService.getMessages(selectedChannel),
    enabled: !!selectedChannel,
  });

  // Fetch announcements
  const { data: announcementsResponse, isLoading: announcementsLoading } = useQuery({
    queryKey: ['announcements'],
    queryFn: DatabaseService.getAnnouncements,
  });

  // Send message mutation
  const sendMessageMutation = useMutation({
    mutationFn: (message: string) => DatabaseService.sendMessage(selectedChannel, message),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['messages', selectedChannel] });
      toast({
        title: "Message Sent",
        description: "Your message has been sent successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to send message.",
        variant: "destructive",
      });
      console.error('Error sending message:', error);
    },
  });

  // Create announcement mutation
  const createAnnouncementMutation = useMutation({
    mutationFn: (announcement: { title: string; content: string; priority: string }) =>
      DatabaseService.createAnnouncement(announcement),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['announcements'] });
      toast({
        title: "Announcement Created",
        description: "Your announcement has been posted successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to create announcement.",
        variant: "destructive",
      });
      console.error('Error creating announcement:', error);
    },
  });

  const handleSendMessage = (message: string) => {
    sendMessageMutation.mutate(message);
  };

  const handleCreateAnnouncement = (announcement: { title: string; content: string; priority: string }) => {
    createAnnouncementMutation.mutate(announcement);
  };

  // Ensure we have arrays
  const channels = Array.isArray(channelsData) ? channelsData : [];
  const messagesData = Array.isArray(messagesResponse) ? messagesResponse : [];
  const announcementsData = Array.isArray(announcementsResponse) ? announcementsResponse : [];

  const getSelectedChannelName = () => {
    const channel = channels.find(c => c.id === selectedChannel);
    return channel?.name || 'Unknown';
  };

  // Convert database messages to component format
  const messages = messagesData.map(msg => ({
    id: msg.id,
    sender: msg.sender || 'Unknown User',
    content: msg.content,
    timestamp: msg.timestamp || msg.created_at,
    avatar: msg.avatar || `https://api.dicebear.com/7.x/avataaars/svg?seed=${msg.sender_id}`,
    type: msg.type as 'text' | 'file' | 'system',
    reactions: msg.reactions || []
  }));

  // Convert database announcements to component format
  const announcements = announcementsData.map(ann => ({
    id: ann.id,
    title: ann.title,
    content: ann.content,
    author: ann.author || 'Unknown Author',
    timestamp: ann.timestamp || ann.created_at,
    priority: ann.priority as 'high' | 'medium' | 'low',
    isPinned: ann.is_pinned || false,
    views: ann.views || 0,
    reactions: ann.reactions || 0,
    comments: ann.comments || 0
  }));

  // Convert channels to the format expected by CommunicationTabs
  const formattedChannels = channels.map(channel => ({
    id: channel.id,
    name: channel.name,
    unread: channel.unread || 0,
    type: channel.type as 'channel' | 'dm'
  }));

  // Mock direct messages for now (will be implemented later)
  const mockDirectMessages = [
    {
      id: 'dm-1',
      name: 'John Smith',
      status: 'online' as const,
      unread: 2,
      type: 'dm' as const
    }
  ];

  if (channelsLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-purple-50 to-pink-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="animate-pulse">Loading communication...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-purple-50 to-pink-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        <CommunicationHeader />
        <CommunicationStats />
        
        <CommunicationTabs
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          channels={formattedChannels}
          directMessages={mockDirectMessages}
          selectedChannel={selectedChannel}
          setSelectedChannel={setSelectedChannel}
          messages={messages}
          announcements={announcements}
          onSendMessage={handleSendMessage}
          onCreateAnnouncement={handleCreateAnnouncement}
          getSelectedChannelName={getSelectedChannelName}
        />
      </div>
    </div>
  );
};
