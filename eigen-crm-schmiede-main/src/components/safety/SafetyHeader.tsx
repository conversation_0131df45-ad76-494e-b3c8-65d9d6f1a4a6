
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Plus, Search, Filter, AlertTriangle } from 'lucide-react';

export const SafetyHeader = () => {
  return (
    <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl p-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-slate-900 via-red-900 to-orange-900 bg-clip-text text-transparent">
            Safety Management
          </h1>
          <p className="text-slate-600 mt-2 text-lg">Monitor incidents, ensure compliance, and maintain safety standards</p>
        </div>
        <div className="flex gap-3">
          <Button className="bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700 text-white shadow-lg hover:shadow-xl transition-all duration-300">
            <AlertTriangle className="h-4 w-4 mr-2" />
            Report Incident
          </Button>
          <Button variant="outline" className="border-slate-200 hover:bg-slate-50">
            <Search className="h-4 w-4 mr-2" />
            Search
          </Button>
          <Button variant="outline" className="border-slate-200 hover:bg-slate-50">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
        </div>
      </div>
    </div>
  );
};
