
import React from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { IncidentTracking } from './IncidentTracking';
import { SafetyProtocols } from './SafetyProtocols';
import { ComplianceMonitoring } from './ComplianceMonitoring';
import { SafetyReports } from './SafetyReports';

interface SafetyTabsProps {
  activeTab: string;
  onTabChange: (value: string) => void;
}

export const SafetyTabs = ({ activeTab, onTabChange }: SafetyTabsProps) => {
  return (
    <Tabs value={activeTab} onValueChange={onTabChange} className="space-y-6">
      <TabsList className="bg-white/80 backdrop-blur-sm border border-white/20 shadow-lg p-1 rounded-xl">
        <TabsTrigger value="incidents" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-red-600 data-[state=active]:to-orange-600 data-[state=active]:text-white rounded-lg">
          Incident Tracking
        </TabsTrigger>
        <TabsTrigger value="protocols" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-red-600 data-[state=active]:to-orange-600 data-[state=active]:text-white rounded-lg">
          Safety Protocols
        </TabsTrigger>
        <TabsTrigger value="compliance" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-red-600 data-[state=active]:to-orange-600 data-[state=active]:text-white rounded-lg">
          Compliance
        </TabsTrigger>
        <TabsTrigger value="reports" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-red-600 data-[state=active]:to-orange-600 data-[state=active]:text-white rounded-lg">
          Reports
        </TabsTrigger>
      </TabsList>

      <TabsContent value="incidents" className="space-y-6">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl p-6">
          <IncidentTracking />
        </div>
      </TabsContent>

      <TabsContent value="protocols" className="space-y-6">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl p-6">
          <SafetyProtocols />
        </div>
      </TabsContent>

      <TabsContent value="compliance" className="space-y-6">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl p-6">
          <ComplianceMonitoring />
        </div>
      </TabsContent>

      <TabsContent value="reports" className="space-y-6">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl p-6">
          <SafetyReports />
        </div>
      </TabsContent>
    </Tabs>
  );
};
