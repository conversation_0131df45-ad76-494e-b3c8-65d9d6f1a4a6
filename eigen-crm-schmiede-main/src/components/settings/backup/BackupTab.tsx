
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Download, RefreshCw, Shield } from 'lucide-react';

interface BackupTabProps {
  onExport: () => void;
  isExporting: boolean;
}

export const BackupTab: React.FC<BackupTabProps> = ({
  onExport,
  isExporting
}) => {
  return (
    <div className="space-y-4">
      <Alert>
        <Shield className="h-4 w-4" />
        <AlertDescription>
          Backup includes all settings except sensitive API keys (for security)
        </AlertDescription>
      </Alert>
      
      <div className="flex items-center justify-between p-4 border rounded-lg">
        <div>
          <h4 className="font-medium">Export Current Settings</h4>
          <p className="text-sm text-muted-foreground">
            Download your current configuration as a JSON file
          </p>
        </div>
        <Button 
          onClick={onExport}
          disabled={isExporting}
        >
          {isExporting ? (
            <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
          ) : (
            <Download className="w-4 h-4 mr-2" />
          )}
          Export Settings
        </Button>
      </div>
    </div>
  );
};
