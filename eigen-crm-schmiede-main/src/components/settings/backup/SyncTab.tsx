
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { RefreshCw, Clock, CheckCircle } from 'lucide-react';

interface SyncTabProps {
  lastSync?: Date;
  onSync: () => void;
  onReset: () => void;
  isSyncing: boolean;
}

export const SyncTab: React.FC<SyncTabProps> = ({
  lastSync,
  onSync,
  onReset,
  isSyncing
}) => {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between p-4 border rounded-lg">
        <div>
          <h4 className="font-medium flex items-center gap-2">
            <Clock className="w-4 h-4" />
            Last Sync
          </h4>
          <p className="text-sm text-muted-foreground">
            {lastSync ? lastSync.toLocaleString() : 'Never synced'}
          </p>
        </div>
        <Button 
          onClick={onSync}
          disabled={isSyncing}
          variant="outline"
        >
          {isSyncing ? (
            <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
          ) : (
            <RefreshCw className="w-4 h-4 mr-2" />
          )}
          Sync Now
        </Button>
      </div>

      <Alert>
        <CheckCircle className="h-4 w-4" />
        <AlertDescription>
          Settings are automatically saved locally. Sync will be available when connected to Supabase.
        </AlertDescription>
      </Alert>

      <div className="pt-4 border-t">
        <Button 
          onClick={onReset}
          variant="destructive"
          className="w-full"
        >
          Reset All Settings
        </Button>
        <p className="text-xs text-muted-foreground text-center mt-2">
          This action cannot be undone
        </p>
      </div>
    </div>
  );
};
