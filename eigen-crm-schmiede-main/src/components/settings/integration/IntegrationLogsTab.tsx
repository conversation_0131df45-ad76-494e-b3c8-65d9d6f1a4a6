
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { CheckCircle, XCircle, Clock } from 'lucide-react';
import { Integration, ConnectionTest } from '@/types/integration';

interface IntegrationLogsTabProps {
  integrations: Integration[];
  connectionTests: Map<string, ConnectionTest>;
}

export const IntegrationLogsTab: React.FC<IntegrationLogsTabProps> = ({
  integrations,
  connectionTests
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-base">Recent Connection Tests</CardTitle>
      </CardHeader>
      <CardContent>
        {connectionTests.size === 0 ? (
          <p className="text-muted-foreground text-center py-4">
            No connection tests performed yet. Use the "Test" buttons to check integration status.
          </p>
        ) : (
          <div className="space-y-2">
            {Array.from(connectionTests.entries()).map(([id, test]) => {
              const integration = integrations.find(i => i.id === id);
              return (
                <div key={id} className="flex items-center justify-between p-2 border rounded text-sm">
                  <span>{integration?.name}</span>
                  <div className="flex items-center gap-2">
                    {test.result === 'success' && <CheckCircle className="w-4 h-4 text-green-600" />}
                    {test.result === 'failed' && <XCircle className="w-4 h-4 text-red-600" />}
                    {test.isRunning && <Clock className="w-4 h-4 text-blue-600 animate-pulse" />}
                    <span className="text-xs text-muted-foreground">
                      {test.responseTime ? `${test.responseTime}ms` : 'Testing...'}
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
