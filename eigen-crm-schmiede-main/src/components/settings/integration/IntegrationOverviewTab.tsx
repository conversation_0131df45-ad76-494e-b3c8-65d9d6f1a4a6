
import React from 'react';
import { IntegrationCard } from './IntegrationCard';
import { Integration, ConnectionTest } from '@/types/integration';

interface IntegrationOverviewTabProps {
  integrations: Integration[];
  connectionTests: Map<string, ConnectionTest>;
  onTestConnection: (integration: Integration) => void;
  onConfigure: (integration: Integration) => void;
}

export const IntegrationOverviewTab: React.FC<IntegrationOverviewTabProps> = ({
  integrations,
  connectionTests,
  onTestConnection,
  onConfigure
}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {integrations.map((integration) => {
        const test = connectionTests.get(integration.id);
        
        return (
          <IntegrationCard
            key={integration.id}
            integration={integration}
            test={test}
            onTest={onTestConnection}
            onConfigure={onConfigure}
          />
        );
      })}
    </div>
  );
};
