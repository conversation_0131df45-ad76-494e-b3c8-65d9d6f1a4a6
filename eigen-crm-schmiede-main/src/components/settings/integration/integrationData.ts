
import { 
  Search, 
  Phone, 
  Database, 
  Mail,
  Zap
} from 'lucide-react';
import { Integration } from '@/types/integration';

export const initializeIntegrations = (): Integration[] => {
  return [
    {
      id: 'perplexity',
      name: 'Perplexity AI',
      description: 'AI-powered research and information retrieval',
      status: localStorage.getItem('perplexity_api_key') ? 'connected' : 'disconnected',
      icon: Search,
      lastSync: new Date(Date.now() - 2 * 60 * 1000),
      version: '1.0.0',
      responseTime: 1200,
      configUrl: 'api'
    },
    {
      id: 'vapi',
      name: 'VAPI Voice',
      description: 'AI voice calling and communication',
      status: localStorage.getItem('vapi_api_key') ? 'connected' : 'disconnected',
      icon: Phone,
      lastSync: new Date(Date.now() - 5 * 60 * 1000),
      version: '2.1.0',
      responseTime: 850,
      configUrl: 'api'
    },
    {
      id: 'vector-db',
      name: 'Vector Database',
      description: 'Document indexing and semantic search',
      status: 'connected',
      icon: Database,
      lastSync: new Date(Date.now() - 1 * 60 * 1000),
      version: '1.2.3',
      responseTime: 300,
      configUrl: 'rag'
    },
    {
      id: 'email',
      name: 'Email Service',
      description: 'Automated email communications',
      status: 'disconnected',
      icon: Mail,
      version: '1.5.0',
      configUrl: 'notifications'
    },
    {
      id: 'automation',
      name: 'N8N Automation',
      description: 'Workflow automation and integrations',
      status: 'connected',
      icon: Zap,
      lastSync: new Date(Date.now() - 30 * 1000),
      version: '0.235.0',
      responseTime: 450,
      configUrl: 'tools'
    }
  ];
};
