
import React from 'react';
import { StatusIndicator } from '@/components/ui/status-indicator';
import { Badge } from '@/components/ui/badge';
import { Integration, ConnectionTest } from '@/types/integration';

interface IntegrationDetailsTabProps {
  integrations: Integration[];
  connectionTests: Map<string, ConnectionTest>;
}

export const IntegrationDetailsTab: React.FC<IntegrationDetailsTabProps> = ({
  integrations,
  connectionTests
}) => {
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'connected':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Connected</Badge>;
      case 'testing':
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Testing...</Badge>;
      case 'error':
        return <Badge className="bg-red-100 text-red-800 border-red-200">Error</Badge>;
      default:
        return <Badge variant="outline">Disconnected</Badge>;
    }
  };

  return (
    <div className="space-y-3">
      {integrations.map((integration) => {
        const test = connectionTests.get(integration.id);
        
        return (
          <div key={integration.id} className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center gap-4">
              <StatusIndicator status={integration.status === 'connected' ? 'good' : integration.status === 'error' ? 'error' : 'warning'} />
              <div>
                <h4 className="font-medium">{integration.name}</h4>
                <p className="text-sm text-muted-foreground">{integration.description}</p>
              </div>
            </div>
            <div className="text-right">
              {getStatusBadge(integration.status)}
              {integration.responseTime && (
                <p className="text-xs text-muted-foreground mt-1">
                  {integration.responseTime}ms
                </p>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
};
