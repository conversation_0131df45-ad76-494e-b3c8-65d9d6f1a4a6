
import React, { useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { ProviderConfig, ProviderType, TaskType } from '@/types/provider';
import { useToast } from '@/hooks/use-toast';

interface ProviderConfigDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  provider?: ProviderConfig;
  onSave: (config: ProviderConfig) => void;
}

export const ProviderConfigDialog: React.FC<ProviderConfigDialogProps> = ({
  open,
  onOpenChange,
  provider,
  onSave
}) => {
  const { toast } = useToast();
  const [config, setConfig] = useState<Partial<ProviderConfig>>(
    provider || {
      name: '',
      type: 'openai',
      models: [],
      isLocal: false,
      privacyScore: 5,
      costPerRequest: 0,
      rateLimits: {
        requestsPerMinute: 60,
        requestsPerDay: 1000
      },
      capabilities: [],
      averageResponseTime: 1000,
      reliability: 0.95
    }
  );

  const handleSave = () => {
    if (!config.name || !config.type) {
      toast({
        title: "Validation Error",
        description: "Name and type are required",
        variant: "destructive"
      });
      return;
    }

    const fullConfig: ProviderConfig = {
      id: provider?.id || `${config.type}-${Date.now()}`,
      name: config.name!,
      type: config.type!,
      apiKey: config.apiKey,
      baseUrl: config.baseUrl,
      models: config.models || [],
      isLocal: config.isLocal || false,
      privacyScore: config.privacyScore || 5,
      costPerRequest: config.costPerRequest || 0,
      rateLimits: config.rateLimits || { requestsPerMinute: 60, requestsPerDay: 1000 },
      capabilities: config.capabilities || [],
      averageResponseTime: config.averageResponseTime || 1000,
      reliability: config.reliability || 0.95
    };

    onSave(fullConfig);
    onOpenChange(false);
    toast({
      title: "Provider Saved",
      description: `${config.name} configuration has been saved`
    });
  };

  const toggleCapability = (capability: TaskType) => {
    const current = config.capabilities || [];
    const updated = current.includes(capability)
      ? current.filter(c => c !== capability)
      : [...current, capability];
    setConfig({ ...config, capabilities: updated });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{provider ? 'Edit Provider' : 'Add Provider'}</DialogTitle>
          <DialogDescription>
            Configure AI provider settings and capabilities
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Provider Name</Label>
              <Input
                id="name"
                value={config.name || ''}
                onChange={(e) => setConfig({ ...config, name: e.target.value })}
                placeholder="e.g., OpenAI GPT-4"
              />
            </div>
            <div>
              <Label htmlFor="type">Provider Type</Label>
              <Select value={config.type} onValueChange={(value: ProviderType) => setConfig({ ...config, type: value })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="openai">OpenAI</SelectItem>
                  <SelectItem value="anthropic">Anthropic</SelectItem>
                  <SelectItem value="google">Google</SelectItem>
                  <SelectItem value="ollama">Ollama</SelectItem>
                  <SelectItem value="perplexity">Perplexity</SelectItem>
                  <SelectItem value="huggingface">HuggingFace</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="apiKey">API Key</Label>
              <Input
                id="apiKey"
                type="password"
                value={config.apiKey || ''}
                onChange={(e) => setConfig({ ...config, apiKey: e.target.value })}
                placeholder="Enter API key"
              />
            </div>
            <div>
              <Label htmlFor="baseUrl">Base URL</Label>
              <Input
                id="baseUrl"
                value={config.baseUrl || ''}
                onChange={(e) => setConfig({ ...config, baseUrl: e.target.value })}
                placeholder="https://api.openai.com/v1"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="models">Models (comma-separated)</Label>
            <Textarea
              id="models"
              value={config.models?.join(', ') || ''}
              onChange={(e) => setConfig({ 
                ...config, 
                models: e.target.value.split(',').map(m => m.trim()).filter(Boolean)
              })}
              placeholder="gpt-4, gpt-3.5-turbo"
              rows={2}
            />
          </div>

          <div className="grid grid-cols-3 gap-4">
            <div>
              <Label htmlFor="privacyScore">Privacy Score (1-10)</Label>
              <Input
                id="privacyScore"
                type="number"
                min="1"
                max="10"
                value={config.privacyScore || 5}
                onChange={(e) => setConfig({ ...config, privacyScore: parseInt(e.target.value) })}
              />
            </div>
            <div>
              <Label htmlFor="costPerRequest">Cost per Request (cents)</Label>
              <Input
                id="costPerRequest"
                type="number"
                step="0.01"
                value={config.costPerRequest || 0}
                onChange={(e) => setConfig({ ...config, costPerRequest: parseFloat(e.target.value) })}
              />
            </div>
            <div>
              <Label htmlFor="reliability">Reliability (0-1)</Label>
              <Input
                id="reliability"
                type="number"
                step="0.01"
                min="0"
                max="1"
                value={config.reliability || 0.95}
                onChange={(e) => setConfig({ ...config, reliability: parseFloat(e.target.value) })}
              />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              checked={config.isLocal || false}
              onCheckedChange={(checked) => setConfig({ ...config, isLocal: checked })}
            />
            <Label>Local Provider</Label>
          </div>

          <div>
            <Label>Capabilities</Label>
            <div className="flex flex-wrap gap-2 mt-2">
              {(['research', 'analysis', 'conversation', 'code_generation', 'creative', 'translation'] as TaskType[]).map((capability) => (
                <Badge
                  key={capability}
                  variant={config.capabilities?.includes(capability) ? 'default' : 'outline'}
                  className="cursor-pointer"
                  onClick={() => toggleCapability(capability)}
                >
                  {capability}
                </Badge>
              ))}
            </div>
          </div>

          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button onClick={handleSave}>
              Save Provider
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
