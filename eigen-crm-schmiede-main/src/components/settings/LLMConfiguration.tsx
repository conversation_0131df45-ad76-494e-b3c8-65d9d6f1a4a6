
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Brain, Cloud, Server, CheckCircle, AlertCircle, Download } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { LLMService } from '@/services/llm/LLMService';
import { OllamaService } from '@/services/llm/OllamaService';

export const LLMConfiguration: React.FC = () => {
  const [systemStatus, setSystemStatus] = useState<any>(null);
  const [isLoadingStatus, setIsLoadingStatus] = useState(false);
  const [hybridMode, setHybridMode] = useState(true);
  const [preferredProvider, setPreferredProvider] = useState<'cloud' | 'ollama'>('ollama');
  const { toast } = useToast();

  useEffect(() => {
    loadSystemStatus();
  }, []);

  const loadSystemStatus = async () => {
    setIsLoadingStatus(true);
    try {
      const status = await LLMService.getSystemStatus();
      setSystemStatus(status);
    } catch (error) {
      console.error('Failed to load system status:', error);
    } finally {
      setIsLoadingStatus(false);
    }
  };

  const pullModel = async (modelName: string) => {
    toast({
      title: "Downloading Model",
      description: `Starting download of ${modelName}...`,
    });

    try {
      const success = await OllamaService.pullModel(modelName);
      if (success) {
        toast({
          title: "Model Downloaded",
          description: `${modelName} is now available locally`,
        });
        loadSystemStatus();
      } else {
        toast({
          title: "Download Failed",
          description: `Failed to download ${modelName}`,
        });
      }
    } catch (error) {
      toast({
        title: "Download Error",
        description: "Failed to download model",
      });
    }
  };

  const testLLMConnection = async (provider: 'cloud' | 'ollama') => {
    try {
      const response = await LLMService.processRequest({
        task: 'conversation',
        prompt: 'Test connection - respond with "Connection successful"',
        preferredProvider: provider
      });

      toast({
        title: "Connection Test",
        description: `${provider} LLM: ${response.response}`,
      });
    } catch (error) {
      toast({
        title: "Connection Failed",
        description: `${provider} LLM connection test failed`,
      });
    }
  };

  const getStatusBadge = (available: boolean) => {
    return available ? (
      <Badge className="bg-green-100 text-green-800">
        <CheckCircle className="w-3 h-3 mr-1" />
        Available
      </Badge>
    ) : (
      <Badge variant="outline">
        <AlertCircle className="w-3 h-3 mr-1" />
        Unavailable
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="w-5 h-5" />
            Hybrid LLM Configuration
          </CardTitle>
          <CardDescription>
            Configure your AI models and routing preferences
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Hybrid LLM Mode</Label>
              <p className="text-sm text-muted-foreground">
                Automatically route tasks between local and cloud LLMs
              </p>
            </div>
            <Switch
              checked={hybridMode}
              onCheckedChange={setHybridMode}
            />
          </div>

          <div className="space-y-2">
            <Label>Preferred Provider</Label>
            <Select value={preferredProvider} onValueChange={(value: 'cloud' | 'ollama') => setPreferredProvider(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ollama">Local (Ollama) - Privacy First</SelectItem>
                <SelectItem value="cloud">Cloud - Maximum Performance</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-sm text-muted-foreground">
              {preferredProvider === 'ollama' 
                ? 'Prioritizes local models for better privacy and lower costs'
                : 'Prioritizes cloud models for maximum performance and capabilities'
              }
            </p>
          </div>

          <div className="flex gap-2">
            <Button 
              variant="outline" 
              onClick={loadSystemStatus}
              disabled={isLoadingStatus}
            >
              {isLoadingStatus ? 'Checking...' : 'Refresh Status'}
            </Button>
            <Button 
              variant="outline"
              onClick={() => testLLMConnection('ollama')}
            >
              Test Local
            </Button>
            <Button 
              variant="outline"
              onClick={() => testLLMConnection('cloud')}
            >
              Test Cloud
            </Button>
          </div>
        </CardContent>
      </Card>

      {systemStatus && (
        <>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Server className="w-5 h-5" />
                Local LLM Status (Ollama)
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span>Ollama Service</span>
                {getStatusBadge(systemStatus.ollama.available)}
              </div>

              {systemStatus.ollama.available ? (
                <div className="space-y-3">
                  <Label>Available Models</Label>
                  {systemStatus.ollama.models.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {systemStatus.ollama.models.map((model: any) => (
                        <div key={model.name} className="p-3 rounded-lg border bg-card">
                          <div className="flex items-center justify-between mb-1">
                            <span className="font-medium text-sm">{model.name}</span>
                            <Badge variant="outline">
                              {(model.size / 1024 / 1024 / 1024).toFixed(1)} GB
                            </Badge>
                          </div>
                          <p className="text-xs text-muted-foreground">
                            Modified: {new Date(model.modified_at).toLocaleDateString()}
                          </p>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-6 text-muted-foreground">
                      <p>No models installed</p>
                      <Button 
                        className="mt-2" 
                        size="sm"
                        onClick={() => pullModel('llama2')}
                      >
                        <Download className="w-3 h-3 mr-1" />
                        Install Llama2
                      </Button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-6 text-muted-foreground">
                  <p>Ollama is not running. Please start Ollama to use local models.</p>
                  <p className="text-xs mt-1">Run: ollama serve</p>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Cloud className="w-5 h-5" />
                Cloud LLM Status
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span>Cloud Service</span>
                {getStatusBadge(systemStatus.cloud.available)}
              </div>

              <div className="space-y-3">
                <Label>Available Models</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {systemStatus.cloud.models.map((model: string) => (
                    <div key={model} className="p-3 rounded-lg border bg-card">
                      <span className="font-medium text-sm">{model}</span>
                      <p className="text-xs text-muted-foreground mt-1">
                        Cloud-hosted model
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Task Routing Configuration</CardTitle>
          <CardDescription>
            How different types of tasks are routed to LLM providers
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-3 rounded-lg border">
                <h4 className="font-medium text-sm mb-2">Sensitive Data & Research</h4>
                <Badge className="bg-blue-100 text-blue-800">Local First</Badge>
                <p className="text-xs text-muted-foreground mt-1">
                  Company research, customer data analysis
                </p>
              </div>
              <div className="p-3 rounded-lg border">
                <h4 className="font-medium text-sm mb-2">Code Generation</h4>
                <Badge className="bg-purple-100 text-purple-800">Local Preferred</Badge>
                <p className="text-xs text-muted-foreground mt-1">
                  Uses CodeLlama when available
                </p>
              </div>
              <div className="p-3 rounded-lg border">
                <h4 className="font-medium text-sm mb-2">Creative Tasks</h4>
                <Badge className="bg-green-100 text-green-800">Cloud Preferred</Badge>
                <p className="text-xs text-muted-foreground mt-1">
                  Email writing, content generation
                </p>
              </div>
              <div className="p-3 rounded-lg border">
                <h4 className="font-medium text-sm mb-2">Real-time Chat</h4>
                <Badge className="bg-orange-100 text-orange-800">Local First</Badge>
                <p className="text-xs text-muted-foreground mt-1">
                  Fast response for conversations
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
