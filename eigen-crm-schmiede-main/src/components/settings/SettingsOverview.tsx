
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Brain, 
  Database, 
  Shield, 
  Key, 
  Bell, 
  Server,
  CheckCircle,
  AlertCircle,
  Settings as SettingsIcon,
  Download,
  Zap,
  Wrench
} from 'lucide-react';
import { useAIStore } from '@/stores/aiStore';

export const SettingsOverview = () => {
  const { agents } = useAIStore();

  const getSystemHealth = () => {
    const activeAgents = agents.filter(a => a.status === 'running').length;
    const totalAgents = agents.length;
    
    if (activeAgents === 0) return { status: 'warning', message: 'No active agents' };
    if (activeAgents === totalAgents) return { status: 'good', message: 'All systems operational' };
    return { status: 'partial', message: `${activeAgents}/${totalAgents} agents active` };
  };

  const systemHealth = getSystemHealth();

  const settingsSections = [
    {
      title: 'Integration Status',
      description: 'Monitor external service connections',
      icon: Zap,
      status: 'configured',
      path: 'integrations'
    },
    {
      title: 'AI Configuration',
      description: 'Configure AI agents and behavior',
      icon: Brain,
      status: 'configured',
      path: 'ai'
    },
    {
      title: 'RAG & Vector DB',
      description: 'Document indexing and retrieval',
      icon: Database,
      status: 'configured',
      path: 'rag'
    },
    {
      title: 'Autonomy Settings',
      description: 'Agent autonomy and approval levels',
      icon: Shield,
      status: 'configured',
      path: 'autonomy'
    },
    {
      title: 'Tools Management',
      description: 'Configure automation tools',
      icon: Wrench,
      status: 'configured',
      path: 'tools'
    },
    {
      title: 'API Keys',
      description: 'External service integrations',
      icon: Key,
      status: localStorage.getItem('perplexity_api_key') ? 'configured' : 'needs-setup',
      path: 'api'
    },
    {
      title: 'LLM Configuration',
      description: 'Language model settings',
      icon: Server,
      status: 'configured',
      path: 'llm'
    },
    {
      title: 'Backup & Export',
      description: 'Data backup and migration',
      icon: Download,
      status: 'configured',
      path: 'backup'
    },
    {
      title: 'Notifications',
      description: 'Alert preferences',
      icon: Bell,
      status: 'configured',
      path: 'notifications'
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'configured':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Ready</Badge>;
      case 'needs-setup':
        return <Badge variant="outline"><AlertCircle className="w-3 h-3 mr-1" />Setup Required</Badge>;
      default:
        return <Badge variant="secondary">Unknown</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <SettingsIcon className="w-5 h-5" />
            System Overview
          </CardTitle>
          <CardDescription>Current system status and quick access to settings</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between p-4 rounded-lg border">
            <div className="flex items-center gap-3">
              <div className={`w-3 h-3 rounded-full ${
                systemHealth.status === 'good' ? 'bg-green-500' :
                systemHealth.status === 'partial' ? 'bg-yellow-500' : 'bg-red-500'
              }`} />
              <div>
                <h4 className="font-medium">System Health</h4>
                <p className="text-sm text-muted-foreground">{systemHealth.message}</p>
              </div>
            </div>
            <Badge className={
              systemHealth.status === 'good' ? 'bg-green-100 text-green-800' :
              systemHealth.status === 'partial' ? 'bg-yellow-100 text-yellow-800' :
              'bg-red-100 text-red-800'
            }>
              {systemHealth.status === 'good' ? 'Operational' :
               systemHealth.status === 'partial' ? 'Partial' : 'Needs Attention'}
            </Badge>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Configuration Sections</CardTitle>
          <CardDescription>Manage all aspects of your AI-powered CRM</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {settingsSections.map((section) => {
              const IconComponent = section.icon;
              return (
                <div key={section.path} className="p-4 rounded-lg border hover:shadow-md transition-shadow">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        <IconComponent className="w-4 h-4 text-blue-600" />
                      </div>
                      <div>
                        <h4 className="font-medium text-sm">{section.title}</h4>
                      </div>
                    </div>
                    {getStatusBadge(section.status)}
                  </div>
                  <p className="text-xs text-muted-foreground mb-3">{section.description}</p>
                  <Button 
                    size="sm" 
                    variant="outline" 
                    className="w-full"
                    onClick={() => {
                      const tabTrigger = document.querySelector(`[value="${section.path}"]`) as HTMLElement;
                      tabTrigger?.click();
                    }}
                  >
                    Configure
                  </Button>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
