import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Brain, Zap, DollarSign, Shield, Server, Activity, Plus, Edit, Trash2, TestTube } from 'lucide-react';
import { useEnhancedAI } from '@/contexts/EnhancedAIContext';
import { RoutingProfile, ProviderConfig } from '@/types/provider';
import { ProviderConfigDialog } from './ProviderConfigDialog';
import { useToast } from '@/hooks/use-toast';

export const EnhancedProviderManagement: React.FC = () => {
  const { router, currentProfile, setRoutingProfile, getUsageStats } = useEnhancedAI();
  const [usageStats, setUsageStats] = useState<Map<string, any>>(new Map());
  const [refreshKey, setRefreshKey] = useState(0);
  const [configDialogOpen, setConfigDialogOpen] = useState(false);
  const [editingProvider, setEditingProvider] = useState<ProviderConfig | undefined>();
  const { toast } = useToast();

  useEffect(() => {
    setUsageStats(getUsageStats());
  }, [refreshKey, getUsageStats]);

  const handleProfileChange = (profile: RoutingProfile) => {
    setRoutingProfile(profile);
    toast({
      title: "Routing Profile Updated",
      description: `Switched to ${profile} optimization mode`
    });
  };

  const handleAddProvider = () => {
    setEditingProvider(undefined);
    setConfigDialogOpen(true);
  };

  const handleEditProvider = (provider: ProviderConfig) => {
    setEditingProvider(provider);
    setConfigDialogOpen(true);
  };

  const handleSaveProvider = (config: ProviderConfig) => {
    router['configManager'].updateConfig(config);
    setRefreshKey(prev => prev + 1);
  };

  const handleDeleteProvider = (providerId: string) => {
    router['configManager'].removeConfig(providerId);
    setRefreshKey(prev => prev + 1);
    toast({
      title: "Provider Deleted",
      description: "Provider configuration has been removed"
    });
  };

  const testProvider = async (provider: ProviderConfig) => {
    toast({
      title: "Testing Provider",
      description: `Testing connection to ${provider.name}...`
    });
    
    // Mock test - in real implementation, this would test actual connectivity
    setTimeout(() => {
      toast({
        title: "Connection Test",
        description: `${provider.name} is ${Math.random() > 0.5 ? 'working correctly' : 'unavailable'}`
      });
    }, 2000);
  };

  const refreshStats = () => {
    setRefreshKey(prev => prev + 1);
  };

  const getProviderStatusColor = (providerId: string) => {
    const quota = router.getRemainingQuota(providerId);
    if (!quota) return 'gray';
    
    const requestsRemaining = quota.requestsPerMinute;
    if (requestsRemaining > 10) return 'green';
    if (requestsRemaining > 0) return 'yellow';
    return 'red';
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="w-5 h-5" />
            Enhanced AI Provider Management
          </CardTitle>
          <CardDescription>
            Configure and monitor intelligent routing between AI providers
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium">Routing Profile</label>
                <p className="text-xs text-muted-foreground">Controls how providers are selected</p>
              </div>
              <Select value={currentProfile} onValueChange={handleProfileChange}>
                <SelectTrigger className="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="privacy">Privacy First</SelectItem>
                  <SelectItem value="efficiency">Efficiency First</SelectItem>
                  <SelectItem value="cost">Cost Optimized</SelectItem>
                  <SelectItem value="balanced">Balanced</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {[
                { profile: 'privacy', icon: Shield, color: 'blue', description: 'Prioritizes local and privacy-focused providers' },
                { profile: 'efficiency', icon: Zap, color: 'green', description: 'Optimizes for speed and reliability' },
                { profile: 'cost', icon: DollarSign, color: 'yellow', description: 'Minimizes costs using free tiers first' },
                { profile: 'balanced', icon: Brain, color: 'purple', description: 'Balanced approach across all factors' }
              ].map(({ profile, icon: Icon, color, description }) => (
                <Card key={profile} className={`p-4 ${currentProfile === profile ? 'ring-2 ring-primary' : ''}`}>
                  <div className="flex items-center gap-2 mb-2">
                    <Icon className={`w-4 h-4 text-${color}-500`} />
                    <span className="text-sm font-medium capitalize">{profile}</span>
                  </div>
                  <p className="text-xs text-muted-foreground">{description}</p>
                </Card>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="providers">
        <TabsList>
          <TabsTrigger value="providers">Providers</TabsTrigger>
          <TabsTrigger value="usage">Usage Analytics</TabsTrigger>
          <TabsTrigger value="routing">Routing Logic</TabsTrigger>
        </TabsList>

        <TabsContent value="providers" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">Configured Providers</h3>
            <Button onClick={handleAddProvider}>
              <Plus className="w-4 h-4 mr-2" />
              Add Provider
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {router['configManager'].getAllConfigs().map((provider) => {
              const usage = usageStats.get(provider.id);
              const quota = router.getRemainingQuota(provider.id);
              const statusColor = getProviderStatusColor(provider.id);
              
              return (
                <Card key={provider.id}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-base">{provider.name}</CardTitle>
                      <div className="flex items-center gap-2">
                        {provider.isLocal && <Badge variant="outline">Local</Badge>}
                        <div className={`w-3 h-3 rounded-full bg-${statusColor}-500`} />
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Privacy:</span>
                        <div className="font-medium">{provider.privacyScore}/10</div>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Cost:</span>
                        <div className="font-medium">{provider.costPerRequest}¢</div>
                      </div>
                    </div>
                    
                    {quota && (
                      <div className="space-y-2">
                        <div className="flex justify-between text-xs">
                          <span>Requests/min</span>
                          <span>{quota.requestsPerMinute}/{provider.rateLimits.requestsPerMinute}</span>
                        </div>
                        <Progress 
                          value={(quota.requestsPerMinute / provider.rateLimits.requestsPerMinute) * 100} 
                          className="h-1"
                        />
                      </div>
                    )}
                    
                    <div className="flex flex-wrap gap-1">
                      {provider.capabilities.map((cap) => (
                        <Badge key={cap} variant="secondary" className="text-xs">
                          {cap}
                        </Badge>
                      ))}
                    </div>

                    <div className="flex gap-2">
                      <Button size="sm" variant="outline" onClick={() => testProvider(provider)}>
                        <TestTube className="w-3 h-3 mr-1" />
                        Test
                      </Button>
                      <Button size="sm" variant="outline" onClick={() => handleEditProvider(provider)}>
                        <Edit className="w-3 h-3 mr-1" />
                        Edit
                      </Button>
                      <Button size="sm" variant="outline" onClick={() => handleDeleteProvider(provider.id)}>
                        <Trash2 className="w-3 h-3 mr-1" />
                        Delete
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        <TabsContent value="usage">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Usage Statistics</CardTitle>
                <Button onClick={refreshStats} variant="outline" size="sm">
                  <Activity className="w-4 h-4 mr-2" />
                  Refresh
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {usageStats.size === 0 ? (
                <p className="text-muted-foreground text-center py-8">
                  No usage data available yet. Start making AI requests to see statistics.
                </p>
              ) : (
                <div className="space-y-4">
                  {Array.from(usageStats.entries()).map(([providerId, usage]) => (
                    <div key={providerId} className="border rounded-lg p-4">
                      <h4 className="font-medium mb-2">{providerId}</h4>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="text-muted-foreground">Today:</span>
                          <div className="font-medium">{usage.requestsToday} requests</div>
                        </div>
                        <div>
                          <span className="text-muted-foreground">This minute:</span>
                          <div className="font-medium">{usage.requestsThisMinute} requests</div>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Tokens used:</span>
                          <div className="font-medium">{usage.tokensToday.toLocaleString()}</div>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Total cost:</span>
                          <div className="font-medium">${usage.totalCost.toFixed(2)}</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="routing">
          <Card>
            <CardHeader>
              <CardTitle>Routing Logic</CardTitle>
              <CardDescription>
                How the system selects providers based on your current profile: {currentProfile}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card className="p-4">
                    <h4 className="font-medium mb-2">Selection Criteria</h4>
                    <ul className="text-sm space-y-1 text-muted-foreground">
                      <li>• Task capability match</li>
                      <li>• Rate limit availability</li>
                      <li>• Cost constraints</li>
                      <li>• Data sensitivity requirements</li>
                    </ul>
                  </Card>
                  
                  <Card className="p-4">
                    <h4 className="font-medium mb-2">Scoring Factors</h4>
                    <ul className="text-sm space-y-1 text-muted-foreground">
                      <li>• Privacy score (0-100)</li>
                      <li>• Efficiency rating</li>
                      <li>• Cost optimization</li>
                      <li>• Specialization bonus</li>
                    </ul>
                  </Card>
                  
                  <Card className="p-4">
                    <h4 className="font-medium mb-2">Fallback Strategy</h4>
                    <ul className="text-sm space-y-1 text-muted-foreground">
                      <li>• Auto-retry on failure</li>
                      <li>• Alternative provider selection</li>
                      <li>• Graceful degradation</li>
                      <li>• Error reporting</li>
                    </ul>
                  </Card>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <ProviderConfigDialog
        open={configDialogOpen}
        onOpenChange={setConfigDialogOpen}
        provider={editingProvider}
        onSave={handleSaveProvider}
      />
    </div>
  );
};
