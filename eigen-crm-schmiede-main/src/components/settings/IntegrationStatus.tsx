
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Zap, 
  RefreshCw,
  AlertTriangle
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Integration, ConnectionTest } from '@/types/integration';
import { IntegrationHealthPanel } from './integration/IntegrationHealthPanel';
import { IntegrationOverviewTab } from './integration/IntegrationOverviewTab';
import { IntegrationDetailsTab } from './integration/IntegrationDetailsTab';
import { IntegrationLogsTab } from './integration/IntegrationLogsTab';
import { simulateConnectionTest } from './integration/connectionTestService';
import { initializeIntegrations } from './integration/integrationData';

export const IntegrationStatus: React.FC = () => {
  const [integrations, setIntegrations] = useState<Integration[]>([]);
  const [connectionTests, setConnectionTests] = useState<Map<string, ConnectionTest>>(new Map());
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const { toast } = useToast();

  useEffect(() => {
    setIntegrations(initializeIntegrations());
  }, []);

  const testConnection = async (integration: Integration) => {
    const testId = integration.id;
    
    setConnectionTests(prev => new Map(prev.set(testId, {
      integrationId: testId,
      isRunning: true
    })));

    // Update integration status to testing
    setIntegrations(prev => prev.map(int => 
      int.id === testId ? { ...int, status: 'testing' } : int
    ));

    toast({
      title: "Testing Connection",
      description: `Testing connection to ${integration.name}...`
    });

    try {
      // Simulate connection test based on integration type
      const testResult = await simulateConnectionTest(integration);
      
      setConnectionTests(prev => new Map(prev.set(testId, {
        integrationId: testId,
        isRunning: false,
        result: testResult.success ? 'success' : 'failed',
        responseTime: testResult.responseTime,
        error: testResult.error
      })));

      // Update integration status and response time
      setIntegrations(prev => prev.map(int => 
        int.id === testId ? { 
          ...int, 
          status: testResult.success ? 'connected' : 'error',
          responseTime: testResult.responseTime,
          errorMessage: testResult.error,
          lastSync: testResult.success ? new Date() : int.lastSync
        } : int
      ));

      toast({
        title: testResult.success ? "Connection Successful" : "Connection Failed",
        description: testResult.success 
          ? `${integration.name} is working correctly (${testResult.responseTime}ms)`
          : `${integration.name} connection failed: ${testResult.error}`,
        variant: testResult.success ? "default" : "destructive"
      });

    } catch (error) {
      setConnectionTests(prev => new Map(prev.set(testId, {
        integrationId: testId,
        isRunning: false,
        result: 'failed',
        error: 'Unexpected error occurred'
      })));

      setIntegrations(prev => prev.map(int => 
        int.id === testId ? { 
          ...int, 
          status: 'error',
          errorMessage: 'Unexpected error occurred'
        } : int
      ));

      toast({
        title: "Test Failed",
        description: `Failed to test ${integration.name}`,
        variant: "destructive"
      });
    }
  };

  const handleRefreshAll = async () => {
    setIsRefreshing(true);
    
    // Test all connected integrations
    const connectedIntegrations = integrations.filter(int => int.status === 'connected');
    
    for (const integration of connectedIntegrations) {
      await testConnection(integration);
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 200));
    }
    
    setLastRefresh(new Date());
    setIsRefreshing(false);
    
    toast({
      title: "Refresh Complete",
      description: "All integration statuses have been updated",
    });
  };

  const handleConfigure = (integration: Integration) => {
    if (integration.configUrl) {
      const tabTrigger = document.querySelector(`[value="${integration.configUrl}"]`) as HTMLElement;
      tabTrigger?.click();
      
      toast({
        title: "Navigation",
        description: `Switched to ${integration.configUrl} settings`,
      });
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Zap className="w-5 h-5" />
                Integration Status
              </CardTitle>
              <CardDescription>
                Monitor and manage external service integrations
              </CardDescription>
            </div>
            <Button 
              variant="outline" 
              size="sm"
              onClick={handleRefreshAll}
              disabled={isRefreshing}
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
              Refresh All
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <IntegrationHealthPanel 
            integrations={integrations} 
            lastRefresh={lastRefresh} 
          />

          <Tabs defaultValue="overview" className="space-y-4">
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="details">Detailed Status</TabsTrigger>
              <TabsTrigger value="logs">Connection Logs</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <IntegrationOverviewTab
                integrations={integrations}
                connectionTests={connectionTests}
                onTestConnection={testConnection}
                onConfigure={handleConfigure}
              />
            </TabsContent>

            <TabsContent value="details" className="space-y-4">
              <IntegrationDetailsTab
                integrations={integrations}
                connectionTests={connectionTests}
              />
            </TabsContent>

            <TabsContent value="logs" className="space-y-4">
              <IntegrationLogsTab
                integrations={integrations}
                connectionTests={connectionTests}
              />
            </TabsContent>
          </Tabs>

          {integrations.some(i => i.status === 'error') && (
            <Alert className="mt-4">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Some integrations are experiencing issues. Check the configuration and API keys for affected services.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
