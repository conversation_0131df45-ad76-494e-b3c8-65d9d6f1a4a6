
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Bell, Volume2, VolumeX } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface NotificationPreferences {
  pushEnabled: boolean;
  soundEnabled: boolean;
  frequency: 'all' | 'important' | 'none';
  types: {
    research: boolean;
    deals: boolean;
    calls: boolean;
    leads: boolean;
    agents: boolean;
    errors: boolean;
  };
}

export const NotificationSettings: React.FC = () => {
  const [preferences, setPreferences] = useState<NotificationPreferences>({
    pushEnabled: true,
    soundEnabled: true,
    frequency: 'important',
    types: {
      research: true,
      deals: true,
      calls: true,
      leads: true,
      agents: false,
      errors: true,
    }
  });
  const [hasPermission, setHasPermission] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    // Load saved preferences
    const saved = localStorage.getItem('notification_preferences');
    if (saved) {
      setPreferences(JSON.parse(saved));
    }

    // Check notification permission
    if ('Notification' in window) {
      setHasPermission(Notification.permission === 'granted');
    }
  }, []);

  const requestPermission = async () => {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      setHasPermission(permission === 'granted');
      
      if (permission === 'granted') {
        toast({
          title: "Notifications Enabled",
          description: "You'll now receive push notifications for important events.",
        });
      }
    }
  };

  const testNotification = () => {
    if (hasPermission) {
      new Notification('CRM Test Notification', {
        body: 'This is a test notification from your AI-powered CRM.',
        icon: '/favicon.ico'
      });
    } else {
      toast({
        title: "Test Notification",
        description: "This would be a push notification if permissions were granted.",
      });
    }
  };

  const updatePreference = (key: keyof NotificationPreferences, value: any) => {
    const updated = { ...preferences, [key]: value };
    setPreferences(updated);
    localStorage.setItem('notification_preferences', JSON.stringify(updated));
  };

  const updateTypePreference = (type: keyof NotificationPreferences['types'], value: boolean) => {
    const updated = {
      ...preferences,
      types: { ...preferences.types, [type]: value }
    };
    setPreferences(updated);
    localStorage.setItem('notification_preferences', JSON.stringify(updated));
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Notification Preferences</CardTitle>
        <CardDescription>Configure when and how you receive updates</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {!hasPermission && (
          <div className="p-4 rounded-lg bg-yellow-50 border border-yellow-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-yellow-800 font-medium">
                  Push notifications are disabled
                </p>
                <p className="text-sm text-yellow-600">
                  Enable browser notifications to stay updated on important events
                </p>
              </div>
              <Button onClick={requestPermission} size="sm">
                Enable
              </Button>
            </div>
          </div>
        )}

        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label>Push Notifications</Label>
            <p className="text-sm text-muted-foreground">
              Receive browser notifications for important events
            </p>
          </div>
          <Switch
            checked={preferences.pushEnabled && hasPermission}
            onCheckedChange={(value) => updatePreference('pushEnabled', value)}
            disabled={!hasPermission}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-0.5 flex items-center gap-2">
            {preferences.soundEnabled ? <Volume2 className="w-4 h-4" /> : <VolumeX className="w-4 h-4" />}
            <div>
              <Label>Sound Alerts</Label>
              <p className="text-sm text-muted-foreground">
                Play sound when notifications arrive
              </p>
            </div>
          </div>
          <Switch
            checked={preferences.soundEnabled}
            onCheckedChange={(value) => updatePreference('soundEnabled', value)}
          />
        </div>

        <div className="space-y-2">
          <Label>Notification Frequency</Label>
          <Select 
            value={preferences.frequency} 
            onValueChange={(value) => updatePreference('frequency', value)}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All notifications</SelectItem>
              <SelectItem value="important">Important only</SelectItem>
              <SelectItem value="none">None</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-4">
          <h4 className="font-medium">Notification Types</h4>
          <div className="space-y-3">
            {Object.entries({
              research: 'AI research completed',
              deals: 'Deal stage changes',
              calls: 'AI call completed',
              leads: 'New lead qualified',
              agents: 'Agent status changes',
              errors: 'System errors'
            }).map(([key, label]) => (
              <div key={key} className="flex items-center justify-between">
                <Label className="text-sm">{label}</Label>
                <Switch
                  checked={preferences.types[key as keyof NotificationPreferences['types']]}
                  onCheckedChange={(value) => updateTypePreference(key as keyof NotificationPreferences['types'], value)}
                />
              </div>
            ))}
          </div>
        </div>

        <div className="pt-4 border-t">
          <Button onClick={testNotification} variant="outline" className="w-full">
            <Bell className="w-4 h-4 mr-2" />
            Test Notification
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
