
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Shield, Lock, Eye, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';

interface SecuritySetting {
  id: string;
  title: string;
  description: string;
  enabled: boolean;
  severity: 'low' | 'medium' | 'high';
}

export const SecurityEnhancements = () => {
  const [securitySettings, setSecuritySettings] = useState<SecuritySetting[]>([
    {
      id: 'two-factor',
      title: 'Two-Factor Authentication',
      description: 'Require 2FA for all user accounts',
      enabled: true,
      severity: 'high'
    },
    {
      id: 'session-timeout',
      title: 'Session Timeout',
      description: 'Automatically log out inactive users after 30 minutes',
      enabled: true,
      severity: 'medium'
    },
    {
      id: 'audit-logging',
      title: 'Audit Logging',
      description: 'Log all user actions for security monitoring',
      enabled: true,
      severity: 'high'
    },
    {
      id: 'ip-whitelist',
      title: 'IP Whitelisting',
      description: 'Restrict access to specific IP addresses',
      enabled: false,
      severity: 'medium'
    },
    {
      id: 'data-encryption',
      title: 'Data Encryption',
      description: 'Encrypt sensitive data at rest',
      enabled: true,
      severity: 'high'
    },
    {
      id: 'password-policy',
      title: 'Strong Password Policy',
      description: 'Enforce complex password requirements',
      enabled: true,
      severity: 'medium'
    }
  ]);

  const toggleSetting = (id: string) => {
    setSecuritySettings(prev => 
      prev.map(setting => 
        setting.id === id 
          ? { ...setting, enabled: !setting.enabled }
          : setting
      )
    );
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const securityScore = Math.round(
    (securitySettings.filter(s => s.enabled).length / securitySettings.length) * 100
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-red-50 to-orange-50 p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-slate-900">Security Center</h1>
            <p className="text-slate-600 mt-2">Manage security settings and monitoring</p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-slate-900">{securityScore}%</div>
            <div className="text-sm text-slate-600">Security Score</div>
          </div>
        </div>

        {/* Security Score Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="h-5 w-5" />
              <span>Security Overview</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                {securityScore >= 80 ? (
                  <CheckCircle className="h-6 w-6 text-green-500" />
                ) : securityScore >= 60 ? (
                  <AlertTriangle className="h-6 w-6 text-yellow-500" />
                ) : (
                  <XCircle className="h-6 w-6 text-red-500" />
                )}
                <span className="font-medium">
                  {securityScore >= 80 ? 'Good' : securityScore >= 60 ? 'Fair' : 'Poor'} Security Posture
                </span>
              </div>
              <Badge variant={securityScore >= 80 ? 'default' : 'destructive'}>
                {securitySettings.filter(s => s.enabled).length} of {securitySettings.length} enabled
              </Badge>
            </div>
            
            <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
              <div 
                className={`h-3 rounded-full transition-all ${
                  securityScore >= 80 ? 'bg-green-500' : 
                  securityScore >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                }`}
                style={{ width: `${securityScore}%` }}
              />
            </div>

            {securityScore < 80 && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  Your security score is below recommended levels. Consider enabling additional security features.
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>

        {/* Security Settings */}
        <div className="grid gap-4">
          {securitySettings.map((setting) => (
            <Card key={setting.id}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <Lock className="h-5 w-5 text-slate-600" />
                      <h3 className="font-medium text-slate-900">{setting.title}</h3>
                      <Badge className={getSeverityColor(setting.severity)}>
                        {setting.severity}
                      </Badge>
                    </div>
                    <p className="text-sm text-slate-600">{setting.description}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Label htmlFor={setting.id} className="text-sm">
                      {setting.enabled ? 'Enabled' : 'Disabled'}
                    </Label>
                    <Switch
                      id={setting.id}
                      checked={setting.enabled}
                      onCheckedChange={() => toggleSetting(setting.id)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Security Recommendations */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Eye className="h-5 w-5" />
              <span>Security Recommendations</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                <div>
                  <p className="font-medium">Regular Security Updates</p>
                  <p className="text-sm text-slate-600">Keep all dependencies and security patches up to date</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                <div>
                  <p className="font-medium">Access Control</p>
                  <p className="text-sm text-slate-600">Implement role-based access control (RBAC)</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                <div>
                  <p className="font-medium">Data Backup</p>
                  <p className="text-sm text-slate-600">Regular automated backups with encryption</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
