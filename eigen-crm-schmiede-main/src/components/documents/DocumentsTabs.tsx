
import React from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { FileManager } from './FileManager';
import { DocumentVersions } from './DocumentVersions';
import { SharedDocuments } from './SharedDocuments';
import { DocumentApprovals } from './DocumentApprovals';

interface DocumentsTabsProps {
  activeTab: string;
  onTabChange: (value: string) => void;
}

export const DocumentsTabs = ({ activeTab, onTabChange }: DocumentsTabsProps) => {
  return (
    <Tabs value={activeTab} onValueChange={onTabChange} className="space-y-6">
      <TabsList className="bg-white/80 backdrop-blur-sm border border-white/20 shadow-lg p-1 rounded-xl">
        <TabsTrigger value="files" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-600 data-[state=active]:to-indigo-600 data-[state=active]:text-white rounded-lg">
          File Manager
        </TabsTrigger>
        <TabsTrigger value="versions" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-600 data-[state=active]:to-indigo-600 data-[state=active]:text-white rounded-lg">
          Version Control
        </TabsTrigger>
        <TabsTrigger value="shared" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-600 data-[state=active]:to-indigo-600 data-[state=active]:text-white rounded-lg">
          Shared Files
        </TabsTrigger>
        <TabsTrigger value="approvals" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-600 data-[state=active]:to-indigo-600 data-[state=active]:text-white rounded-lg">
          Approvals
        </TabsTrigger>
      </TabsList>

      <TabsContent value="files" className="space-y-6">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl p-6">
          <FileManager />
        </div>
      </TabsContent>

      <TabsContent value="versions" className="space-y-6">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl p-6">
          <DocumentVersions />
        </div>
      </TabsContent>

      <TabsContent value="shared" className="space-y-6">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl p-6">
          <SharedDocuments />
        </div>
      </TabsContent>

      <TabsContent value="approvals" className="space-y-6">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl p-6">
          <DocumentApprovals />
        </div>
      </TabsContent>
    </Tabs>
  );
};
