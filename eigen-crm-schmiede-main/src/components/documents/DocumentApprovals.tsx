
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { 
  CheckCircle, 
  XCircle,
  Clock,
  Eye,
  MessageSquare,
  FileText,
  User
} from 'lucide-react';

interface DocumentApproval {
  id: string;
  documentName: string;
  submitter: string;
  submittedDate: string;
  status: 'pending' | 'approved' | 'rejected' | 'changes-requested';
  approvers: {
    name: string;
    status: 'pending' | 'approved' | 'rejected';
    date?: string;
    comments?: string;
  }[];
  priority: 'low' | 'medium' | 'high';
  dueDate?: string;
}

const mockApprovals: DocumentApproval[] = [
  {
    id: '1',
    documentName: 'Updated_Safety_Protocol.pdf',
    submitter: '<PERSON>',
    submittedDate: '2024-01-20',
    status: 'pending',
    priority: 'high',
    dueDate: '2024-01-25',
    approvers: [
      { name: '<PERSON>', status: 'approved', date: '2024-01-21' },
      { name: '<PERSON>', status: 'pending' },
      { name: '<PERSON>', status: 'pending' }
    ]
  },
  {
    id: '2',
    documentName: 'Q1_Budget_Proposal.xlsx',
    submitter: 'Maria Garcia',
    submittedDate: '2024-01-19',
    status: 'changes-requested',
    priority: 'medium',
    approvers: [
      { name: 'David Lee', status: 'rejected', date: '2024-01-20', comments: 'Need more detail on marketing expenses' },
      { name: 'Lisa Chen', status: 'pending' }
    ]
  }
];

export const DocumentApprovals = () => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'changes-requested': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getApprovalIcon = (status: string) => {
    switch (status) {
      case 'approved': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'rejected': return <XCircle className="h-4 w-4 text-red-600" />;
      case 'pending': return <Clock className="h-4 w-4 text-yellow-600" />;
      default: return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold">Document Approvals</h2>
        <p className="text-muted-foreground">Review and approve document submissions</p>
      </div>

      {/* Approval Queue */}
      <div className="space-y-4">
        {mockApprovals.map((approval) => (
          <Card key={approval.id} className="hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <FileText className="h-5 w-5 text-blue-600" />
                    <h3 className="text-lg font-semibold">{approval.documentName}</h3>
                    <Badge className={getStatusColor(approval.status)}>
                      {approval.status.replace('-', ' ')}
                    </Badge>
                    <Badge className={getPriorityColor(approval.priority)}>
                      {approval.priority} priority
                    </Badge>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-gray-500" />
                      <div>
                        <p className="text-sm font-medium">Submitted by</p>
                        <p className="text-sm text-muted-foreground">{approval.submitter}</p>
                      </div>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Submitted Date</p>
                      <p className="text-sm text-muted-foreground">{new Date(approval.submittedDate).toLocaleDateString()}</p>
                    </div>
                    {approval.dueDate && (
                      <div>
                        <p className="text-sm font-medium">Due Date</p>
                        <p className="text-sm text-muted-foreground">{new Date(approval.dueDate).toLocaleDateString()}</p>
                      </div>
                    )}
                  </div>

                  <div className="mb-4">
                    <h4 className="font-medium mb-2">Approval Status:</h4>
                    <div className="space-y-2">
                      {approval.approvers.map((approver, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center gap-3">
                            <Avatar className="h-8 w-8">
                              <AvatarFallback className="text-xs">
                                {approver.name.split(' ').map(n => n[0]).join('')}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-medium">{approver.name}</p>
                              {approver.date && (
                                <p className="text-xs text-muted-foreground">{new Date(approver.date).toLocaleDateString()}</p>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            {getApprovalIcon(approver.status)}
                            <Badge variant="outline" className={getStatusColor(approver.status)}>
                              {approver.status}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Comments */}
                  {approval.approvers.some(a => a.comments) && (
                    <div className="mb-4">
                      <h4 className="font-medium mb-2">Comments:</h4>
                      {approval.approvers.filter(a => a.comments).map((approver, index) => (
                        <div key={index} className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                          <p className="font-medium text-sm">{approver.name}:</p>
                          <p className="text-sm text-gray-700">{approver.comments}</p>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <Eye className="h-4 w-4 mr-1" />
                  View Document
                </Button>
                <Button variant="outline" size="sm" className="text-green-600 border-green-200 hover:bg-green-50">
                  <CheckCircle className="h-4 w-4 mr-1" />
                  Approve
                </Button>
                <Button variant="outline" size="sm" className="text-red-600 border-red-200 hover:bg-red-50">
                  <XCircle className="h-4 w-4 mr-1" />
                  Reject
                </Button>
                <Button variant="outline" size="sm">
                  <MessageSquare className="h-4 w-4 mr-1" />
                  Add Comment
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Approval Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6 text-center">
            <Clock className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
            <p className="text-2xl font-bold">5</p>
            <p className="text-sm text-muted-foreground">Pending</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6 text-center">
            <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <p className="text-2xl font-bold">23</p>
            <p className="text-sm text-muted-foreground">Approved</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6 text-center">
            <XCircle className="h-8 w-8 text-red-600 mx-auto mb-2" />
            <p className="text-2xl font-bold">2</p>
            <p className="text-sm text-muted-foreground">Rejected</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6 text-center">
            <MessageSquare className="h-8 w-8 text-orange-600 mx-auto mb-2" />
            <p className="text-2xl font-bold">3</p>
            <p className="text-sm text-muted-foreground">Changes Requested</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
