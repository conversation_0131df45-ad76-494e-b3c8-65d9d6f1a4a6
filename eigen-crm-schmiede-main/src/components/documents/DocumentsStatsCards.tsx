
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { 
  FileText, 
  Share2, 
  HardDrive,
  Upload,
  Clock
} from 'lucide-react';

interface DocumentsStatsCardsProps {
  totalDocuments: number;
  sharedDocuments: number;
  storageUsed: number;
  recentUploads: number;
  pendingApprovals: number;
}

export const DocumentsStatsCards = ({
  totalDocuments,
  sharedDocuments,
  storageUsed,
  recentUploads,
  pendingApprovals
}: DocumentsStatsCardsProps) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
      <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-600 text-sm font-medium">Total Documents</p>
              <p className="text-3xl font-bold text-slate-900">{totalDocuments}</p>
            </div>
            <div className="bg-gradient-to-r from-blue-100 to-indigo-100 p-3 rounded-xl">
              <FileText className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-600 text-sm font-medium">Shared Files</p>
              <p className="text-3xl font-bold text-slate-900">{sharedDocuments}</p>
            </div>
            <div className="bg-gradient-to-r from-green-100 to-emerald-100 p-3 rounded-xl">
              <Share2 className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-600 text-sm font-medium">Storage Used</p>
              <p className="text-3xl font-bold text-slate-900">{storageUsed}GB</p>
            </div>
            <div className="bg-gradient-to-r from-purple-100 to-violet-100 p-3 rounded-xl">
              <HardDrive className="h-6 w-6 text-purple-600" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-600 text-sm font-medium">Recent Uploads</p>
              <p className="text-3xl font-bold text-slate-900">{recentUploads}</p>
            </div>
            <div className="bg-gradient-to-r from-orange-100 to-red-100 p-3 rounded-xl">
              <Upload className="h-6 w-6 text-orange-600" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-600 text-sm font-medium">Pending Approvals</p>
              <p className="text-3xl font-bold text-slate-900">{pendingApprovals}</p>
            </div>
            <div className="bg-gradient-to-r from-yellow-100 to-amber-100 p-3 rounded-xl">
              <Clock className="h-6 w-6 text-yellow-600" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
