
import React from 'react';
import { useForm } from 'react-hook-form';
import { Card, CardContent } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { PersonFormData } from '@/types/person';
import { PersonFormHeader } from './PersonFormHeader';
import { PersonBasicInfoFields } from './PersonBasicInfoFields';
import { PersonAddressFields } from './PersonAddressFields';
import { PersonSocialFields } from './PersonSocialFields';
import { PersonFormActions } from './PersonFormActions';

interface PersonFormProps {
  onSubmit: (data: PersonFormData) => void;
  onCancel: () => void;
  initialData?: Partial<PersonFormData>;
  isEdit?: boolean;
}

export const PersonForm: React.FC<PersonFormProps> = ({
  onSubmit,
  onCancel,
  initialData,
  isEdit = false
}) => {
  const {
    register,
    handleSubmit,
    control,
    formState: { errors, isSubmitting }
  } = useForm<PersonFormData>({
    defaultValues: {
      firstName: initialData?.firstName || '',
      lastName: initialData?.lastName || '',
      email: initialData?.email || '',
      phone: initialData?.phone || '',
      position: initialData?.position || '',
      company: initialData?.company || '',
      type: initialData?.type || 'client',
      status: initialData?.status || 'active',
      notes: initialData?.notes || '',
      street: initialData?.street || '',
      city: initialData?.city || '',
      state: initialData?.state || '',
      zipCode: initialData?.zipCode || '',
      country: initialData?.country || '',
      linkedin: initialData?.linkedin || '',
      twitter: initialData?.twitter || '',
      website: initialData?.website || ''
    }
  });

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <PersonFormHeader isEdit={isEdit} onCancel={onCancel} />

      <Card>
        <CardContent className="p-6">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <Tabs defaultValue="basic" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="basic">Basic Info</TabsTrigger>
                <TabsTrigger value="address">Address</TabsTrigger>
                <TabsTrigger value="social">Social & Notes</TabsTrigger>
              </TabsList>

              <TabsContent value="basic" className="space-y-4">
                <PersonBasicInfoFields
                  register={register}
                  control={control}
                  errors={errors}
                />
              </TabsContent>

              <TabsContent value="address" className="space-y-4">
                <PersonAddressFields register={register} />
              </TabsContent>

              <TabsContent value="social" className="space-y-4">
                <PersonSocialFields register={register} />
              </TabsContent>
            </Tabs>

            <PersonFormActions
              isEdit={isEdit}
              isSubmitting={isSubmitting}
              onCancel={onCancel}
            />
          </form>
        </CardContent>
      </Card>
    </div>
  );
};
