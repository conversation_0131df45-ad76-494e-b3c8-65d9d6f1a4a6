
import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { UseFormRegister } from 'react-hook-form';
import { PersonFormData } from '@/types/person';

interface PersonSocialFieldsProps {
  register: UseFormRegister<PersonFormData>;
}

export const PersonSocialFields: React.FC<PersonSocialFieldsProps> = ({ register }) => {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="linkedin">LinkedIn</Label>
          <Input
            id="linkedin"
            placeholder="https://linkedin.com/in/..."
            {...register('linkedin')}
          />
        </div>

        <div>
          <Label htmlFor="twitter">Twitter</Label>
          <Input
            id="twitter"
            placeholder="https://twitter.com/..."
            {...register('twitter')}
          />
        </div>

        <div className="md:col-span-2">
          <Label htmlFor="website">Website</Label>
          <Input
            id="website"
            placeholder="https://..."
            {...register('website')}
          />
        </div>
      </div>

      <div>
        <Label htmlFor="notes">Notes</Label>
        <Textarea
          id="notes"
          placeholder="Additional notes about this contact..."
          rows={4}
          {...register('notes')}
        />
      </div>
    </div>
  );
};
