
import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Plus } from 'lucide-react';

interface PeopleHeaderProps {
  onAddPerson: () => void;
}

export const PeopleHeader: React.FC<PeopleHeaderProps> = ({ onAdd<PERSON>erson }) => {
  return (
    <div className="flex items-center justify-between">
      <div>
        <h1 className="text-3xl font-bold">People</h1>
        <p className="text-muted-foreground">Manage your contacts and relationships</p>
      </div>
      <Button onClick={onAddPerson} className="bg-gradient-to-r from-blue-600 to-indigo-600">
        <Plus className="h-4 w-4 mr-2" />
        Add Contact
      </Button>
    </div>
  );
};
