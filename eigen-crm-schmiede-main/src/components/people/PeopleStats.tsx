
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Users, UserCheck, Building } from 'lucide-react';
import { Person } from '@/types/person';

interface PeopleStatsProps {
  people: Person[];
}

export const PeopleStats: React.FC<PeopleStatsProps> = ({ people }) => {
  const totalPeople = people.length;
  const activePeople = people.filter(p => p.status === 'active').length;
  const clientsCount = people.filter(p => p.type === 'client').length;
  const employeesCount = people.filter(p => p.type === 'employee').length;
  const contractorsCount = people.filter(p => p.type === 'contractor').length;

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Total Contacts</p>
              <p className="text-2xl font-bold">{totalPeople}</p>
            </div>
            <Users className="h-8 w-8 text-blue-600" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Active Contacts</p>
              <p className="text-2xl font-bold">{activePeople}</p>
            </div>
            <UserCheck className="h-8 w-8 text-green-600" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Clients</p>
              <p className="text-2xl font-bold">{clientsCount}</p>
            </div>
            <Building className="h-8 w-8 text-purple-600" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Contractors</p>
              <p className="text-2xl font-bold">{contractorsCount}</p>
            </div>
            <UserCheck className="h-8 w-8 text-orange-600" />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
