
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Mail,
  Phone,
  Building,
  MapPin,
  Edit,
  Eye,
  Users,
  Calendar
} from 'lucide-react';
import { Person } from '@/types/person';

interface PersonCardDisplayProps {
  person: Person;
  onEdit: (person: Person) => void;
}

export const PersonCardDisplay: React.FC<PersonCardDisplayProps> = ({ person, onEdit }) => {
  const getTypeColor = (type: string) => {
    switch (type) {
      case 'client': return 'bg-blue-100 text-blue-800';
      case 'employee': return 'bg-green-100 text-green-800';
      case 'contractor': return 'bg-orange-100 text-orange-800';
      case 'contact': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardContent className="p-6">
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-4 flex-1">
            <Avatar className="h-12 w-12">
              <AvatarImage src={person.avatar} />
              <AvatarFallback>
                {person.firstName[0]}{person.lastName[0]}
              </AvatarFallback>
            </Avatar>
            
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-2">
                <h3 className="text-lg font-semibold">
                  {person.firstName} {person.lastName}
                </h3>
                <Badge className={getTypeColor(person.type)}>
                  {person.type}
                </Badge>
                <Badge className={getStatusColor(person.status)}>
                  {person.status}
                </Badge>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-3">
                <div className="flex items-center gap-2 text-sm">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <span>{person.email}</span>
                </div>
                {person.phone && (
                  <div className="flex items-center gap-2 text-sm">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span>{person.phone}</span>
                  </div>
                )}
                {person.company && (
                  <div className="flex items-center gap-2 text-sm">
                    <Building className="h-4 w-4 text-muted-foreground" />
                    <span>{person.company}</span>
                  </div>
                )}
                {person.position && (
                  <div className="flex items-center gap-2 text-sm">
                    <Users className="h-4 w-4 text-muted-foreground" />
                    <span>{person.position}</span>
                  </div>
                )}
              </div>

              {person.address?.city && (
                <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                  <MapPin className="h-4 w-4" />
                  <span>
                    {person.address.city}
                    {person.address.state && `, ${person.address.state}`}
                    {person.address.country && `, ${person.address.country}`}
                  </span>
                </div>
              )}

              {person.tags.length > 0 && (
                <div className="flex gap-1 flex-wrap mb-2">
                  {person.tags.map((tag, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              )}

              {person.notes && (
                <p className="text-sm text-muted-foreground">{person.notes}</p>
              )}

              {person.lastContact && (
                <div className="flex items-center gap-2 text-xs text-muted-foreground mt-2">
                  <Calendar className="h-3 w-3" />
                  <span>Last contact: {new Date(person.lastContact).toLocaleDateString()}</span>
                </div>
              )}
            </div>
          </div>
          
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <Eye className="h-4 w-4" />
            </Button>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => onEdit(person)}
            >
              <Edit className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
