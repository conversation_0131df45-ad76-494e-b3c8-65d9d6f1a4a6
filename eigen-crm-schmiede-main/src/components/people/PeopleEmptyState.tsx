
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Users, Plus } from 'lucide-react';

interface PeopleEmptyStateProps {
  onAddPerson: () => void;
}

export const PeopleEmptyState: React.FC<PeopleEmptyStateProps> = ({ onAddPerson }) => {
  return (
    <Card>
      <CardContent className="p-12 text-center">
        <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-lg font-semibold mb-2">No contacts found</h3>
        <p className="text-muted-foreground mb-4">
          Try adjusting your search criteria or add a new contact.
        </p>
        <Button onClick={onAddPerson}>
          <Plus className="h-4 w-4 mr-2" />
          Add Contact
        </Button>
      </CardContent>
    </Card>
  );
};
