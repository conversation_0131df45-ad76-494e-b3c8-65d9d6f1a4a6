
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Mic, MicOff, Volume2, VolumeX } from 'lucide-react';
import { nlProcessor } from '@/services/NLProcessor';
import { useToast } from '@/hooks/use-toast';

export const VoiceControl: React.FC = () => {
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [isSupported, setIsSupported] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [lastResponse, setLastResponse] = useState('');
  const { toast } = useToast();

  useEffect(() => {
    setIsSupported(nlProcessor.isVoiceSupported());
  }, []);

  const startListening = async () => {
    try {
      setIsListening(true);
      setTranscript('Listening...');
      
      const result = await nlProcessor.processVoiceInput();
      
      if (result) {
        setTranscript(result.entities.message || 'Voice input processed');
        setLastResponse(result.response || 'Command executed');
        
        // Speak the response
        if (result.response) {
          setIsSpeaking(true);
          await nlProcessor.speakResponse(result.response);
          setIsSpeaking(false);
        }

        toast({
          title: 'Voice Command Processed',
          description: `Intent: ${result.intent} (${Math.round(result.confidence * 100)}% confidence)`,
        });
      }
    } catch (error) {
      console.error('Voice processing error:', error);
      toast({
        title: 'Voice Error',
        description: error instanceof Error ? error.message : 'Voice processing failed',
        variant: 'destructive'
      });
    } finally {
      setIsListening(false);
    }
  };

  const stopListening = () => {
    setIsListening(false);
    setTranscript('');
  };

  if (!isSupported) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MicOff className="w-5 h-5 text-muted-foreground" />
            Voice Control
          </CardTitle>
          <CardDescription>Voice control is not supported in this browser</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Mic className="w-5 h-5 text-primary" />
          Voice Control
        </CardTitle>
        <CardDescription>Control your AI agents with natural speech</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-2">
          <Button
            variant={isListening ? "destructive" : "default"}
            onClick={isListening ? stopListening : startListening}
            disabled={isSpeaking}
            className="flex items-center gap-2"
          >
            {isListening ? (
              <>
                <MicOff className="w-4 h-4" />
                Stop Listening
              </>
            ) : (
              <>
                <Mic className="w-4 h-4" />
                Start Listening
              </>
            )}
          </Button>

          <Badge variant={isListening ? "default" : "secondary"}>
            {isListening ? 'Listening' : 'Ready'}
          </Badge>

          {isSpeaking && (
            <Badge variant="default" className="animate-pulse">
              <Volume2 className="w-3 h-3 mr-1" />
              Speaking
            </Badge>
          )}
        </div>

        {transcript && (
          <div className="p-3 bg-muted rounded-lg">
            <p className="text-sm font-medium text-muted-foreground mb-1">Transcript:</p>
            <p className="text-sm">{transcript}</p>
          </div>
        )}

        {lastResponse && (
          <div className="p-3 bg-blue-50 rounded-lg border-l-2 border-blue-500">
            <p className="text-sm font-medium text-blue-800 mb-1">AI Response:</p>
            <p className="text-sm text-blue-700">{lastResponse}</p>
          </div>
        )}

        <div className="text-xs text-muted-foreground">
          <p>• Say commands like "Research Apple" or "Show me leads"</p>
          <p>• AI will speak responses back to you</p>
          <p>• Click stop to end voice session</p>
        </div>
      </CardContent>
    </Card>
  );
};
