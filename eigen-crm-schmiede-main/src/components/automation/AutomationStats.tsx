
import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Activity, Bot, Zap, TrendingUp } from 'lucide-react'

interface AutomationStatsProps {
  activeWorkflows?: number
  aiWorkflows?: number
  totalExecutions?: number
  successRate?: number
  avgExecutionTime?: number
}

export const AutomationStats: React.FC<AutomationStatsProps> = ({
  activeWorkflows = 0,
  aiWorkflows = 0,
  totalExecutions = 0,
  successRate = 0,
  avgExecutionTime = 0
}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-sm font-medium">Active Workflows</CardTitle>
          <Activity className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{activeWorkflows}</div>
          <p className="text-xs text-muted-foreground">
            Currently running automations
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-sm font-medium">AI Workflows</CardTitle>
          <Bot className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{aiWorkflows}</div>
          <p className="text-xs text-muted-foreground">
            AI-powered automations
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-sm font-medium">Total Executions</CardTitle>
          <Zap className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{totalExecutions.toLocaleString()}</div>
          <p className="text-xs text-muted-foreground">
            All time executions
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
          <TrendingUp className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">
            {(successRate * 100).toFixed(1)}%
          </div>
          <Progress value={successRate * 100} className="mt-2" />
        </CardContent>
      </Card>
    </div>
  )
}
