
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Bot, Activity, Plus } from 'lucide-react';

interface AutomationHeaderProps {
  onRefresh: () => void;
  onCreateWorkflow: () => void;
}

export const AutomationHeader: React.FC<AutomationHeaderProps> = ({
  onRefresh,
  onCreateWorkflow
}) => {
  return (
    <div className="flex items-center justify-between">
      <div>
        <h1 className="text-3xl font-bold text-foreground flex items-center gap-2">
          <Bot className="w-8 h-8" />
          AI Revenue Automation Hub
        </h1>
        <p className="text-muted-foreground">
          Autonomous AI agents working 24/7 to accelerate your revenue
        </p>
      </div>
      <div className="flex items-center gap-2">
        <Button variant="outline" onClick={onRefresh}>
          <Activity className="w-4 h-4 mr-2" />
          Refresh AI Status
        </Button>
        <Button onClick={onCreateWorkflow}>
          <Plus className="w-4 h-4 mr-2" />
          Create AI Workflow
        </Button>
      </div>
    </div>
  );
};
