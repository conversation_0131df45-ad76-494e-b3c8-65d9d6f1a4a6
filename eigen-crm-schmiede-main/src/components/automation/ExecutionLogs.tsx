
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { CheckCircle, AlertCircle, Activity } from 'lucide-react';

export const ExecutionLogs: React.FC = () => {
  const executionLogs = [
    {
      id: '1',
      type: 'success',
      icon: CheckCircle,
      title: 'AI Voice Call Completed Successfully',
      description: 'Qualified lead at TechCorp Inc, identified $50k opportunity',
      timestamp: '2 min ago',
      details: 'Duration: 12 min | Sentiment: Positive | Next: Demo scheduled'
    },
    {
      id: '2',
      type: 'info',
      icon: Activity,
      title: 'Predictive Email Sequence Triggered',
      description: 'High-score lead detected, personalized sequence initiated',
      timestamp: '15 min ago',
      details: 'Lead Score: 89/100 | Personalization: Industry-specific content'
    },
    {
      id: '3',
      type: 'warning',
      icon: AlertCircle,
      title: 'Deal Risk Alert Generated',
      description: 'Enterprise Corp deal showing velocity decline',
      timestamp: '1 hour ago',
      details: 'Risk Level: Medium | Recommendation: Schedule stakeholder call'
    },
    {
      id: '4',
      type: 'success',
      icon: CheckCircle,
      title: 'Market Intelligence Update',
      description: 'Competitor pricing change detected, opportunities identified',
      timestamp: '2 hours ago',
      details: 'Competitor: SalesForce | Change: 15% price increase | Action: Leverage pricing advantage'
    }
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>AI Execution Intelligence</CardTitle>
        <CardDescription>Real-time AI agent activities and outcomes</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {executionLogs.map((log) => {
            const Icon = log.icon;
            return (
              <div key={log.id} className="flex items-start gap-3 p-3 border rounded-lg">
                <Icon className={`w-4 h-4 mt-0.5 ${
                  log.type === 'success' ? 'text-green-500' : 
                  log.type === 'warning' ? 'text-yellow-500' : 
                  'text-blue-500'
                }`} />
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium">{log.title}</p>
                    <span className="text-xs text-muted-foreground">{log.timestamp}</span>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">{log.description}</p>
                  <p className="text-xs text-blue-600 mt-2">{log.details}</p>
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};
