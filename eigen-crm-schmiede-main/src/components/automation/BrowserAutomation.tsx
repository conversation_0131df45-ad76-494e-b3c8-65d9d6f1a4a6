
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  Play, 
  Pause, 
  Trash2, 
  Plus, 
  Globe, 
  Clock, 
  Target,
  Eye,
  Download,
  AlertTriangle
} from 'lucide-react';
import { PlaywrightService, AutomationScript, AutomationResult } from '@/services/automation/PlaywrightService';
import { toast } from '@/components/ui/use-toast';

export const BrowserAutomation: React.FC = () => {
  const [scripts, setScripts] = useState<AutomationScript[]>([]);
  const [results, setResults] = useState<Map<string, AutomationResult>>(new Map());
  const [activeTab, setActiveTab] = useState('scripts');
  const [running, setRunning] = useState<Set<string>>(new Set());

  useEffect(() => {
    loadScripts();
  }, []);

  const loadScripts = () => {
    const loadedScripts = PlaywrightService.getScripts();
    setScripts(loadedScripts);
  };

  const handleRunScript = async (scriptId: string) => {
    setRunning(prev => new Set([...prev, scriptId]));
    
    try {
      const result = await PlaywrightService.executeScript(scriptId);
      setResults(prev => new Map(prev.set(scriptId, result)));
      
      toast({
        title: result.success ? "Script Executed Successfully" : "Script Failed",
        description: result.success 
          ? `Completed in ${result.duration}ms` 
          : result.error,
        variant: result.success ? "default" : "destructive"
      });
    } catch (error) {
      toast({
        title: "Execution Error",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive"
      });
    } finally {
      setRunning(prev => {
        const newSet = new Set(prev);
        newSet.delete(scriptId);
        return newSet;
      });
    }
  };

  const handleDeleteScript = (scriptId: string) => {
    PlaywrightService.deleteScript(scriptId);
    loadScripts();
    toast({
      title: "Script Deleted",
      description: "Automation script has been removed"
    });
  };

  const createFromTemplate = (template: any) => {
    const scriptId = PlaywrightService.createScript(template);
    loadScripts();
    toast({
      title: "Script Created",
      description: `${template.name} automation script created`
    });
  };

  const getStatusColor = (success?: boolean) => {
    if (success === undefined) return 'secondary';
    return success ? 'default' : 'destructive';
  };

  const formatDuration = (ms: number) => {
    return `${(ms / 1000).toFixed(1)}s`;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-foreground flex items-center gap-2">
            <Globe className="w-6 h-6" />
            Browser Automation
          </h2>
          <p className="text-muted-foreground">
            Automate web scraping and data extraction tasks
          </p>
        </div>
        <Button onClick={() => setActiveTab('templates')}>
          <Plus className="w-4 h-4 mr-2" />
          New Script
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="scripts">My Scripts</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="results">Results</TabsTrigger>
        </TabsList>

        <TabsContent value="scripts" className="space-y-4">
          {scripts.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Globe className="w-12 h-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No Scripts Yet</h3>
                <p className="text-muted-foreground text-center mb-4">
                  Create your first browser automation script from a template
                </p>
                <Button onClick={() => setActiveTab('templates')}>
                  Browse Templates
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {scripts.map((script) => {
                const result = results.get(script.id);
                const isRunning = running.has(script.id);
                
                return (
                  <Card key={script.id}>
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div>
                          <CardTitle className="text-base">{script.name}</CardTitle>
                          <p className="text-sm text-muted-foreground">
                            {script.description}
                          </p>
                        </div>
                        <Badge variant={getStatusColor(result?.success)}>
                          {result?.success === undefined ? 'New' : 
                           result.success ? 'Success' : 'Failed'}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="text-sm">
                          <div className="flex items-center gap-2 text-muted-foreground">
                            <Target className="w-4 h-4" />
                            {script.actions.length} actions
                          </div>
                          <div className="flex items-center gap-2 text-muted-foreground">
                            <Globe className="w-4 h-4" />
                            {script.url}
                          </div>
                          {script.schedule?.enabled && (
                            <div className="flex items-center gap-2 text-muted-foreground">
                              <Clock className="w-4 h-4" />
                              {script.schedule.frequency}
                            </div>
                          )}
                        </div>
                        
                        {result && (
                          <div className="text-xs space-y-1">
                            <div>Duration: {formatDuration(result.duration)}</div>
                            <div>Data points: {Object.keys(result.data).length}</div>
                            <div>Screenshots: {result.screenshots.length}</div>
                          </div>
                        )}

                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            onClick={() => handleRunScript(script.id)}
                            disabled={isRunning}
                          >
                            {isRunning ? (
                              <Pause className="w-4 h-4" />
                            ) : (
                              <Play className="w-4 h-4" />
                            )}
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDeleteScript(script.id)}
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}
        </TabsContent>

        <TabsContent value="templates" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {PlaywrightService.getTemplates().map((template, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="text-base">{template.name}</CardTitle>
                  <p className="text-sm text-muted-foreground">
                    {template.description}
                  </p>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="text-sm space-y-1">
                      <div className="flex items-center gap-2 text-muted-foreground">
                        <Target className="w-4 h-4" />
                        {template.actions.length} actions
                      </div>
                      <div className="flex items-center gap-2 text-muted-foreground">
                        <Eye className="w-4 h-4" />
                        {template.config.headless ? 'Headless' : 'Visible'}
                      </div>
                    </div>

                    <div className="flex flex-wrap gap-1">
                      {template.actions.slice(0, 3).map((action, i) => (
                        <Badge key={i} variant="outline" className="text-xs">
                          {action.type}
                        </Badge>
                      ))}
                      {template.actions.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{template.actions.length - 3} more
                        </Badge>
                      )}
                    </div>

                    <Button
                      className="w-full"
                      onClick={() => createFromTemplate(template)}
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      Create Script
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="results" className="space-y-4">
          {Array.from(results.entries()).length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Download className="w-12 h-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No Results Yet</h3>
                <p className="text-muted-foreground text-center">
                  Run some automation scripts to see results here
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {Array.from(results.entries()).map(([scriptId, result]) => {
                const script = scripts.find(s => s.id === scriptId);
                
                return (
                  <Card key={scriptId}>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div>
                          <CardTitle className="text-base">
                            {script?.name || 'Unknown Script'}
                          </CardTitle>
                          <p className="text-sm text-muted-foreground">
                            {new Date(result.timestamp).toLocaleString()}
                          </p>
                        </div>
                        <Badge variant={getStatusColor(result.success)}>
                          {result.success ? 'Success' : 'Failed'}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="grid grid-cols-3 gap-4 text-sm">
                          <div>
                            <span className="font-medium">Duration:</span>
                            <div>{formatDuration(result.duration)}</div>
                          </div>
                          <div>
                            <span className="font-medium">Data Points:</span>
                            <div>{Object.keys(result.data).length}</div>
                          </div>
                          <div>
                            <span className="font-medium">Screenshots:</span>
                            <div>{result.screenshots.length}</div>
                          </div>
                        </div>

                        {result.error && (
                          <div className="flex items-start gap-2 p-3 bg-destructive/10 rounded-lg">
                            <AlertTriangle className="w-4 h-4 text-destructive mt-0.5" />
                            <div className="text-sm text-destructive">
                              {result.error}
                            </div>
                          </div>
                        )}

                        {Object.keys(result.data).length > 0 && (
                          <div>
                            <h4 className="font-medium text-sm mb-2">Extracted Data:</h4>
                            <div className="bg-muted p-3 rounded-lg text-xs">
                              <pre className="whitespace-pre-wrap">
                                {JSON.stringify(result.data, null, 2)}
                              </pre>
                            </div>
                          </div>
                        )}

                        {result.logs.length > 0 && (
                          <div>
                            <h4 className="font-medium text-sm mb-2">Execution Log:</h4>
                            <div className="bg-muted p-3 rounded-lg text-xs space-y-1 max-h-32 overflow-y-auto">
                              {result.logs.map((log, i) => (
                                <div key={i} className="text-muted-foreground">
                                  {log}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};
