
import React from 'react';
import { cn } from '@/lib/utils';
import { Loader, AlertCircle, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  text?: string;
  variant?: 'spinner' | 'dots' | 'pulse';
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 'md', 
  className,
  text,
  variant = 'spinner'
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  };

  const textSizes = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
    xl: 'text-lg'
  };

  if (variant === 'dots') {
    return (
      <div className={cn('flex items-center justify-center', className)}>
        <div className="flex flex-col items-center gap-3">
          <div className="flex space-x-2">
            <div className="w-2 h-2 bg-green-600 rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-green-600 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-2 h-2 bg-green-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          </div>
          {text && (
            <p className={cn('text-slate-600 font-medium', textSizes[size])}>{text}</p>
          )}
        </div>
      </div>
    );
  }

  if (variant === 'pulse') {
    return (
      <div className={cn('flex items-center justify-center', className)}>
        <div className="flex flex-col items-center gap-3">
          <div className={cn('bg-green-600 rounded-full animate-pulse', sizeClasses[size])}></div>
          {text && (
            <p className={cn('text-slate-600 font-medium', textSizes[size])}>{text}</p>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={cn('flex items-center justify-center', className)}>
      <div className="flex flex-col items-center gap-3">
        <Loader 
          className={cn(
            'animate-spin text-green-600',
            sizeClasses[size]
          )}
        />
        {text && (
          <p className={cn('text-slate-600 font-medium', textSizes[size])}>{text}</p>
        )}
      </div>
    </div>
  );
};

interface PageLoadingProps {
  text?: string;
  variant?: 'spinner' | 'dots' | 'pulse';
  showCard?: boolean;
}

export const PageLoading: React.FC<PageLoadingProps> = ({ 
  text = 'Loading...', 
  variant = 'spinner',
  showCard = true 
}) => {
  const content = (
    <LoadingSpinner 
      size="lg" 
      text={text} 
      variant={variant}
      className="min-h-[400px]"
    />
  );

  if (showCard) {
    return (
      <div className="flex items-center justify-center min-h-[400px] p-8">
        <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl">
          <CardContent className="p-8">
            {content}
          </CardContent>
        </Card>
      </div>
    );
  }

  return content;
};

interface ErrorStateProps {
  title?: string;
  message?: string;
  onRetry?: () => void;
  onGoBack?: () => void;
  showDetails?: boolean;
  error?: Error;
}

export const ErrorState: React.FC<ErrorStateProps> = ({
  title = "Something went wrong",
  message = "We encountered an unexpected error. Please try again.",
  onRetry,
  onGoBack,
  showDetails = false,
  error
}) => {
  return (
    <div className="flex items-center justify-center min-h-[400px] p-8">
      <Card className="max-w-md mx-auto border-red-200 bg-red-50/30">
        <CardContent className="p-8 text-center space-y-4">
          <div className="mx-auto w-12 h-12 bg-red-100/50 rounded-full flex items-center justify-center">
            <AlertCircle className="w-6 h-6 text-red-600" />
          </div>
          
          <div>
            <h3 className="text-lg font-semibold text-red-900">{title}</h3>
            <p className="text-red-700 mt-2">{message}</p>
          </div>

          {showDetails && error && process.env.NODE_ENV === 'development' && (
            <details className="text-xs text-left bg-red-100/50 p-3 rounded border">
              <summary className="cursor-pointer font-medium text-red-800">Error Details</summary>
              <pre className="mt-2 text-red-700 overflow-auto max-h-32">
                {error.toString()}
              </pre>
            </details>
          )}

          <div className="flex gap-2 justify-center">
            {onRetry && (
              <Button 
                onClick={onRetry}
                size="sm"
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Try Again
              </Button>
            )}
            {onGoBack && (
              <Button 
                onClick={onGoBack}
                variant="outline"
                size="sm"
                className="border-red-300 text-red-700 hover:bg-red-100/50"
              >
                Go Back
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

interface EmptyStateProps {
  title: string;
  description?: string;
  icon?: React.ReactNode;
  action?: {
    label: string;
    onClick: () => void;
  };
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  title,
  description,
  icon,
  action
}) => {
  return (
    <div className="flex items-center justify-center min-h-[300px] p-8">
      <div className="text-center space-y-4">
        {icon && (
          <div className="mx-auto w-12 h-12 bg-slate-100 rounded-full flex items-center justify-center">
            {icon}
          </div>
        )}
        
        <div>
          <h3 className="text-lg font-semibold text-slate-900">{title}</h3>
          {description && (
            <p className="text-slate-600 mt-2">{description}</p>
          )}
        </div>

        {action && (
          <Button onClick={action.onClick} className="mt-4">
            {action.label}
          </Button>
        )}
      </div>
    </div>
  );
};

export const CardLoading: React.FC = () => (
  <div className="animate-pulse space-y-3">
    <div className="h-4 bg-slate-200 rounded w-3/4"></div>
    <div className="h-3 bg-slate-100 rounded w-1/2"></div>
    <div className="h-3 bg-slate-100 rounded w-2/3"></div>
  </div>
);

export const TableLoading: React.FC<{ rows?: number; columns?: number }> = ({ 
  rows = 5, 
  columns = 4 
}) => (
  <div className="space-y-3">
    {Array.from({ length: rows }).map((_, i) => (
      <div key={i} className="flex space-x-4 animate-pulse">
        {Array.from({ length: columns }).map((_, j) => (
          <div key={j} className="h-4 bg-slate-200 rounded flex-1"></div>
        ))}
      </div>
    ))}
  </div>
);
