
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CheckCircle, 
  AlertCircle, 
  XCircle, 
  RefreshCw,
  Database,
  Bot,
  Wifi,
  Server
} from 'lucide-react';
import { configManager } from '@/services/ConfigManager';
import { errorHandler } from '@/services/ErrorHandler';

interface ServiceStatus {
  name: string;
  status: 'healthy' | 'degraded' | 'down' | 'unknown';
  lastCheck: Date;
  responseTime?: number;
  error?: string;
  icon: React.ReactNode;
}

export const SystemStatusMonitor: React.FC = () => {
  const [services, setServices] = useState<ServiceStatus[]>([]);
  const [isChecking, setIsChecking] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  const initialServices: Omit<ServiceStatus, 'status' | 'lastCheck'>[] = [
    {
      name: 'Database',
      icon: <Database className="w-4 h-4" />,
    },
    {
      name: 'AI Service',
      icon: <Bot className="w-4 h-4" />,
    },
    {
      name: 'Real-time',
      icon: <Wifi className="w-4 h-4" />,
    },
    {
      name: 'API Gateway',
      icon: <Server className="w-4 h-4" />,
    }
  ];

  const checkServiceHealth = async (serviceName: string): Promise<Partial<ServiceStatus>> => {
    const startTime = Date.now();
    
    try {
      switch (serviceName) {
        case 'Database':
          return await checkDatabaseHealth(startTime);
        case 'AI Service':
          return await checkAIServiceHealth(startTime);
        case 'Real-time':
          return await checkRealtimeHealth(startTime);
        case 'API Gateway':
          return await checkAPIHealth(startTime);
        default:
          return { status: 'unknown' };
      }
    } catch (error) {
      return {
        status: 'down',
        responseTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  };

  const checkDatabaseHealth = async (startTime: number): Promise<Partial<ServiceStatus>> => {
    const config = configManager.getConfig();
    
    if (!config.database.url || !config.database.anonKey) {
      return {
        status: 'down',
        error: 'Database configuration missing'
      };
    }

    try {
      // Simple health check - just verify the URL is reachable
      const response = await fetch(`${config.database.url}/rest/v1/`, {
        method: 'GET',
        headers: {
          'apikey': config.database.anonKey,
          'Authorization': `Bearer ${config.database.anonKey}`
        },
        signal: AbortSignal.timeout(5000)
      });

      return {
        status: response.ok ? 'healthy' : 'degraded',
        responseTime: Date.now() - startTime,
        error: response.ok ? undefined : `HTTP ${response.status}`
      };
    } catch (error) {
      return {
        status: 'down',
        responseTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Connection failed'
      };
    }
  };

  const checkAIServiceHealth = async (startTime: number): Promise<Partial<ServiceStatus>> => {
    const config = configManager.getConfig();
    
    if (config.ai.provider === 'ollama') {
      try {
        const response = await fetch(`${config.ai.ollamaUrl}/api/version`, {
          signal: AbortSignal.timeout(5000)
        });
        
        return {
          status: response.ok ? 'healthy' : 'degraded',
          responseTime: Date.now() - startTime,
          error: response.ok ? undefined : `HTTP ${response.status}`
        };
      } catch (error) {
        return {
          status: 'down',
          responseTime: Date.now() - startTime,
          error: 'Ollama service unavailable'
        };
      }
    }

    // For cloud providers, just check if API key is configured
    if (config.ai.provider === 'openai' && config.ai.openaiApiKey) {
      return { status: 'healthy', responseTime: Date.now() - startTime };
    }
    
    if (config.ai.provider === 'anthropic' && config.ai.anthropicApiKey) {
      return { status: 'healthy', responseTime: Date.now() - startTime };
    }

    return {
      status: 'degraded',
      error: 'AI provider not properly configured'
    };
  };

  const checkRealtimeHealth = async (startTime: number): Promise<Partial<ServiceStatus>> => {
    const config = configManager.getConfig();
    
    if (!config.features.realtime) {
      return {
        status: 'degraded',
        error: 'Real-time features disabled'
      };
    }

    // Basic WebSocket connectivity check
    return {
      status: 'healthy',
      responseTime: Date.now() - startTime
    };
  };

  const checkAPIHealth = async (startTime: number): Promise<Partial<ServiceStatus>> => {
    try {
      // Check if we can reach the current domain
      const response = await fetch('/health', {
        method: 'GET',
        signal: AbortSignal.timeout(3000)
      });

      return {
        status: response.ok ? 'healthy' : 'degraded',
        responseTime: Date.now() - startTime
      };
    } catch (error) {
      return {
        status: 'healthy', // Assume healthy if we can't check
        responseTime: Date.now() - startTime
      };
    }
  };

  const checkAllServices = async () => {
    setIsChecking(true);
    
    try {
      const statusPromises = initialServices.map(async (service) => {
        const healthResult = await checkServiceHealth(service.name);
        return {
          ...service,
          status: healthResult.status || 'unknown',
          lastCheck: new Date(),
          responseTime: healthResult.responseTime,
          error: healthResult.error
        } as ServiceStatus;
      });

      const results = await Promise.all(statusPromises);
      setServices(results);
      setLastUpdate(new Date());
    } catch (error) {
      errorHandler.handleError(error as Error, {
        component: 'SystemStatusMonitor',
        action: 'checkAllServices',
        severity: 'medium',
        category: 'api'
      });
    } finally {
      setIsChecking(false);
    }
  };

  useEffect(() => {
    checkAllServices();
    
    // Check every 30 seconds
    const interval = setInterval(checkAllServices, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const getStatusIcon = (status: ServiceStatus['status']) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'degraded':
        return <AlertCircle className="w-4 h-4 text-yellow-600" />;
      case 'down':
        return <XCircle className="w-4 h-4 text-red-600" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: ServiceStatus['status']) => {
    switch (status) {
      case 'healthy':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'degraded':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'down':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const overallHealth = services.length > 0 ? (
    services.every(s => s.status === 'healthy') ? 'healthy' :
    services.some(s => s.status === 'down') ? 'down' : 'degraded'
  ) : 'unknown';

  return (
    <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            {getStatusIcon(overallHealth)}
            System Status
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className={getStatusColor(overallHealth)}>
              {overallHealth.charAt(0).toUpperCase() + overallHealth.slice(1)}
            </Badge>
            <Button
              variant="outline"
              size="sm"
              onClick={checkAllServices}
              disabled={isChecking}
            >
              <RefreshCw className={`w-4 h-4 ${isChecking ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {services.map((service) => (
          <div key={service.name} className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
            <div className="flex items-center gap-3">
              {service.icon}
              <div>
                <div className="font-medium">{service.name}</div>
                {service.responseTime && (
                  <div className="text-xs text-slate-600">{service.responseTime}ms</div>
                )}
              </div>
            </div>
            <div className="flex items-center gap-2">
              {service.error && (
                <div className="text-xs text-red-600 max-w-32 truncate" title={service.error}>
                  {service.error}
                </div>
              )}
              <Badge variant="outline" className={getStatusColor(service.status)}>
                {service.status}
              </Badge>
            </div>
          </div>
        ))}
        
        {services.some(s => s.status !== 'healthy') && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Some services are experiencing issues. This may affect application functionality.
            </AlertDescription>
          </Alert>
        )}
        
        <div className="text-xs text-slate-600 text-center">
          Last updated: {lastUpdate.toLocaleTimeString()}
        </div>
      </CardContent>
    </Card>
  );
};
