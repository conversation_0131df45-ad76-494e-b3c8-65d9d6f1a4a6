
import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  DollarSign, 
  Target, 
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3,
  PieChart,
  Activity,
  Zap,
  Brain,
  Settings
} from 'lucide-react'
import { BusinessIntelligenceService, KPI, BusinessInsight } from '@/services/ai/BusinessIntelligenceService'
import { ProjectManagementAI } from '@/services/ai/ProjectManagementAI'
import { BusinessOperationsAI } from '@/services/ai/BusinessOperationsAI'
import { BrowserAutomationService } from '@/services/automation/BrowserAutomationService'

export const EnhancedDashboard: React.FC = () => {
  const [kpis, setKpis] = useState<KPI[]>([])
  const [insights, setInsights] = useState<BusinessInsight[]>([])
  const [revenueAnalysis, setRevenueAnalysis] = useState<any>(null)
  const [marketIntelligence, setMarketIntelligence] = useState<any>(null)
  const [executiveSummary, setExecutiveSummary] = useState<string>('')
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    setIsLoading(true)
    try {
      // Load all dashboard data
      await BusinessIntelligenceService.updateKPIs()
      const [
        kpisData,
        insightsData,
        revenueData,
        marketData,
        summaryData
      ] = await Promise.all([
        BusinessIntelligenceService.getKPIs(),
        BusinessIntelligenceService.generateDashboardInsights(),
        BusinessIntelligenceService.generateRevenueAnalysis(),
        BusinessIntelligenceService.generateMarketIntelligence(),
        BusinessIntelligenceService.generateExecutiveSummary()
      ])

      setKpis(kpisData)
      setInsights(insightsData)
      setRevenueAnalysis(revenueData)
      setMarketIntelligence(marketData)
      setExecutiveSummary(summaryData)
    } catch (error) {
      console.error('Failed to load dashboard data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const renderKPICard = (kpi: KPI) => {
    const isPositive = kpi.change > 0
    const TrendIcon = isPositive ? TrendingUp : TrendingDown
    const trendColor = isPositive ? 'text-green-600' : 'text-red-600'
    
    const getIcon = () => {
      switch (kpi.category) {
        case 'sales': return <DollarSign className="w-4 h-4" />
        case 'finance': return <BarChart3 className="w-4 h-4" />
        case 'operations': return <Activity className="w-4 h-4" />
        case 'hr': return <Users className="w-4 h-4" />
        case 'marketing': return <Target className="w-4 h-4" />
        default: return <Activity className="w-4 h-4" />
      }
    }

    const progress = kpi.target ? (kpi.value / kpi.target) * 100 : 0

    return (
      <Card key={kpi.id}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">{kpi.name}</CardTitle>
          {getIcon()}
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {kpi.unit === '$' ? '$' : ''}{kpi.value.toLocaleString()}{kpi.unit === '%' ? '%' : ''}
          </div>
          <div className={`flex items-center text-xs ${trendColor}`}>
            <TrendIcon className="w-3 h-3 mr-1" />
            {Math.abs(kpi.change)}% from last period
          </div>
          {kpi.target && (
            <div className="mt-2">
              <div className="flex justify-between text-xs text-muted-foreground mb-1">
                <span>Progress to target</span>
                <span>{Math.round(progress)}%</span>
              </div>
              <Progress value={progress} className="h-1" />
            </div>
          )}
        </CardContent>
      </Card>
    )
  }

  const renderInsightCard = (insight: BusinessInsight) => {
    const getPriorityColor = () => {
      switch (insight.priority) {
        case 'critical': return 'bg-red-100 text-red-800 border-red-200'
        case 'high': return 'bg-orange-100 text-orange-800 border-orange-200'
        case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
        case 'low': return 'bg-green-100 text-green-800 border-green-200'
        default: return 'bg-gray-100 text-gray-800 border-gray-200'
      }
    }

    const getTypeIcon = () => {
      switch (insight.type) {
        case 'opportunity': return <TrendingUp className="w-4 h-4 text-green-600" />
        case 'risk': return <AlertTriangle className="w-4 h-4 text-red-600" />
        case 'trend': return <BarChart3 className="w-4 h-4 text-blue-600" />
        case 'recommendation': return <CheckCircle className="w-4 h-4 text-purple-600" />
        default: return <Activity className="w-4 h-4" />
      }
    }

    return (
      <Card key={insight.id} className="mb-4">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getTypeIcon()}
              <CardTitle className="text-sm">{insight.title}</CardTitle>
            </div>
            <Badge className={getPriorityColor()}>
              {insight.priority}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <p className="text-sm text-muted-foreground mb-2">{insight.description}</p>
          <p className="text-sm font-medium mb-3">{insight.impact}</p>
          
          {insight.suggestedActions.length > 0 && (
            <div>
              <p className="text-xs font-medium mb-2">Suggested Actions:</p>
              <ul className="text-xs text-muted-foreground space-y-1">
                {insight.suggestedActions.map((action, index) => (
                  <li key={index} className="flex items-start gap-1">
                    <span className="text-blue-600">•</span>
                    {action}
                  </li>
                ))}
              </ul>
            </div>
          )}
          
          <div className="flex justify-between items-center mt-3 pt-3 border-t">
            <span className="text-xs text-muted-foreground">
              Confidence: {Math.round(insight.confidence * 100)}%
            </span>
            {insight.actionRequired && (
              <Button size="sm" variant="outline">
                Take Action
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading dashboard insights...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Business Intelligence Dashboard</h1>
          <p className="text-muted-foreground">AI-powered insights and analytics</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={loadDashboardData}>
            <Activity className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <Settings className="w-4 h-4 mr-2" />
            Configure
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="insights">AI Insights</TabsTrigger>
          <TabsTrigger value="revenue">Revenue</TabsTrigger>
          <TabsTrigger value="market">Market Intel</TabsTrigger>
          <TabsTrigger value="automation">Automation</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Executive Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="w-5 h-5" />
                Executive Summary
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="prose max-w-none">
                <p className="text-sm leading-relaxed">{executiveSummary}</p>
              </div>
            </CardContent>
          </Card>

          {/* KPI Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {kpis.map(renderKPICard)}
          </div>

          {/* Quick Insights */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Critical Insights</CardTitle>
                <CardDescription>Top priority items requiring attention</CardDescription>
              </CardHeader>
              <CardContent>
                {insights
                  .filter(insight => insight.priority === 'critical' || insight.priority === 'high')
                  .slice(0, 3)
                  .map(renderInsightCard)}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>Latest system activities and updates</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    <span className="text-sm">Revenue analysis updated</span>
                    <span className="text-xs text-muted-foreground ml-auto">5 min ago</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Zap className="w-4 h-4 text-blue-600" />
                    <span className="text-sm">New automation workflow created</span>
                    <span className="text-xs text-muted-foreground ml-auto">15 min ago</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Target className="w-4 h-4 text-purple-600" />
                    <span className="text-sm">Lead qualification completed</span>
                    <span className="text-xs text-muted-foreground ml-auto">1 hour ago</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-semibold mb-4">All Business Insights</h3>
              {insights.length > 0 ? (
                insights.map(renderInsightCard)
              ) : (
                <Card>
                  <CardContent className="text-center py-8">
                    <Brain className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-muted-foreground">No insights available yet</p>
                  </CardContent>
                </Card>
              )}
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4">Insight Categories</h3>
              <div className="space-y-3">
                {['opportunity', 'risk', 'trend', 'recommendation'].map(type => {
                  const count = insights.filter(i => i.type === type).length
                  return (
                    <Card key={type}>
                      <CardContent className="flex items-center justify-between p-4">
                        <span className="capitalize font-medium">{type}s</span>
                        <Badge variant="outline">{count}</Badge>
                      </CardContent>
                    </Card>
                  )
                })}
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="revenue" className="space-y-6">
          {revenueAnalysis && (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">Total Revenue</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">${revenueAnalysis.totalRevenue.toLocaleString()}</div>
                    <div className="text-xs text-green-600 flex items-center">
                      <TrendingUp className="w-3 h-3 mr-1" />
                      +{revenueAnalysis.growth}%
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">Pipeline Value</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">${revenueAnalysis.pipeline.total.toLocaleString()}</div>
                    <div className="text-xs text-muted-foreground">{revenueAnalysis.opportunities} opportunities</div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">Qualified Deals</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">${revenueAnalysis.pipeline.qualified.toLocaleString()}</div>
                    <div className="text-xs text-muted-foreground">High probability</div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">Forecast</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">${revenueAnalysis.forecast.toLocaleString()}</div>
                    <div className="text-xs text-blue-600">Next quarter</div>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Revenue Breakdown</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {revenueAnalysis.breakdown.map((item: any, index: number) => (
                      <div key={index} className="flex items-center justify-between">
                        <span className="text-sm">{item.category}</span>
                        <div className="flex items-center gap-3">
                          <div className="w-32 bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-blue-600 h-2 rounded-full" 
                              style={{ width: `${item.percentage}%` }}
                            />
                          </div>
                          <span className="text-sm font-medium w-16 text-right">
                            ${item.amount.toLocaleString()}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </TabsContent>

        <TabsContent value="market" className="space-y-6">
          {marketIntelligence && (
            <>
              <Card>
                <CardHeader>
                  <CardTitle>Market Opportunities</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {marketIntelligence.opportunities.map((opp: any, index: number) => (
                      <div key={index} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium">{opp.title}</h4>
                          <Badge className={`${opp.priority === 'high' ? 'bg-red-100 text-red-800' : 
                            opp.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' : 
                            'bg-green-100 text-green-800'}`}>
                            {opp.priority}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">{opp.description}</p>
                        <div className="text-sm font-medium">
                          Estimated Value: ${opp.estimatedValue.toLocaleString()}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Market Trends</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {marketIntelligence.marketTrends.map((trend: any, index: number) => (
                        <div key={index} className="flex items-start gap-3">
                          <div className={`w-2 h-2 rounded-full mt-2 ${
                            trend.impact === 'positive' ? 'bg-green-500' :
                            trend.impact === 'negative' ? 'bg-red-500' : 'bg-gray-500'
                          }`} />
                          <div className="flex-1">
                            <div className="font-medium text-sm">{trend.trend}</div>
                            <div className="text-xs text-muted-foreground">{trend.description}</div>
                            <div className="text-xs text-blue-600 mt-1">
                              Confidence: {Math.round(trend.confidence * 100)}%
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Competitor Analysis</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {marketIntelligence.competitorAnalysis.slice(0, 2).map((comp: any, index: number) => (
                        <div key={index} className="border rounded-lg p-3">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium text-sm">{comp.competitor}</h4>
                            <span className="text-xs text-muted-foreground">
                              {comp.marketShare}% market share
                            </span>
                          </div>
                          <div className="text-xs space-y-1">
                            <div>
                              <span className="text-green-600">Strengths:</span> {comp.strengths.slice(0, 2).join(', ')}
                            </div>
                            <div>
                              <span className="text-red-600">Weaknesses:</span> {comp.weaknesses.slice(0, 2).join(', ')}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </>
          )}
        </TabsContent>

        <TabsContent value="automation" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Active Automations</CardTitle>
                <CardDescription>Browser automation and business processes</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {BrowserAutomationService.getTasks().slice(0, 5).map((task) => (
                    <div key={task.id} className="flex items-center justify-between p-3 border rounded">
                      <div>
                        <div className="font-medium text-sm">{task.name}</div>
                        <div className="text-xs text-muted-foreground">{task.description}</div>
                      </div>
                      <Badge className={`${task.status === 'active' ? 'bg-green-100 text-green-800' : 
                        task.status === 'paused' ? 'bg-yellow-100 text-yellow-800' : 
                        'bg-gray-100 text-gray-800'}`}>
                        {task.status}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Business Processes</CardTitle>
                <CardDescription>Automated workflows and operations</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {BusinessOperationsAI.getProcesses().slice(0, 5).map((process) => (
                    <div key={process.id} className="flex items-center justify-between p-3 border rounded">
                      <div>
                        <div className="font-medium text-sm">{process.name}</div>
                        <div className="text-xs text-muted-foreground">
                          {process.runCount} executions
                        </div>
                      </div>
                      <Badge className={`${process.status === 'active' ? 'bg-green-100 text-green-800' : 
                        'bg-gray-100 text-gray-800'}`}>
                        {process.status}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Automation Templates</CardTitle>
              <CardDescription>Ready-to-use automation workflows</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {BrowserAutomationService.getTemplates().slice(0, 3).map((template, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <h4 className="font-medium mb-2">{template.name}</h4>
                    <p className="text-sm text-muted-foreground mb-3">{template.description}</p>
                    <Button size="sm" variant="outline" className="w-full">
                      <Zap className="w-3 h-3 mr-1" />
                      Use Template
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
