
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, TrendingDown, DollarSign, Users, Target, Brain } from 'lucide-react';

interface MetricsProps {
  metrics: {
    totalDeals: number;
    totalValue: number;
    activeLeads: number;
    companiesTracked: number;
    conversionRate: number;
    monthlyGrowth: number;
    aiAcceleration: number;
    predictedRevenue: number;
    riskMitigation: number;
  };
}

export const MetricsCards: React.FC<MetricsProps> = ({ metrics }) => {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatPercentage = (value: number) => `${value.toFixed(1)}%`;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Pipeline</CardTitle>
          <DollarSign className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatCurrency(metrics.totalValue)}</div>
          <p className="text-xs text-muted-foreground">
            +{formatPercentage(metrics.monthlyGrowth)} from last month
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Active Deals</CardTitle>
          <Target className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{metrics.totalDeals}</div>
          <p className="text-xs text-muted-foreground">
            {formatPercentage(metrics.conversionRate)} conversion rate
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Active Leads</CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{metrics.activeLeads}</div>
          <p className="text-xs text-muted-foreground">
            {metrics.companiesTracked} companies tracked
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">AI Acceleration</CardTitle>
          <Brain className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">+{formatPercentage(metrics.aiAcceleration)}</div>
          <p className="text-xs text-muted-foreground">
            Revenue velocity increase
          </p>
        </CardContent>
      </Card>

      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle className="text-sm font-medium">AI Revenue Intelligence</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-sm">Predicted Revenue</span>
            <Badge variant="outline" className="bg-green-50 text-green-700">
              {formatCurrency(metrics.predictedRevenue)}
            </Badge>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm">Risk Mitigation</span>
            <Badge variant="outline" className="bg-blue-50 text-blue-700">
              {formatPercentage(metrics.riskMitigation)} reduced
            </Badge>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
