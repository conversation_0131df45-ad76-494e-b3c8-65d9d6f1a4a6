
import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Brain, MessageSquare, Settings } from 'lucide-react'
import { useNavigate } from 'react-router-dom'

export const AgentStatus: React.FC = () => {
  const navigate = useNavigate();

  const systemComponents = [
    { name: 'Database', status: 'Connected', color: 'green' },
    { name: 'File Storage', status: 'Connected', color: 'green' },
    { name: 'Backup System', status: 'Active', color: 'blue' },
    { name: 'Security', status: 'Protected', color: 'green' }
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Brain className="w-5 h-5 text-primary" />
          System Status
        </CardTitle>
        <CardDescription>
          System components and health monitoring
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {systemComponents.map((component) => (
            <div key={component.name} className="p-3 rounded-lg border bg-card">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-sm">{component.name}</h4>
                <Badge 
                  variant={component.color === 'green' ? 'default' : 'secondary'}
                  className={component.color === 'green' ? 'bg-green-500' : ''}
                >
                  {component.status}
                </Badge>
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-4 pt-4 border-t">
          <Button 
            variant="outline" 
            className="w-full"
            onClick={() => navigate('/settings')}
          >
            <Settings className="w-4 h-4 mr-2" />
            System Settings
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
