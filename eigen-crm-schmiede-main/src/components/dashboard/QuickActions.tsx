
import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  Plus, 
  FileText, 
  Users, 
  Calendar, 
  CheckSquare,
  Briefcase,
  ArrowRight
} from 'lucide-react'
import { useNavigate } from 'react-router-dom'
import { useToast } from '@/hooks/use-toast'

export const QuickActions = () => {
  const navigate = useNavigate()
  const { toast } = useToast()

  const handleQuickAction = (action: string, path?: string) => {
    if (path) {
      navigate(path)
    } else {
      toast({
        title: "Feature Coming Soon",
        description: `${action} functionality is being developed`,
      })
    }
  }

  const quickActions = [
    {
      title: 'New Project',
      description: 'Start a new construction project',
      icon: Briefcase,
      color: 'bg-blue-500 hover:bg-blue-600',
      action: () => handleQuickAction('New Project', '/projects')
    },
    {
      title: 'Add Team Member',
      description: 'Invite new team member',
      icon: Users,
      color: 'bg-green-500 hover:bg-green-600',
      action: () => handleQuickAction('Add Team Member', '/people')
    },
    {
      title: 'Create Task',
      description: 'Add new task or milestone',
      icon: CheckSquare,
      color: 'bg-purple-500 hover:bg-purple-600',
      action: () => handleQuickAction('Create Task', '/tasks')
    },
    {
      title: 'Schedule Meeting',
      description: 'Plan team or client meeting',
      icon: Calendar,
      color: 'bg-orange-500 hover:bg-orange-600',
      action: () => handleQuickAction('Schedule Meeting', '/calendar')
    },
    {
      title: 'Upload Document',
      description: 'Add project documentation',
      icon: FileText,
      color: 'bg-indigo-500 hover:bg-indigo-600',
      action: () => handleQuickAction('Upload Document', '/documents')
    },
    {
      title: 'Generate Report',
      description: 'Create project status report',
      icon: FileText,
      color: 'bg-red-500 hover:bg-red-600',
      action: () => handleQuickAction('Generate Report')
    }
  ]

  return (
    <Card className="border-white/20 bg-white/80 backdrop-blur-sm shadow-xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-xl">
          <Plus className="w-6 h-6 text-blue-600" />
          Quick Actions
        </CardTitle>
        <CardDescription>
          Common tasks and shortcuts for efficient project management
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {quickActions.map((action, index) => {
            const IconComponent = action.icon
            
            return (
              <Button
                key={index}
                onClick={action.action}
                variant="outline"
                className="h-24 p-4 text-left border-2 hover:border-blue-300 hover:shadow-lg transition-all duration-300 group"
              >
                <div className="flex flex-col items-start w-full">
                  <div className="flex items-center gap-3 mb-2">
                    <div className={`p-2 rounded-lg text-white ${action.color} group-hover:scale-110 transition-transform duration-300`}>
                      <IconComponent className="w-5 h-5" />
                    </div>
                    <ArrowRight className="w-4 h-4 text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </div>
                  <div>
                    <div className="font-semibold text-sm text-foreground">{action.title}</div>
                    <div className="text-xs text-muted-foreground mt-1">{action.description}</div>
                  </div>
                </div>
              </Button>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}
