
import React from 'react';
import { Bell, Activity } from 'lucide-react';

export const CommunicationStatusBar = () => {
  return (
    <div className="flex items-center gap-6 mt-4">
      <div className="flex items-center gap-2 px-4 py-2 bg-green-50 rounded-xl border border-green-200">
        <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse shadow-lg"></div>
        <span className="text-sm font-medium text-green-700">47 members online</span>
      </div>
      <div className="flex items-center gap-2 px-4 py-2 bg-blue-50 rounded-xl border border-blue-200">
        <Bell className="w-4 h-4 text-blue-600" />
        <span className="text-sm font-medium text-blue-700">3 new notifications</span>
      </div>
      <div className="flex items-center gap-2 px-4 py-2 bg-purple-50 rounded-xl border border-purple-200">
        <Activity className="w-4 h-4 text-purple-600" />
        <span className="text-sm font-medium text-purple-700">Real-time sync</span>
      </div>
    </div>
  );
};
