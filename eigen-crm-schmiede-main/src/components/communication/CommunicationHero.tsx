
import React from 'react';
import { MessageSquare } from 'lucide-react';

export const CommunicationHero = () => {
  return (
    <div className="flex-1">
      <div className="flex items-center gap-3 mb-3">
        <div className="p-3 rounded-xl bg-gradient-to-r from-purple-100 to-pink-100 shadow-lg">
          <MessageSquare className="w-7 h-7 text-purple-600" />
        </div>
        <div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-indigo-600 bg-clip-text text-transparent">
            Communication Hub
          </h1>
          <p className="text-lg text-slate-600 mt-1 font-medium">
            Team messaging, announcements, and real-time collaboration
          </p>
        </div>
      </div>
    </div>
  );
};
