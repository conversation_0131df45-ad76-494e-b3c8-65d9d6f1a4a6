
import React from 'react';
import { Badge } from '@/components/ui/badge';

export const CommunicationQuickStats = () => {
  return (
    <div className="flex items-center justify-between mt-6 pt-6 border-t border-slate-200">
      <div className="flex items-center gap-8">
        <div className="text-center">
          <div className="text-2xl font-bold text-slate-900">12</div>
          <div className="text-xs text-slate-500 font-medium">Active Channels</div>
        </div>
        <div className="w-px h-8 bg-slate-300"></div>
        <div className="text-center">
          <div className="text-2xl font-bold text-slate-900">248</div>
          <div className="text-xs text-slate-500 font-medium">Messages Today</div>
        </div>
        <div className="w-px h-8 bg-slate-300"></div>
        <div className="text-center">
          <div className="text-2xl font-bold text-slate-900">5</div>
          <div className="text-xs text-slate-500 font-medium">Active Calls</div>
        </div>
      </div>
      
      <div className="flex items-center gap-3">
        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 px-3 py-1">
          <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
          All Systems Operational
        </Badge>
      </div>
    </div>
  );
};
