
import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Hash, User, Plus, Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';

interface ChannelSidebarProps {
  channels: Array<{ id: string; name: string; unread: number; type: 'channel' | 'dm' }>;
  directMessages: Array<{ id: string; name: string; status?: 'online' | 'away' | 'offline'; unread: number; type: 'channel' | 'dm' }>;
  selectedChannel: string;
  onSelectChannel: (channelId: string) => void;
}

export const ChannelSidebar = ({
  channels,
  directMessages,
  selectedChannel,
  onSelectChannel
}: ChannelSidebarProps) => {
  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'online': return 'bg-green-500';
      case 'away': return 'bg-yellow-500';
      case 'offline': return 'bg-gray-400';
      default: return 'bg-gray-400';
    }
  };

  return (
    <Card className="bg-white/90 backdrop-blur-md border-white/30 shadow-xl h-fit sticky top-6">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-semibold text-slate-900">Conversations</CardTitle>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
          <Input 
            placeholder="Search channels..." 
            className="pl-10 bg-slate-50 border-slate-200 focus:bg-white transition-colors"
          />
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-semibold text-slate-700 uppercase tracking-wide">Channels</h3>
            <Button size="sm" variant="ghost" className="h-6 w-6 p-0 hover:bg-purple-100">
              <Plus className="h-3 w-3 text-purple-600" />
            </Button>
          </div>
          <div className="space-y-1">
            {channels.map((channel) => (
              <button
                key={channel.id}
                onClick={() => onSelectChannel(channel.id)}
                className={`w-full flex items-center justify-between p-3 rounded-lg text-left transition-all duration-200 group ${
                  selectedChannel === channel.id
                    ? 'bg-gradient-to-r from-purple-100 to-pink-100 border border-purple-200'
                    : 'hover:bg-slate-50'
                }`}
              >
                <div className="flex items-center space-x-3">
                  <div className={`p-1.5 rounded-md ${selectedChannel === channel.id ? 'bg-purple-200' : 'bg-slate-200 group-hover:bg-slate-300'}`}>
                    <Hash className="h-3 w-3 text-slate-600" />
                  </div>
                  <span className={`text-sm font-medium ${selectedChannel === channel.id ? 'text-purple-900' : 'text-slate-700'}`}>
                    {channel.name}
                  </span>
                </div>
                {channel.unread > 0 && (
                  <Badge variant="secondary" className="bg-purple-600 text-white text-xs px-2 py-0.5">
                    {channel.unread}
                  </Badge>
                )}
              </button>
            ))}
          </div>
        </div>

        <div>
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-semibold text-slate-700 uppercase tracking-wide">Direct Messages</h3>
            <Button size="sm" variant="ghost" className="h-6 w-6 p-0 hover:bg-green-100">
              <Plus className="h-3 w-3 text-green-600" />
            </Button>
          </div>
          <div className="space-y-1">
            {directMessages.map((dm) => (
              <button
                key={dm.id}
                onClick={() => onSelectChannel(dm.id)}
                className={`w-full flex items-center justify-between p-3 rounded-lg text-left transition-all duration-200 group ${
                  selectedChannel === dm.id
                    ? 'bg-gradient-to-r from-green-100 to-emerald-100 border border-green-200'
                    : 'hover:bg-slate-50'
                }`}
              >
                <div className="flex items-center space-x-3">
                  <div className="relative">
                    <div className={`p-1.5 rounded-md ${selectedChannel === dm.id ? 'bg-green-200' : 'bg-slate-200 group-hover:bg-slate-300'}`}>
                      <User className="h-3 w-3 text-slate-600" />
                    </div>
                    <div className={`absolute -bottom-0.5 -right-0.5 h-3 w-3 ${getStatusColor(dm.status)} rounded-full border-2 border-white`}></div>
                  </div>
                  <span className={`text-sm font-medium ${selectedChannel === dm.id ? 'text-green-900' : 'text-slate-700'}`}>
                    {dm.name}
                  </span>
                </div>
                {dm.unread > 0 && (
                  <Badge variant="secondary" className="bg-green-600 text-white text-xs px-2 py-0.5">
                    {dm.unread}
                  </Badge>
                )}
              </button>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
