
import React from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { MessageThread } from './MessageThread';
import { ChannelSidebar } from './ChannelSidebar';
import { AnnouncementBoard } from './AnnouncementBoard';

interface CommunicationTabsProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
  channels: Array<{ id: string; name: string; unread: number; type: 'channel' | 'dm' }>;
  directMessages: Array<{ id: string; name: string; status?: 'online' | 'away' | 'offline'; unread: number; type: 'channel' | 'dm' }>;
  selectedChannel: string;
  setSelectedChannel: (channel: string) => void;
  messages: Array<{
    id: string;
    sender: string;
    content: string;
    timestamp: string;
    avatar: string;
    type: 'text' | 'file' | 'system';
    reactions?: string[];
  }>;
  announcements: Array<{
    id: string;
    title: string;
    content: string;
    author: string;
    timestamp: string;
    priority: 'high' | 'medium' | 'low';
    isPinned: boolean;
    views: number;
    reactions: number;
    comments: number;
  }>;
  onSendMessage: (message: string) => void;
  onCreateAnnouncement: (announcement: { title: string; content: string; priority: string }) => void;
  getSelectedChannelName: () => string;
}

export const CommunicationTabs = ({
  activeTab,
  setActiveTab,
  channels,
  directMessages,
  selectedChannel,
  setSelectedChannel,
  messages,
  announcements,
  onSendMessage,
  onCreateAnnouncement,
  getSelectedChannelName
}: CommunicationTabsProps) => {
  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
      <TabsList className="grid w-full grid-cols-2 bg-white/80 backdrop-blur-sm">
        <TabsTrigger value="messages">Messages</TabsTrigger>
        <TabsTrigger value="announcements">Announcements</TabsTrigger>
      </TabsList>

      <TabsContent value="messages">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          <ChannelSidebar
            channels={channels}
            directMessages={directMessages}
            selectedChannel={selectedChannel}
            onSelectChannel={setSelectedChannel}
          />
          <div className="lg:col-span-3">
            <MessageThread
              channelName={getSelectedChannelName()}
              messages={messages}
              onSendMessage={onSendMessage}
            />
          </div>
        </div>
      </TabsContent>

      <TabsContent value="announcements">
        <AnnouncementBoard
          announcements={announcements}
          onCreateAnnouncement={onCreateAnnouncement}
        />
      </TabsContent>
    </Tabs>
  );
};
