import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Clock, Check, X, AlertTriangle, Info } from 'lucide-react';
import { useAIStore } from '@/stores/aiStore';
import { AutonomyService } from '@/services/AutonomyService';
import { useToast } from '@/hooks/use-toast';
import type { ApprovalRequest } from '@/types/autonomy';

export const ApprovalQueue: React.FC = () => {
  const { approvalRequests, agents } = useAIStore();
  const { toast } = useToast();

  const pendingRequests = approvalRequests.filter(req => req.status === 'pending');

  const handleApprove = async (requestId: string) => {
    const success = await AutonomyService.approveRequest(requestId, 'user');
    if (success) {
      toast({
        title: "Action Approved",
        description: "The agent action has been approved and will proceed.",
      });
    }
  };

  const handleReject = async (requestId: string) => {
    const success = await AutonomyService.rejectRequest(requestId, 'user');
    if (success) {
      toast({
        title: "Action Rejected",
        description: "The agent action has been rejected and will not proceed.",
        variant: "destructive"
      });
    }
  };

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return 'bg-green-500';
      case 'medium': return 'bg-yellow-500';
      case 'high': return 'bg-orange-500';
      case 'critical': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getRiskIcon = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return <Info className="w-4 h-4" />;
      case 'medium': return <Clock className="w-4 h-4" />;
      case 'high': return <AlertTriangle className="w-4 h-4" />;
      case 'critical': return <AlertTriangle className="w-4 h-4" />;
      default: return <Info className="w-4 h-4" />;
    }
  };

  const getTimeRemaining = (expiresAt: string) => {
    const now = new Date();
    const expires = new Date(expiresAt);
    const diff = expires.getTime() - now.getTime();
    
    if (diff <= 0) return 'Expired';
    
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    }
    return `${minutes}m`;
  };

  if (pendingRequests.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Check className="w-5 h-5 text-green-500" />
            Approval Queue
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <p className="text-muted-foreground">No pending approval requests</p>
            <p className="text-sm text-muted-foreground mt-1">
              All agent actions are running autonomously
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="w-5 h-5 text-orange-500" />
          Approval Queue ({pendingRequests.length})
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {pendingRequests.map((request) => {
            const agent = agents.find(a => a.id === request.agentId);
            
            return (
              <div key={request.id} className="p-4 rounded-lg border bg-card">
                <div className="flex items-start justify-between mb-3">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium">{request.actionName}</h4>
                      <Badge 
                        className={`${getRiskColor(request.riskLevel)} text-white`}
                      >
                        <span className="flex items-center gap-1">
                          {getRiskIcon(request.riskLevel)}
                          {request.riskLevel}
                        </span>
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Agent: {agent?.name || 'Unknown'} • {request.actionType}
                    </p>
                    <p className="text-sm">{request.description}</p>
                  </div>
                  <div className="text-right text-sm text-muted-foreground">
                    <p>Expires in</p>
                    <p className="font-medium">{getTimeRemaining(request.expiresAt)}</p>
                  </div>
                </div>

                {Object.keys(request.parameters).length > 0 && (
                  <div className="mb-3 p-2 rounded bg-muted/50">
                    <p className="text-xs font-medium mb-1">Parameters:</p>
                    <code className="text-xs">
                      {JSON.stringify(request.parameters, null, 2)}
                    </code>
                  </div>
                )}

                <div className="flex gap-2">
                  <Button
                    size="sm"
                    onClick={() => handleApprove(request.id)}
                    className="flex-1"
                  >
                    <Check className="w-3 h-3 mr-1" />
                    Approve
                  </Button>
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={() => handleReject(request.id)}
                    className="flex-1"
                  >
                    <X className="w-3 h-3 mr-1" />
                    Reject
                  </Button>
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};
