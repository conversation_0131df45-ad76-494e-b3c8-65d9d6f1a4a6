
import React, { useState } from 'react';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  useInvoices, 
  useExpenses, 
  useFinanceMetrics,
  useCreateInvoice,
  useUpdateInvoiceStatus,
  useRealtimeFinance
} from '@/hooks/useFinanceData';
import { LoadingSpinner, ErrorState } from '@/components/common/LoadingStates';
import { EnhancedErrorBoundary } from '@/components/common/EnhancedErrorBoundary';
import { useToast } from '@/hooks/use-toast';
import { FinanceHeader } from './finance/FinanceHeader';
import { FinanceStatsCards } from './finance/FinanceStatsCards';
import { FinanceOverview } from './finance/FinanceOverview';
import { InvoicesList } from './finance/InvoicesList';
import { ExpensesList } from './finance/ExpensesList';
import { FinanceReports } from './finance/FinanceReports';

export const Finance = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const { toast } = useToast();
  
  // Data hooks
  const { data: invoices, isLoading: invoicesLoading, error: invoicesError, refetch: refetchInvoices } = useInvoices();
  const { data: expenses, isLoading: expensesLoading, error: expensesError, refetch: refetchExpenses } = useExpenses();
  const { data: metrics, isLoading: metricsLoading } = useFinanceMetrics();
  
  // Mutations
  const createInvoiceMutation = useCreateInvoice();
  const updateInvoiceStatusMutation = useUpdateInvoiceStatus();
  
  // Real-time updates
  useRealtimeFinance();

  const handleCreateInvoice = () => {
    createInvoiceMutation.mutate({
      client: 'New Client',
      project: 'New Project',
      amount: 50000,
      status: 'draft',
      dueDate: '2024-04-01',
      issueDate: new Date().toISOString().split('T')[0],
      description: 'New invoice description'
    }, {
      onSuccess: () => {
        toast({
          title: "Invoice Created",
          description: "New invoice has been created successfully.",
        });
      }
    });
  };

  const handleUpdateInvoiceStatus = (id: string, status: any) => {
    updateInvoiceStatusMutation.mutate({ id, status }, {
      onSuccess: () => {
        toast({
          title: "Invoice Updated",
          description: `Invoice status updated to ${status}.`,
        });
      }
    });
  };

  const handleRefresh = () => {
    refetchInvoices();
    refetchExpenses();
    toast({
      title: "Data Refreshed",
      description: "Financial data has been refreshed.",
    });
  };

  // Show loading state for initial load
  if (invoicesLoading || expensesLoading || metricsLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-green-50 to-emerald-50 p-6">
        <LoadingSpinner size="lg" text="Loading financial data..." className="min-h-[400px]" />
      </div>
    );
  }

  // Show error state
  if (invoicesError || expensesError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-green-50 to-emerald-50 p-6">
        <ErrorState
          title="Failed to load financial data"
          message="There was an error loading your financial information. Please try again."
          onRetry={handleRefresh}
          error={invoicesError || expensesError}
          showDetails={true}
        />
      </div>
    );
  }

  return (
    <EnhancedErrorBoundary component="Finance">
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-green-50 to-emerald-50 p-6">
        <div className="max-w-7xl mx-auto space-y-8">
          <FinanceHeader 
            onCreateInvoice={handleCreateInvoice}
            onRefresh={handleRefresh}
            isCreatingInvoice={createInvoiceMutation.isPending}
          />

          <FinanceStatsCards metrics={metrics} invoices={invoices} />

          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="bg-white/80 backdrop-blur-sm border border-white/20 shadow-lg p-1 rounded-xl">
              <TabsTrigger value="overview" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-600 data-[state=active]:to-emerald-600 data-[state=active]:text-white rounded-lg">
                Overview
              </TabsTrigger>
              <TabsTrigger value="invoices" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-600 data-[state=active]:to-emerald-600 data-[state=active]:text-white rounded-lg">
                Invoices
              </TabsTrigger>
              <TabsTrigger value="expenses" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-600 data-[state=active]:to-emerald-600 data-[state=active]:text-white rounded-lg">
                Expenses
              </TabsTrigger>
              <TabsTrigger value="reports" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-600 data-[state=active]:to-emerald-600 data-[state=active]:text-white rounded-lg">
                Reports
              </TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              <FinanceOverview metrics={metrics} />
            </TabsContent>

            <TabsContent value="invoices" className="space-y-6">
              <InvoicesList 
                invoices={invoices}
                onUpdateInvoiceStatus={handleUpdateInvoiceStatus}
                isUpdatingStatus={updateInvoiceStatusMutation.isPending}
              />
            </TabsContent>

            <TabsContent value="expenses" className="space-y-6">
              <ExpensesList expenses={expenses} />
            </TabsContent>

            <TabsContent value="reports">
              <FinanceReports />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </EnhancedErrorBoundary>
  );
};
