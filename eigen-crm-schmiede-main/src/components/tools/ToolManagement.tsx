
import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Settings, Play, Trash2, Plus, Webhook, Mail, Calendar, Users } from 'lucide-react'
import { toolRegistry } from '@/services/tools/ToolRegistry'
import { N8nWebhookTool } from '@/services/tools/implementations/N8nWebhookTool'
import { EmailApiTool } from '@/services/tools/implementations/EmailApiTool'
import type { ToolDefinition } from '@/types/tools'
import { useToast } from '@/hooks/use-toast'

export const ToolManagement: React.FC = () => {
  const [tools, setTools] = useState<ToolDefinition[]>([])
  const [isAddingTool, setIsAddingTool] = useState(false)
  const [webhookUrl, setWebhookUrl] = useState('')
  const [toolName, setToolName] = useState('')
  const { toast } = useToast()

  useEffect(() => {
    loadTools()
    initializeDefaultTools()
  }, [])

  const loadTools = () => {
    setTools(toolRegistry.getAllTools())
  }

  const initializeDefaultTools = () => {
    // Register default tools if not already present
    const existingTools = toolRegistry.getAllTools()
    
    if (!existingTools.find(t => t.id === 'email-api')) {
      const emailTool = new EmailApiTool()
      toolRegistry.registerTool(emailTool)
    }
    
    loadTools()
  }

  const handleAddN8nTool = () => {
    if (!webhookUrl || !toolName) {
      toast({
        title: "Error",
        description: "Please provide both tool name and webhook URL",
        variant: "destructive"
      })
      return
    }

    const toolId = `n8n-${toolName.toLowerCase().replace(/\s+/g, '-')}`
    const n8nTool = new N8nWebhookTool(toolId, toolName, webhookUrl)
    
    toolRegistry.registerTool(n8nTool)
    loadTools()
    
    setIsAddingTool(false)
    setWebhookUrl('')
    setToolName('')
    
    toast({
      title: "Tool Added",
      description: `n8n workflow tool "${toolName}" has been registered`
    })
  }

  const handleRemoveTool = (toolId: string) => {
    toolRegistry.unregisterTool(toolId)
    loadTools()
    
    toast({
      title: "Tool Removed",
      description: "Tool has been unregistered"
    })
  }

  const getToolIcon = (category: string) => {
    switch (category) {
      case 'workflow': return Webhook
      case 'communication': return Mail
      case 'calendar': return Calendar
      case 'crm': return Users
      default: return Settings
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'inactive': return 'bg-gray-100 text-gray-800'
      case 'error': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Tool Management</h2>
          <p className="text-muted-foreground">Manage integrations and tools for your AI agents</p>
        </div>
        <Dialog open={isAddingTool} onOpenChange={setIsAddingTool}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Add Tool
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add n8n Workflow Tool</DialogTitle>
              <DialogDescription>
                Connect an n8n workflow via webhook URL
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="toolName">Tool Name</Label>
                <Input
                  id="toolName"
                  value={toolName}
                  onChange={(e) => setToolName(e.target.value)}
                  placeholder="e.g., Lead Enrichment Workflow"
                />
              </div>
              <div>
                <Label htmlFor="webhookUrl">Webhook URL</Label>
                <Input
                  id="webhookUrl"
                  value={webhookUrl}
                  onChange={(e) => setWebhookUrl(e.target.value)}
                  placeholder="https://your-n8n-instance.com/webhook/..."
                />
              </div>
              <Button onClick={handleAddN8nTool} className="w-full">
                Add Tool
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {tools.map((tool) => {
          const IconComponent = getToolIcon(tool.category)
          
          return (
            <Card key={tool.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <IconComponent className="w-5 h-5" />
                    <CardTitle className="text-lg">{tool.name}</CardTitle>
                  </div>
                  <Badge className={getStatusColor(tool.status)}>
                    {tool.status}
                  </Badge>
                </div>
                <CardDescription>{tool.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Category:</span>
                    <span className="capitalize">{tool.category}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Version:</span>
                    <span>{tool.version}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Usage:</span>
                    <span>{tool.usageCount} times</span>
                  </div>
                  {tool.lastUsed && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Last used:</span>
                      <span>{new Date(tool.lastUsed).toLocaleDateString()}</span>
                    </div>
                  )}
                </div>
                
                <div className="flex gap-2 mt-4">
                  <Button size="sm" variant="outline" className="flex-1">
                    <Play className="w-3 h-3 mr-1" />
                    Test
                  </Button>
                  <Button size="sm" variant="outline">
                    <Settings className="w-3 h-3" />
                  </Button>
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => handleRemoveTool(tool.id)}
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>
    </div>
  )
}
