
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { 
  BookOpen, 
  Plus, 
  Search, 
  Filter,
  Brain,
  Zap,
  Settings,
  Users,
  Building,
  Target
} from 'lucide-react';
import { InstructionCard } from './InstructionCard';
import { InstructionDialog } from './InstructionDialog';
import { PreviewDialog } from './PreviewDialog';
import { mockInstructions } from './mockData';
import type { Instruction, InstructionCategory } from './types';

export const Instructions = () => {
  const [instructions, setInstructions] = useState<Instruction[]>(mockInstructions);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<InstructionCategory | 'all'>('all');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingInstruction, setEditingInstruction] = useState<Instruction | null>(null);
  const [previewInstruction, setPreviewInstruction] = useState<Instruction | null>(null);

  const categories = [
    { value: 'all' as const, label: 'All Instructions', icon: BookOpen, color: 'bg-gray-100 text-gray-800' },
    { value: 'agent_behavior' as const, label: 'Agent Behavior', icon: Brain, color: 'bg-purple-100 text-purple-800' },
    { value: 'automation' as const, label: 'Automation', icon: Zap, color: 'bg-yellow-100 text-yellow-800' },
    { value: 'communication' as const, label: 'Communication', icon: Users, color: 'bg-blue-100 text-blue-800' },
    { value: 'workflow' as const, label: 'Workflow', icon: Settings, color: 'bg-gray-100 text-gray-800' },
    { value: 'integration' as const, label: 'Integration', icon: Building, color: 'bg-green-100 text-green-800' },
    { value: 'goals' as const, label: 'Goals & Targets', icon: Target, color: 'bg-red-100 text-red-800' }
  ];

  const filteredInstructions = instructions.filter(instruction => {
    const matchesSearch = instruction.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         instruction.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || instruction.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handleCreateInstruction = (newInstruction: Partial<Instruction>) => {
    const instruction: Instruction = {
      id: `inst_${Date.now()}`,
      title: newInstruction.title || '',
      content: newInstruction.content || '',
      description: newInstruction.description || '',
      category: newInstruction.category || 'general',
      tags: newInstruction.tags || [],
      version: 1,
      isActive: true,
      status: 'active',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    setInstructions(prev => [instruction, ...prev]);
    setIsCreateDialogOpen(false);
  };

  const handleUpdateInstruction = (updates: Partial<Instruction>) => {
    if (!editingInstruction) return;
    
    setInstructions(prev => prev.map(inst => 
      inst.id === editingInstruction.id 
        ? { ...inst, ...updates, updatedAt: new Date().toISOString() }
        : inst
    ));
    setEditingInstruction(null);
  };

  const handleDeleteInstruction = (id: string) => {
    setInstructions(prev => prev.filter(inst => inst.id !== id));
  };

  const stats = {
    total: instructions.length,
    active: instructions.filter(i => i.status === 'active').length,
    inactive: instructions.filter(i => i.status === 'inactive').length,
    categories: new Set(instructions.map(i => i.category)).size
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground flex items-center gap-2">
            <BookOpen className="w-8 h-8" />
            AI Instructions
          </h1>
          <p className="text-muted-foreground">Manage AI agent instructions and behaviors</p>
        </div>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="w-4 h-4 mr-2" />
          Create Instruction
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Instructions</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active</CardTitle>
            <Badge className="bg-green-100 text-green-800">Live</Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.active}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Inactive</CardTitle>
            <Badge variant="outline">Paused</Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.inactive}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Categories</CardTitle>
            <Filter className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.categories}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="Search instructions..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Tabs value={selectedCategory} onValueChange={(value) => setSelectedCategory(value as any)}>
          <TabsList className="grid grid-cols-7">
            {categories.map((category) => {
              const Icon = category.icon;
              return (
                <TabsTrigger key={category.value} value={category.value} className="text-xs">
                  <Icon className="w-3 h-3 mr-1" />
                  {category.label.split(' ')[0]}
                </TabsTrigger>
              );
            })}
          </TabsList>
        </Tabs>
      </div>

      {/* Instructions Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
        {filteredInstructions.map((instruction) => (
          <InstructionCard
            key={instruction.id}
            instruction={instruction}
            categories={categories}
            onPreview={() => setPreviewInstruction(instruction)}
            onEdit={() => setEditingInstruction(instruction)}
            onDelete={() => handleDeleteInstruction(instruction.id)}
          />
        ))}
      </div>

      {filteredInstructions.length === 0 && (
        <div className="text-center py-12">
          <BookOpen className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium text-foreground mb-2">No instructions found</h3>
          <p className="text-muted-foreground mb-4">
            {searchTerm || selectedCategory !== 'all' 
              ? 'Try adjusting your search or filters'
              : 'Create your first AI instruction to get started'
            }
          </p>
          {!searchTerm && selectedCategory === 'all' && (
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Create First Instruction
            </Button>
          )}
        </div>
      )}

      {/* Dialogs */}
      <InstructionDialog
        instruction={editingInstruction}
        isOpen={isCreateDialogOpen || !!editingInstruction}
        onSave={editingInstruction ? handleUpdateInstruction : handleCreateInstruction}
        onClose={() => {
          setIsCreateDialogOpen(false);
          setEditingInstruction(null);
        }}
      />

      <PreviewDialog
        instruction={previewInstruction}
        onClose={() => setPreviewInstruction(null)}
      />
    </div>
  );
};
