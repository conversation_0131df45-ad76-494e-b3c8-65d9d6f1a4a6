
import React from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Instruction } from './types';

interface PreviewDialogProps {
  instruction: Instruction | null;
  onClose: () => void;
}

export const PreviewDialog: React.FC<PreviewDialogProps> = ({ instruction, onClose }) => {
  if (!instruction) return null;

  return (
    <Dialog open={!!instruction} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>{instruction.title}</DialogTitle>
          <DialogDescription>
            Preview how this instruction affects AI behavior
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label>Instruction Content</Label>
            <div className="mt-1 p-3 bg-muted rounded-lg">
              {instruction.content}
            </div>
          </div>
          <div>
            <Label>AI Response Example</Label>
            <div className="mt-1 p-3 bg-blue-50 rounded-lg border-l-4 border-blue-500">
              <p className="text-sm">
                Based on this instruction, I will {instruction.content.toLowerCase()}
              </p>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
