
import React, { useState } from 'react';
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useLanguage } from '@/hooks/useLanguage';
import { Instruction, InstructionCategory } from './types';

interface InstructionDialogProps {
  instruction: Instruction | null;
  isOpen: boolean;
  onSave: (instruction: Partial<Instruction>) => void;
  onClose: () => void;
}

export const InstructionDialog: React.FC<InstructionDialogProps> = ({
  instruction,
  isOpen,
  onSave,
  onClose
}) => {
  const { t } = useLanguage();
  const [formData, setFormData] = useState({
    title: instruction?.title || '',
    content: instruction?.content || '',
    category: instruction?.category || 'general' as InstructionCategory,
    tags: instruction?.tags.join(', ') || ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave({
      title: formData.title,
      content: formData.content,
      category: formData.category,
      tags: formData.tags.split(',').map(tag => tag.trim()).filter(Boolean)
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>
            {instruction ? t('common.edit') + ' ' + t('instructions.form.title') : t('common.create') + ' ' + t('instructions.form.title')}
          </DialogTitle>
          <DialogDescription>
            {t('instructions.subtitle')}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="title">{t('instructions.form.title')}</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              placeholder={t('instructions.form.titlePlaceholder')}
              required
            />
          </div>
          
          <div>
            <Label htmlFor="category">{t('instructions.form.category')}</Label>
            <Select 
              value={formData.category} 
              onValueChange={(value: InstructionCategory) => setFormData(prev => ({ ...prev, category: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder={t('instructions.form.categoryPlaceholder')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="general">{t('instructions.general')}</SelectItem>
                <SelectItem value="company">{t('instructions.company')}</SelectItem>
                <SelectItem value="process">{t('instructions.process')}</SelectItem>
                <SelectItem value="commands">{t('instructions.commands')}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="content">{t('instructions.form.content')}</Label>
            <Textarea
              id="content"
              value={formData.content}
              onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
              placeholder={t('instructions.form.contentPlaceholder')}
              rows={6}
              required
            />
          </div>

          <div>
            <Label htmlFor="tags">{t('instructions.form.tags')}</Label>
            <Input
              id="tags"
              value={formData.tags}
              onChange={(e) => setFormData(prev => ({ ...prev, tags: e.target.value }))}
              placeholder={t('instructions.form.tagsPlaceholder')}
            />
          </div>

          <div className="flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={onClose}>
              {t('common.cancel')}
            </Button>
            <Button type="submit">
              {instruction ? t('common.update') : t('common.create')} {t('instructions.form.title')}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
