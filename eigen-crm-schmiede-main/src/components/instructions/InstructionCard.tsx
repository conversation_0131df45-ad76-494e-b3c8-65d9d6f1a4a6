
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Edit, Trash2, History, Eye, MoreHorizontal } from 'lucide-react';
import { Instruction, CategoryConfig } from './types';

interface InstructionCardProps {
  instruction: Instruction;
  categories: CategoryConfig[];
  onPreview: (instruction: Instruction) => void;
  onEdit: (instruction: Instruction) => void;
  onDelete: (id: string) => void;
}

export const InstructionCard: React.FC<InstructionCardProps> = ({
  instruction,
  categories,
  onPreview,
  onEdit,
  onDelete
}) => {
  const getCategoryIcon = (category: string) => {
    const cat = categories.find(c => c.value === category);
    return cat ? cat.icon : categories[0].icon;
  };

  const getCategoryColor = (category: string) => {
    const cat = categories.find(c => c.value === category);
    return cat ? cat.color : 'bg-gray-100 text-gray-800';
  };

  const CategoryIcon = getCategoryIcon(instruction.category);

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-2">
            <CategoryIcon className="w-5 h-5" />
            <CardTitle className="text-lg">{instruction.title}</CardTitle>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant={instruction.isActive ? "default" : "secondary"}>
              {instruction.isActive ? 'Active' : 'Inactive'}
            </Badge>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onPreview(instruction)}>
                  <Eye className="w-4 h-4 mr-2" />
                  Preview
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onEdit(instruction)}>
                  <Edit className="w-4 h-4 mr-2" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <History className="w-4 h-4 mr-2" />
                  Version History
                </DropdownMenuItem>
                <DropdownMenuItem 
                  onClick={() => onDelete(instruction.id)}
                  className="text-red-600"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className={getCategoryColor(instruction.category)}>
            {categories.find(c => c.value === instruction.category)?.label}
          </Badge>
          <span className="text-sm text-muted-foreground">v{instruction.version}</span>
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-muted-foreground mb-4 line-clamp-3">
          {instruction.content}
        </p>
        <div className="flex flex-wrap gap-1 mb-4">
          {instruction.tags.map(tag => (
            <Badge key={tag} variant="secondary" className="text-xs">
              {tag}
            </Badge>
          ))}
        </div>
        <p className="text-xs text-muted-foreground">
          Updated: {new Date(instruction.updatedAt).toLocaleDateString()}
        </p>
      </CardContent>
    </Card>
  );
};
