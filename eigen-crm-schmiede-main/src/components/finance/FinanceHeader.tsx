
import React from 'react';
import { Button } from '@/components/ui/button';
import { Plus, Download, RefreshCw } from 'lucide-react';

interface FinanceHeaderProps {
  onCreateInvoice: () => void;
  onRefresh: () => void;
  isCreatingInvoice: boolean;
}

export const FinanceHeader: React.FC<FinanceHeaderProps> = ({
  onCreateInvoice,
  onRefresh,
  isCreatingInvoice
}) => {
  return (
    <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl p-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-slate-900 via-green-900 to-emerald-900 bg-clip-text text-transparent">
            Finance & Billing
          </h1>
          <p className="text-slate-600 mt-2 text-lg">Manage invoices, expenses, and financial reporting</p>
        </div>
        <div className="flex gap-3">
          <Button 
            onClick={onCreateInvoice}
            disabled={isCreatingInvoice}
            className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white shadow-lg hover:shadow-xl transition-all duration-300"
          >
            {isCreatingInvoice ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Plus className="h-4 w-4 mr-2" />
            )}
            New Invoice
          </Button>
          <Button variant="outline" className="border-slate-200 hover:bg-slate-50">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button 
            variant="outline" 
            onClick={onRefresh}
            className="border-slate-200 hover:bg-slate-50"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>
    </div>
  );
};
