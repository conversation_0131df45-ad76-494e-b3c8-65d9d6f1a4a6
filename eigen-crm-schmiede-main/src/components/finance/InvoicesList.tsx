
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar } from 'lucide-react';
import { LoadingSpinner } from '@/components/common/LoadingStates';
import { useLoadingStore } from '@/stores/loadingStore';
import type { Invoice } from '@/types/finance';

interface InvoicesListProps {
  invoices: Invoice[] | undefined;
  onUpdateInvoiceStatus: (id: string, status: Invoice['status']) => void;
  isUpdatingStatus: boolean;
}

export const InvoicesList: React.FC<InvoicesListProps> = ({
  invoices,
  onUpdateInvoiceStatus,
  isUpdatingStatus
}) => {
  const { isLoading } = useLoadingStore();

  const getInvoiceStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800 border-green-200';
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'overdue': return 'bg-red-100 text-red-800 border-red-200';
      case 'draft': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (isLoading('invoices')) {
    return <LoadingSpinner size="lg" text="Loading invoices..." />;
  }

  return (
    <div className="space-y-4">
      {invoices?.map((invoice) => (
        <Card key={invoice.id} className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300 group">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <div className="flex items-center gap-3">
                  <h3 className="text-lg font-bold text-slate-900">{invoice.id}</h3>
                  <Badge variant="outline" className={getInvoiceStatusColor(invoice.status)}>
                    {invoice.status}
                  </Badge>
                </div>
                <div className="flex items-center gap-6 text-slate-600">
                  <span>{invoice.client}</span>
                  <span>{invoice.project}</span>
                  <div className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    <span>Due: {invoice.dueDate}</span>
                  </div>
                </div>
                <p className="text-sm text-slate-500">{invoice.description}</p>
              </div>
              <div className="text-right space-y-2">
                <p className="text-2xl font-bold text-slate-900">${(invoice.amount / 1000).toFixed(0)}K</p>
                <div className="flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                  {invoice.status === 'pending' && (
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => onUpdateInvoiceStatus(invoice.id, 'paid')}
                      disabled={isUpdatingStatus}
                    >
                      Mark Paid
                    </Button>
                  )}
                  <Button variant="outline" size="sm">
                    View Details
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
