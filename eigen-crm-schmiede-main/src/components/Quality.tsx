
import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { CheckCircle, XCircle, AlertTriangle, Plus, Search, BarChart3 } from 'lucide-react';
import { DatabaseService } from '@/services/DatabaseService';
import { useToast } from '@/hooks/use-toast';
import { useRealtimeUpdates } from '@/services/RealtimeService';

export const Quality = () => {
  const [activeTab, setActiveTab] = useState('inspections');
  const [searchTerm, setSearchTerm] = useState('');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newInspection, setNewInspection] = useState({
    inspector_name: '',
    area: '',
    type: 'material' as const,
    passed: true,
    defects_found: 0,
    corrective_actions: ['']
  });

  const queryClient = useQueryClient();
  const { toast } = useToast();

  // Set up real-time updates
  useRealtimeUpdates(['quality_inspections', 'quality_metrics']);

  // Fetch quality inspections
  const { data: inspections = [], isLoading: inspectionsLoading } = useQuery({
    queryKey: ['quality_inspections'],
    queryFn: () => DatabaseService.getQualityInspections(),
  });

  // Fetch quality metrics
  const { data: metrics = [], isLoading: metricsLoading } = useQuery({
    queryKey: ['quality_metrics'],
    queryFn: () => DatabaseService.getQualityMetrics(),
  });

  // Create inspection mutation
  const createInspectionMutation = useMutation({
    mutationFn: (inspection: any) => DatabaseService.createQualityInspection(inspection),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['quality_inspections'] });
      setIsCreateDialogOpen(false);
      setNewInspection({
        inspector_name: '',
        area: '',
        type: 'material',
        passed: true,
        defects_found: 0,
        corrective_actions: ['']
      });
      toast({
        title: "Inspection Created",
        description: "Quality inspection has been recorded successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to create inspection.",
        variant: "destructive",
      });
      console.error('Error creating inspection:', error);
    },
  });

  const handleCreateInspection = () => {
    const inspectionData = {
      ...newInspection,
      inspection_date: new Date().toISOString().split('T')[0],
      checklist: [
        { item: 'Quality Standards', status: newInspection.passed ? 'pass' : 'fail' }
      ]
    };
    createInspectionMutation.mutate(inspectionData);
  };

  const getStatusColor = (passed: boolean) => {
    return passed ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'material': return <CheckCircle className="h-5 w-5" />;
      case 'workmanship': return <BarChart3 className="h-5 w-5" />;
      case 'compliance': return <AlertTriangle className="h-5 w-5" />;
      default: return <CheckCircle className="h-5 w-5" />;
    }
  };

  const filteredInspections = inspections.filter(inspection =>
    inspection.area?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    inspection.inspector_name?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const stats = {
    total: inspections.length,
    passed: inspections.filter(i => i.passed).length,
    failed: inspections.filter(i => !i.passed).length,
    avgDefects: inspections.length > 0 ? 
      (inspections.reduce((sum, i) => sum + (i.defects_found || 0), 0) / inspections.length).toFixed(1) : '0'
  };

  if (inspectionsLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-green-50 to-blue-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="animate-pulse">Loading quality data...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-green-50 to-blue-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-slate-900">Quality Control</h1>
            <p className="text-slate-600 mt-2">Monitor and maintain quality standards</p>
          </div>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-green-600 hover:bg-green-700">
                <Plus className="h-4 w-4 mr-2" />
                New Inspection
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create Quality Inspection</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="inspector">Inspector Name</Label>
                  <Input
                    id="inspector"
                    value={newInspection.inspector_name}
                    onChange={(e) => setNewInspection({ ...newInspection, inspector_name: e.target.value })}
                    placeholder="Inspector name"
                  />
                </div>
                <div>
                  <Label htmlFor="area">Area/Location</Label>
                  <Input
                    id="area"
                    value={newInspection.area}
                    onChange={(e) => setNewInspection({ ...newInspection, area: e.target.value })}
                    placeholder="e.g., Building A - Floor 3"
                  />
                </div>
                <div>
                  <Label htmlFor="type">Inspection Type</Label>
                  <Select value={newInspection.type} onValueChange={(value: any) => setNewInspection({ ...newInspection, type: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="material">Material</SelectItem>
                      <SelectItem value="workmanship">Workmanship</SelectItem>
                      <SelectItem value="compliance">Compliance</SelectItem>
                      <SelectItem value="final">Final</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="defects">Defects Found</Label>
                  <Input
                    id="defects"
                    type="number"
                    value={newInspection.defects_found}
                    onChange={(e) => setNewInspection({ ...newInspection, defects_found: parseInt(e.target.value) || 0 })}
                    min="0"
                  />
                </div>
                <Button onClick={handleCreateInspection} className="w-full">
                  Create Inspection
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Total Inspections</p>
                  <p className="text-2xl font-bold text-slate-900">{stats.total}</p>
                </div>
                <BarChart3 className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Passed</p>
                  <p className="text-2xl font-bold text-green-600">{stats.passed}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Failed</p>
                  <p className="text-2xl font-bold text-red-600">{stats.failed}</p>
                </div>
                <XCircle className="h-8 w-8 text-red-500" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Avg Defects</p>
                  <p className="text-2xl font-bold text-orange-600">{stats.avgDefects}</p>
                </div>
                <AlertTriangle className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search */}
        <Card>
          <CardContent className="p-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
              <Input
                placeholder="Search inspections..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </CardContent>
        </Card>

        {/* Inspections List */}
        <div className="space-y-4">
          {filteredInspections.map((inspection) => (
            <Card key={inspection.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getTypeIcon(inspection.type)}
                    <div>
                      <CardTitle className="text-lg">{inspection.area}</CardTitle>
                      <p className="text-sm text-slate-600">by {inspection.inspector_name}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge className={getStatusColor(inspection.passed)}>
                      {inspection.passed ? 'Passed' : 'Failed'}
                    </Badge>
                    <span className="text-sm text-slate-500">{inspection.inspection_date}</span>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-slate-600">Type</p>
                    <p className="font-medium capitalize">{inspection.type}</p>
                  </div>
                  <div>
                    <p className="text-slate-600">Defects Found</p>
                    <p className="font-medium">{inspection.defects_found || 0}</p>
                  </div>
                </div>
                {inspection.corrective_actions && inspection.corrective_actions.length > 0 && (
                  <div>
                    <p className="text-slate-600 text-sm">Corrective Actions</p>
                    <ul className="text-sm list-disc list-inside">
                      {inspection.corrective_actions.map((action, index) => (
                        <li key={index}>{action}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};
