
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { FolderOpen, Plus } from 'lucide-react';
import { ProjectCard } from './ProjectCard';
import { Project } from '@/types/project';

interface ProjectsListProps {
  projects: Project[];
  onEditProject: (project: Project) => void;
  onAddProject: () => void;
}

export const ProjectsList: React.FC<ProjectsListProps> = ({
  projects,
  onEditProject,
  onAddProject
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'planning': return 'bg-blue-100 text-blue-800';
      case 'active': return 'bg-green-100 text-green-800';
      case 'on-hold': return 'bg-yellow-100 text-yellow-800';
      case 'completed': return 'bg-gray-100 text-gray-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'critical': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (projects.length === 0) {
    return (
      <Card>
        <CardContent className="p-12 text-center">
          <FolderOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No projects found</h3>
          <p className="text-muted-foreground mb-4">
            Try adjusting your filters or create a new project.
          </p>
          <Button onClick={onAddProject}>
            <Plus className="h-4 w-4 mr-2" />
            New Project
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid gap-4">
      {projects.map((project) => (
        <ProjectCard
          key={project.id}
          project={project}
          onEdit={onEditProject}
          getStatusColor={getStatusColor}
          getPriorityColor={getPriorityColor}
        />
      ))}
    </div>
  );
};
