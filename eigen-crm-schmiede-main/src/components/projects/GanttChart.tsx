
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock, Users } from 'lucide-react';
import { format, differenceInDays, addDays } from 'date-fns';
import { Project } from '@/types/project';

interface GanttChartProps {
  projects: Project[];
}

export const GanttChart: React.FC<GanttChartProps> = ({ projects }) => {
  // Calculate timeline boundaries
  const today = new Date();
  const startDate = new Date(Math.min(...projects.map(p => new Date(p.startDate).getTime())));
  const endDate = new Date(Math.max(...projects.map(p => new Date(p.endDate).getTime())));
  const totalDays = differenceInDays(endDate, startDate);

  const getBarWidth = (project: Project) => {
    const projectDays = differenceInDays(new Date(project.endDate), new Date(project.startDate));
    return (projectDays / totalDays) * 100;
  };

  const getBarPosition = (project: Project) => {
    const daysFromStart = differenceInDays(new Date(project.startDate), startDate);
    return (daysFromStart / totalDays) * 100;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'planning': return 'bg-blue-500';
      case 'active': return 'bg-green-500';
      case 'on-hold': return 'bg-yellow-500';
      case 'completed': return 'bg-gray-500';
      case 'cancelled': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="w-5 h-5" />
          Project Timeline (Gantt Chart)
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Timeline header */}
        <div className="flex justify-between text-sm text-muted-foreground mb-4">
          <span>{format(startDate, 'MMM yyyy')}</span>
          <span>{format(endDate, 'MMM yyyy')}</span>
        </div>

        {/* Project bars */}
        <div className="space-y-3">
          {projects.map((project) => (
            <div key={project.id} className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2 w-64">
                  <h4 className="font-medium text-sm truncate">{project.name}</h4>
                  <Badge className={`${getStatusColor(project.status)} text-white text-xs`}>
                    {project.status}
                  </Badge>
                </div>
                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                  <span className="flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    {differenceInDays(new Date(project.endDate), new Date(project.startDate))} days
                  </span>
                  <span className="flex items-center gap-1">
                    <Users className="w-3 h-3" />
                    {project.team.length}
                  </span>
                </div>
              </div>
              
              {/* Timeline bar */}
              <div className="relative h-6 bg-gray-100 rounded">
                <div
                  className={`absolute h-full rounded ${getStatusColor(project.status)} opacity-80`}
                  style={{
                    left: `${getBarPosition(project)}%`,
                    width: `${getBarWidth(project)}%`
                  }}
                >
                  {/* Progress overlay */}
                  <div
                    className="h-full bg-white bg-opacity-30 rounded"
                    style={{ width: `${project.progress}%` }}
                  />
                </div>
                
                {/* Today indicator */}
                {(() => {
                  const todayPosition = (differenceInDays(today, startDate) / totalDays) * 100;
                  if (todayPosition >= 0 && todayPosition <= 100) {
                    return (
                      <div
                        className="absolute top-0 bottom-0 w-0.5 bg-red-500 z-10"
                        style={{ left: `${todayPosition}%` }}
                      />
                    );
                  }
                  return null;
                })()}
              </div>

              <div className="flex justify-between text-xs text-muted-foreground">
                <span>{format(new Date(project.startDate), 'MMM d')}</span>
                <span className="font-medium">{project.progress}% complete</span>
                <span>{format(new Date(project.endDate), 'MMM d')}</span>
              </div>
            </div>
          ))}
        </div>

        {/* Legend */}
        <div className="flex items-center gap-4 pt-4 border-t text-xs">
          <span className="flex items-center gap-1">
            <div className="w-0.5 h-4 bg-red-500" />
            Today
          </span>
          <span className="text-muted-foreground">
            White overlay shows completion progress
          </span>
        </div>
      </CardContent>
    </Card>
  );
};
