
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Users, Truck, Wrench, AlertTriangle } from 'lucide-react';
import { Project } from '@/types/project';

interface ResourceAllocationProps {
  projects: Project[];
}

interface ResourceData {
  type: 'crew' | 'equipment' | 'vehicles';
  name: string;
  total: number;
  allocated: number;
  icon: typeof Users;
  color: string;
}

export const ResourceAllocation: React.FC<ResourceAllocationProps> = ({ projects }) => {
  const resources: ResourceData[] = [
    {
      type: 'crew',
      name: 'Construction Crew',
      total: 50,
      allocated: 38,
      icon: Users,
      color: 'blue'
    },
    {
      type: 'equipment',
      name: 'Heavy Equipment',
      total: 15,
      allocated: 12,
      icon: Wrench,
      color: 'green'
    },
    {
      type: 'vehicles',
      name: 'Vehicles',
      total: 25,
      allocated: 20,
      icon: Truck,
      color: 'purple'
    }
  ];

  const getUtilizationColor = (percentage: number) => {
    if (percentage > 90) return 'text-red-600';
    if (percentage > 75) return 'text-yellow-600';
    return 'text-green-600';
  };

  const getProgressColor = (percentage: number) => {
    if (percentage > 90) return 'bg-red-500';
    if (percentage > 75) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            Resource Allocation Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {resources.map((resource) => {
              const IconComponent = resource.icon;
              const utilizationPercentage = (resource.allocated / resource.total) * 100;
              
              return (
                <Card key={resource.type} className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <IconComponent className={`w-5 h-5 text-${resource.color}-600`} />
                      <h3 className="font-medium">{resource.name}</h3>
                    </div>
                    {utilizationPercentage > 90 && (
                      <AlertTriangle className="w-4 h-4 text-red-500" />
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Allocated: {resource.allocated}/{resource.total}</span>
                      <span className={getUtilizationColor(utilizationPercentage)}>
                        {utilizationPercentage.toFixed(0)}%
                      </span>
                    </div>
                    
                    <div className="relative">
                      <Progress value={utilizationPercentage} className="h-2" />
                      <div 
                        className={`absolute top-0 left-0 h-2 rounded-full ${getProgressColor(utilizationPercentage)}`}
                        style={{ width: `${utilizationPercentage}%` }}
                      />
                    </div>
                    
                    <div className="text-xs text-muted-foreground">
                      Available: {resource.total - resource.allocated}
                    </div>
                  </div>
                </Card>
              );
            })}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Project Resource Breakdown</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {projects.filter(p => p.status === 'active').map((project) => (
              <div key={project.id} className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-medium">{project.name}</h3>
                  <Badge variant="outline">{project.status}</Badge>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <Users className="w-4 h-4 text-blue-600" />
                    <span>Team: {project.team.length} members</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Wrench className="w-4 h-4 text-green-600" />
                    <span>Equipment: 3 units</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Truck className="w-4 h-4 text-purple-600" />
                    <span>Vehicles: 2 units</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
