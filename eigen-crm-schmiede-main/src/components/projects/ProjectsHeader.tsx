
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Plus } from 'lucide-react';

interface ProjectsHeaderProps {
  onAddProject: () => void;
}

export const ProjectsHeader: React.FC<ProjectsHeaderProps> = ({ onAddProject }) => {
  return (
    <div className="flex items-center justify-between">
      <div>
        <h1 className="text-3xl font-bold">Enhanced Project Management</h1>
        <p className="text-muted-foreground">Advanced tools for comprehensive project tracking</p>
      </div>
      <Button onClick={onAddProject} className="bg-gradient-to-r from-blue-600 to-indigo-600">
        <Plus className="h-4 w-4 mr-2" />
        New Project
      </Button>
    </div>
  );
};
