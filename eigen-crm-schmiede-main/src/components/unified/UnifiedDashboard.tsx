
import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { 
  Brain, 
  TrendingUp, 
  Users, 
  DollarSign, 
  Activity,
  Zap,
  Shield,
  Clock
} from 'lucide-react'
import { unifiedDatabase } from '@/services/unified/UnifiedDatabaseService'
import { unifiedLLM } from '@/services/unified/UnifiedLLMService'
import { unifiedAI } from '@/services/unified/UnifiedAIOrchestrator'
import type { UnifiedProject, UnifiedTask, UnifiedAgent, UnifiedMetrics } from '@/types/unified'

export const UnifiedDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview')
  const [projects, setProjects] = useState<UnifiedProject[]>([])
  const [tasks, setTasks] = useState<UnifiedTask[]>([])
  const [agents, setAgents] = useState<UnifiedAgent[]>([])
  const [metrics, setMetrics] = useState<UnifiedMetrics | null>(null)
  const [systemStatus, setSystemStatus] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    setIsLoading(true)
    try {
      const [projectsRes, tasksRes, agentsRes, systemRes, aiMetricsRes] = await Promise.all([
        unifiedDatabase.getProjects(),
        unifiedDatabase.getTasks(),
        unifiedDatabase.getAgents(),
        unifiedLLM.getSystemStatus(),
        unifiedAI.getAIMetrics()
      ])

      if (projectsRes.success) setProjects(projectsRes.data || [])
      if (tasksRes.success) setTasks(tasksRes.data || [])
      if (agentsRes.success) setAgents(agentsRes.data || [])
      if (systemRes.success) setSystemStatus(systemRes.data)
      if (aiMetricsRes.success) setMetrics(aiMetricsRes.data?.llm)
    } catch (error) {
      console.error('Failed to load dashboard data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
      case 'completed':
      case 'healthy':
        return 'default'
      case 'on-hold':
      case 'degraded':
        return 'secondary'
      case 'cancelled':
      case 'down':
        return 'destructive'
      default:
        return 'outline'
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading dashboard...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Business Intelligence Hub</h1>
            <p className="text-muted-foreground">
              Unified dashboard for all your business operations and AI systems
            </p>
          </div>
          <Button onClick={loadDashboardData} variant="outline">
            <Activity className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>

        {/* Key Metrics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Active Projects</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{projects.filter(p => p.status === 'active').length}</div>
              <p className="text-xs text-muted-foreground">
                {projects.length} total projects
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">AI Agents</CardTitle>
              <Brain className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{agents.filter(a => a.status === 'running').length}</div>
              <p className="text-xs text-muted-foreground">
                {agents.length} total agents
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">LLM Requests</CardTitle>
              <Zap className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics?.totalRequests || 0}</div>
              <p className="text-xs text-muted-foreground">
                {((metrics?.successRate || 0) * 100).toFixed(1)}% success rate
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">System Health</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {(metrics?.systemHealth || 100).toFixed(0)}%
              </div>
              <Progress value={metrics?.systemHealth || 100} className="mt-2" />
            </CardContent>
          </Card>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="projects">Projects</TabsTrigger>
            <TabsTrigger value="agents">AI Agents</TabsTrigger>
            <TabsTrigger value="llm">LLM Status</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Recent Projects</CardTitle>
                  <CardDescription>Latest project activities</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {projects.slice(0, 5).map((project) => (
                      <div key={project.id} className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">{project.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {project.progress}% complete
                          </p>
                        </div>
                        <Badge variant={getStatusColor(project.status)}>
                          {project.status}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>AI Agent Status</CardTitle>
                  <CardDescription>Current agent activities</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {agents.slice(0, 5).map((agent) => (
                      <div key={agent.id} className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">{agent.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {agent.type} • {agent.performance.tasksCompleted} tasks
                          </p>
                        </div>
                        <Badge variant={getStatusColor(agent.status)}>
                          {agent.status}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="projects" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Project Management</CardTitle>
                <CardDescription>Overview of all projects and tasks</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {projects.map((project) => (
                    <div key={project.id} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{project.name}</h4>
                        <Badge variant={getStatusColor(project.status)}>
                          {project.status}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">
                        {project.description}
                      </p>
                      <div className="flex items-center justify-between text-sm">
                        <span>Progress: {project.progress}%</span>
                        <span>Budget: ${project.budget.toLocaleString()}</span>
                      </div>
                      <Progress value={project.progress} className="mt-2" />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="agents" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>AI Agent Management</CardTitle>
                <CardDescription>Monitor and manage AI agents</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {agents.map((agent) => (
                    <div key={agent.id} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{agent.name}</h4>
                        <Badge variant={getStatusColor(agent.status)}>
                          {agent.status}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">
                        Type: {agent.type}
                      </p>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>Tasks Completed:</span>
                          <span>{agent.performance.tasksCompleted}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Success Rate:</span>
                          <span>{(agent.performance.successRate * 100).toFixed(1)}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Avg Response:</span>
                          <span>{agent.performance.averageResponseTime}ms</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="llm" className="space-y-6">
            {systemStatus && (
              <Card>
                <CardHeader>
                  <CardTitle>LLM System Status</CardTitle>
                  <CardDescription>Provider health and performance metrics</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-medium mb-4">Local LLM (Ollama)</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span>Status:</span>
                          <Badge variant={systemStatus.providers.ollama.available ? 'default' : 'destructive'}>
                            {systemStatus.providers.ollama.available ? 'Available' : 'Offline'}
                          </Badge>
                        </div>
                        <div className="flex justify-between">
                          <span>Models:</span>
                          <span>{systemStatus.providers.ollama.models.length}</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-4">Cloud LLM</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span>Status:</span>
                          <Badge variant="default">Available</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span>Models:</span>
                          <span>{systemStatus.providers.cloud.models.length}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {metrics && (
                    <div className="mt-6 pt-6 border-t">
                      <h4 className="font-medium mb-4">Performance Metrics</h4>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold">{metrics.totalRequests}</div>
                          <p className="text-sm text-muted-foreground">Total Requests</p>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold">
                            {(metrics.successRate * 100).toFixed(1)}%
                          </div>
                          <p className="text-sm text-muted-foreground">Success Rate</p>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold">{metrics.averageResponseTime}ms</div>
                          <p className="text-sm text-muted-foreground">Avg Response</p>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold">${metrics.costToday.toFixed(2)}</div>
                          <p className="text-sm text-muted-foreground">Cost Today</p>
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Business Analytics</CardTitle>
                <CardDescription>Comprehensive business insights and trends</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div className="text-center p-4 border rounded-lg">
                    <Users className="h-8 w-8 mx-auto mb-2 text-blue-500" />
                    <div className="text-2xl font-bold">{projects.length}</div>
                    <p className="text-sm text-muted-foreground">Active Projects</p>
                  </div>
                  
                  <div className="text-center p-4 border rounded-lg">
                    <Clock className="h-8 w-8 mx-auto mb-2 text-green-500" />
                    <div className="text-2xl font-bold">
                      {tasks.filter(t => t.status === 'completed').length}
                    </div>
                    <p className="text-sm text-muted-foreground">Completed Tasks</p>
                  </div>
                  
                  <div className="text-center p-4 border rounded-lg">
                    <DollarSign className="h-8 w-8 mx-auto mb-2 text-yellow-500" />
                    <div className="text-2xl font-bold">
                      ${projects.reduce((sum, p) => sum + p.budget, 0).toLocaleString()}
                    </div>
                    <p className="text-sm text-muted-foreground">Total Budget</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
