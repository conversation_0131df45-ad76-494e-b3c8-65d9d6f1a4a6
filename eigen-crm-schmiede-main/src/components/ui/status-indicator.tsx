
import React from 'react';
import { cn } from '@/lib/utils';

interface StatusIndicatorProps {
  status: 'good' | 'warning' | 'error' | 'inactive';
  size?: 'sm' | 'md' | 'lg';
  showPulse?: boolean;
}

export const StatusIndicator: React.FC<StatusIndicatorProps> = ({ 
  status, 
  size = 'md', 
  showPulse = false 
}) => {
  const sizeClasses = {
    sm: 'w-2 h-2',
    md: 'w-3 h-3', 
    lg: 'w-4 h-4'
  };

  const statusClasses = {
    good: 'bg-green-500',
    warning: 'bg-yellow-500',
    error: 'bg-red-500',
    inactive: 'bg-gray-400'
  };

  return (
    <div className={cn(
      'rounded-full',
      sizeClasses[size],
      statusClasses[status],
      showPulse && 'animate-pulse'
    )} />
  );
};
