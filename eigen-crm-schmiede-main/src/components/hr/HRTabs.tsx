
import React from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { EmployeeManagement } from './EmployeeManagement';
import { CertificationTracking } from './CertificationTracking';
import { TrainingScheduling } from './TrainingScheduling';
import { PerformanceAnalytics } from './PerformanceAnalytics';

interface HRTabsProps {
  activeTab: string;
  onTabChange: (value: string) => void;
}

export const HRTabs = ({ activeTab, onTabChange }: HRTabsProps) => {
  return (
    <Tabs value={activeTab} onValueChange={onTabChange} className="space-y-6">
      <TabsList className="bg-white/80 backdrop-blur-sm border border-white/20 shadow-lg p-1 rounded-xl">
        <TabsTrigger value="employees" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-emerald-600 data-[state=active]:to-teal-600 data-[state=active]:text-white rounded-lg">
          Employee Management
        </TabsTrigger>
        <TabsTrigger value="certifications" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-emerald-600 data-[state=active]:to-teal-600 data-[state=active]:text-white rounded-lg">
          Certifications
        </TabsTrigger>
        <TabsTrigger value="training" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-emerald-600 data-[state=active]:to-teal-600 data-[state=active]:text-white rounded-lg">
          Training & Development
        </TabsTrigger>
        <TabsTrigger value="performance" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-emerald-600 data-[state=active]:to-teal-600 data-[state=active]:text-white rounded-lg">
          Performance Analytics
        </TabsTrigger>
      </TabsList>

      <TabsContent value="employees" className="space-y-6">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl p-6">
          <EmployeeManagement />
        </div>
      </TabsContent>

      <TabsContent value="certifications" className="space-y-6">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl p-6">
          <CertificationTracking />
        </div>
      </TabsContent>

      <TabsContent value="training" className="space-y-6">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl p-6">
          <TrainingScheduling />
        </div>
      </TabsContent>

      <TabsContent value="performance" className="space-y-6">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl p-6">
          <PerformanceAnalytics />
        </div>
      </TabsContent>
    </Tabs>
  );
};
