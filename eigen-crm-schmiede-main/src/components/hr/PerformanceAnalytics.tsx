
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  LineChart,
  Line,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar
} from 'recharts';
import { 
  TrendingUp, 
  TrendingDown, 
  Target, 
  Award,
  Users,
  Clock,
  Star,
  AlertTriangle
} from 'lucide-react';

interface PerformanceMetric {
  id: string;
  employeeId: string;
  employeeName: string;
  department: string;
  position: string;
  overallScore: number;
  productivity: number;
  quality: number;
  safety: number;
  teamwork: number;
  attendance: number;
  reviewDate: string;
  goals: string[];
  achievements: string[];
  improvementAreas: string[];
}

const mockPerformanceData: PerformanceMetric[] = [
  {
    id: '1',
    employeeId: '1',
    employeeName: '<PERSON>',
    department: 'Construction',
    position: 'Site Supervisor',
    overallScore: 88,
    productivity: 92,
    quality: 85,
    safety: 95,
    teamwork: 80,
    attendance: 90,
    reviewDate: '2024-01-15',
    goals: ['Complete advanced safety training', 'Lead 2 major projects'],
    achievements: ['Zero safety incidents in Q4', 'Completed project ahead of schedule'],
    improvementAreas: ['Communication skills', 'Delegation']
  },
  {
    id: '2',
    employeeId: '2',
    employeeName: 'Maria Garcia',
    department: 'Heavy Machinery',
    position: 'Equipment Operator',
    overallScore: 92,
    productivity: 95,
    quality: 90,
    safety: 88,
    teamwork: 94,
    attendance: 96,
    reviewDate: '2024-01-20',
    goals: ['Obtain crane operator certification', 'Mentor junior operators'],
    achievements: ['Highest productivity in Q4', 'Perfect attendance record'],
    improvementAreas: ['Technical documentation', 'Equipment maintenance knowledge']
  },
  {
    id: '3',
    employeeId: '3',
    employeeName: 'David Johnson',
    department: 'Electrical',
    position: 'Electrician',
    overallScore: 85,
    productivity: 88,
    quality: 92,
    safety: 90,
    teamwork: 75,
    attendance: 82,
    reviewDate: '2024-01-25',
    goals: ['Complete master electrician training', 'Improve attendance'],
    achievements: ['Quality work recognition', 'Solved complex wiring issues'],
    improvementAreas: ['Punctuality', 'Team collaboration']
  }
];

const departmentPerformance = [
  { department: 'Construction', avgScore: 88, employees: 25 },
  { department: 'Heavy Machinery', avgScore: 92, employees: 15 },
  { department: 'Electrical', avgScore: 85, employees: 12 },
  { department: 'Plumbing', avgScore: 87, employees: 8 },
  { department: 'Safety', avgScore: 94, employees: 5 }
];

const performanceTrend = [
  { month: 'Oct', score: 85 },
  { month: 'Nov', score: 87 },
  { month: 'Dec', score: 89 },
  { month: 'Jan', score: 88 }
];

export const PerformanceAnalytics = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('quarterly');
  const [selectedDepartment, setSelectedDepartment] = useState('all');

  const filteredData = selectedDepartment === 'all' 
    ? mockPerformanceData 
    : mockPerformanceData.filter(p => p.department === selectedDepartment);

  const averageScore = filteredData.reduce((sum, p) => sum + p.overallScore, 0) / filteredData.length;
  const topPerformers = filteredData.filter(p => p.overallScore >= 90).length;
  const improvementNeeded = filteredData.filter(p => p.overallScore < 80).length;

  const getPerformanceLevel = (score: number) => {
    if (score >= 90) return { level: 'Excellent', color: 'bg-green-100 text-green-800' };
    if (score >= 80) return { level: 'Good', color: 'bg-blue-100 text-blue-800' };
    if (score >= 70) return { level: 'Satisfactory', color: 'bg-yellow-100 text-yellow-800' };
    return { level: 'Needs Improvement', color: 'bg-red-100 text-red-800' };
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Performance Analytics</h2>
          <p className="text-muted-foreground">Track and analyze employee performance metrics</p>
        </div>
        <div className="flex gap-2">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-[150px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="quarterly">Quarterly</SelectItem>
              <SelectItem value="yearly">Yearly</SelectItem>
            </SelectContent>
          </Select>
          <Select value={selectedDepartment} onValueChange={setSelectedDepartment}>
            <SelectTrigger className="w-[150px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Departments</SelectItem>
              <SelectItem value="Construction">Construction</SelectItem>
              <SelectItem value="Heavy Machinery">Heavy Machinery</SelectItem>
              <SelectItem value="Electrical">Electrical</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Average Score</p>
                <p className="text-3xl font-bold">{averageScore.toFixed(0)}</p>
              </div>
              <Target className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Top Performers</p>
                <p className="text-3xl font-bold text-green-600">{topPerformers}</p>
              </div>
              <Award className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Employees</p>
                <p className="text-3xl font-bold">{filteredData.length}</p>
              </div>
              <Users className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Need Improvement</p>
                <p className="text-3xl font-bold text-red-600">{improvementNeeded}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Department Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={departmentPerformance}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="department" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="avgScore" fill="#3b82f6" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Performance Trend</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={performanceTrend}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="score" stroke="#3b82f6" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Individual Performance */}
      <Card>
        <CardHeader>
          <CardTitle>Individual Performance Review</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {filteredData.map((employee) => {
              const performance = getPerformanceLevel(employee.overallScore);
              const radarData = [
                { subject: 'Productivity', score: employee.productivity },
                { subject: 'Quality', score: employee.quality },
                { subject: 'Safety', score: employee.safety },
                { subject: 'Teamwork', score: employee.teamwork },
                { subject: 'Attendance', score: employee.attendance }
              ];

              return (
                <div key={employee.id} className="border rounded-lg p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h3 className="text-xl font-semibold">{employee.employeeName}</h3>
                      <p className="text-muted-foreground">{employee.position} - {employee.department}</p>
                    </div>
                    <div className="text-right">
                      <Badge className={performance.color}>
                        {performance.level}
                      </Badge>
                      <p className="text-2xl font-bold mt-1">{employee.overallScore}</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-semibold mb-3">Performance Metrics</h4>
                      <div className="space-y-3">
                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span>Productivity</span>
                            <span>{employee.productivity}%</span>
                          </div>
                          <Progress value={employee.productivity} className="h-2" />
                        </div>
                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span>Quality</span>
                            <span>{employee.quality}%</span>
                          </div>
                          <Progress value={employee.quality} className="h-2" />
                        </div>
                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span>Safety</span>
                            <span>{employee.safety}%</span>
                          </div>
                          <Progress value={employee.safety} className="h-2" />
                        </div>
                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span>Teamwork</span>
                            <span>{employee.teamwork}%</span>
                          </div>
                          <Progress value={employee.teamwork} className="h-2" />
                        </div>
                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span>Attendance</span>
                            <span>{employee.attendance}%</span>
                          </div>
                          <Progress value={employee.attendance} className="h-2" />
                        </div>
                      </div>
                    </div>

                    <div>
                      <ResponsiveContainer width="100%" height={200}>
                        <RadarChart data={radarData}>
                          <PolarGrid />
                          <PolarAngleAxis dataKey="subject" />
                          <PolarRadiusAxis angle={90} domain={[0, 100]} />
                          <Radar
                            name="Score"
                            dataKey="score"
                            stroke="#3b82f6"
                            fill="#3b82f6"
                            fillOpacity={0.3}
                          />
                        </RadarChart>
                      </ResponsiveContainer>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                    <div>
                      <h5 className="font-medium mb-2">Goals</h5>
                      <ul className="text-sm space-y-1">
                        {employee.goals.map((goal, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <Target className="h-3 w-3 mt-1 text-blue-600" />
                            {goal}
                          </li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <h5 className="font-medium mb-2">Achievements</h5>
                      <ul className="text-sm space-y-1">
                        {employee.achievements.map((achievement, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <Star className="h-3 w-3 mt-1 text-green-600" />
                            {achievement}
                          </li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <h5 className="font-medium mb-2">Improvement Areas</h5>
                      <ul className="text-sm space-y-1">
                        {employee.improvementAreas.map((area, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <TrendingUp className="h-3 w-3 mt-1 text-orange-600" />
                            {area}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
