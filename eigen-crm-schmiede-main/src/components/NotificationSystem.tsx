
import React, { useEffect } from 'react';
import { useAIStore } from '@/stores/aiStore';
import { useToast } from '@/hooks/use-toast';
import { Bell, Brain, CheckCircle, AlertCircle } from 'lucide-react';

export const NotificationSystem: React.FC = () => {
  const { agents, conversations } = useAIStore();
  const { toast } = useToast();

  // Listen for agent status changes
  useEffect(() => {
    const interval = setInterval(() => {
      agents.forEach(agent => {
        if (agent.status === 'running' && agent.lastActivity) {
          const lastActivity = new Date(agent.lastActivity);
          const now = new Date();
          const minutesSinceActivity = (now.getTime() - lastActivity.getTime()) / (1000 * 60);
          
          // Show notification if agent has been running for more than 5 minutes
          if (minutesSinceActivity > 5) {
            toast({
              title: "Agent Update",
              description: `${agent.name} has been running for ${Math.round(minutesSinceActivity)} minutes`,
              action: (
                <div className="flex items-center gap-2">
                  <Brain className="w-4 h-4" />
                  <span>View Details</span>
                </div>
              ),
            });
          }
        }
      });
    }, 60000); // Check every minute

    return () => clearInterval(interval);
  }, [agents, toast]);

  // Listen for new conversations
  useEffect(() => {
    if (conversations.length > 0) {
      const latestConversation = conversations[conversations.length - 1];
      const conversationTime = new Date(latestConversation.timestamp);
      const now = new Date();
      const secondsSinceConversation = (now.getTime() - conversationTime.getTime()) / 1000;
      
      // Show notification for conversations less than 10 seconds old
      if (secondsSinceConversation < 10 && latestConversation.aiResponse) {
        toast({
          title: "AI Response Ready",
          description: "Your AI assistant has completed the analysis",
          action: (
            <div className="flex items-center gap-2">
              <CheckCircle className="w-4 h-4 text-green-600" />
              <span>View Response</span>
            </div>
          ),
        });
      }
    }
  }, [conversations, toast]);

  return null; // This component doesn't render anything visible
};
