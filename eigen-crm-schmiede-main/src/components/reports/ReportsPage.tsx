
import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Download, BarChart3, FileText, TrendingUp, Users } from 'lucide-react';
import { ReportingService, ReportData } from '@/services/ReportingService';
import { format } from 'date-fns';
import { DateRange } from 'react-day-picker';

export const ReportsPage = () => {
  const [selectedReportType, setSelectedReportType] = useState<string>('safety');
  const [dateRange, setDateRange] = useState<DateRange | undefined>();

  const { data: reportData, isLoading, refetch } = useQuery({
    queryKey: ['report', selectedReportType, dateRange],
    queryFn: async () => {
      const range = dateRange?.from && dateRange?.to ? {
        start: format(dateRange.from, 'yyyy-MM-dd'),
        end: format(dateRange.to, 'yyyy-MM-dd')
      } : undefined;

      switch (selectedReportType) {
        case 'safety':
          return ReportingService.generateSafetyReport(range);
        case 'quality':
          return ReportingService.generateQualityReport(range);
        case 'hr':
          return ReportingService.generateHRReport();
        case 'fleet':
          return ReportingService.generateFleetReport();
        default:
          return ReportingService.generateSafetyReport(range);
      }
    },
  });

  const handleExport = () => {
    if (!reportData) return;
    
    const dataStr = JSON.stringify(reportData, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const exportFileDefaultName = `${reportData.title}_${format(new Date(), 'yyyy-MM-dd')}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  const getReportIcon = (type: string) => {
    switch (type) {
      case 'safety': return <BarChart3 className="h-5 w-5" />;
      case 'quality': return <FileText className="h-5 w-5" />;
      case 'hr': return <Users className="h-5 w-5" />;
      case 'fleet': return <TrendingUp className="h-5 w-5" />;
      default: return <BarChart3 className="h-5 w-5" />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-indigo-50 to-purple-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-slate-900">Advanced Reports</h1>
            <p className="text-slate-600 mt-2">Generate comprehensive reports across all modules</p>
          </div>
          <Button onClick={handleExport} disabled={!reportData}>
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
        </div>

        {/* Controls */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-wrap gap-4 items-center">
              <div className="flex-1 min-w-[200px]">
                <label className="text-sm font-medium text-slate-700 mb-2 block">Report Type</label>
                <Select value={selectedReportType} onValueChange={setSelectedReportType}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="safety">Safety Report</SelectItem>
                    <SelectItem value="quality">Quality Report</SelectItem>
                    <SelectItem value="hr">HR Report</SelectItem>
                    <SelectItem value="fleet">Fleet Report</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex-1 min-w-[200px]">
                <label className="text-sm font-medium text-slate-700 mb-2 block">Date Range</label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-full justify-start text-left">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateRange?.from && dateRange?.to 
                        ? `${format(dateRange.from, 'MMM dd')} - ${format(dateRange.to, 'MMM dd')}` 
                        : 'Select dates'
                      }
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="range"
                      selected={dateRange}
                      onSelect={setDateRange}
                      numberOfMonths={2}
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="pt-6">
                <Button onClick={() => refetch()}>
                  Generate Report
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Report Display */}
        {isLoading ? (
          <Card>
            <CardContent className="p-12 text-center">
              <div className="animate-pulse">Generating report...</div>
            </CardContent>
          </Card>
        ) : reportData ? (
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {getReportIcon(reportData.type)}
                  <div>
                    <CardTitle>{reportData.title}</CardTitle>
                    <p className="text-sm text-slate-600">
                      Generated on {format(new Date(reportData.generatedAt), 'MMM dd, yyyy HH:mm')}
                    </p>
                  </div>
                </div>
                <Badge variant="secondary" className="capitalize">
                  {reportData.type}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Summary Stats */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <p className="text-sm font-medium text-blue-700">Total Records</p>
                    <p className="text-2xl font-bold text-blue-900">{reportData.data.length}</p>
                  </div>
                  <div className="bg-green-50 p-4 rounded-lg">
                    <p className="text-sm font-medium text-green-700">Generated By</p>
                    <p className="text-lg font-semibold text-green-900 capitalize">{reportData.generatedBy}</p>
                  </div>
                  <div className="bg-purple-50 p-4 rounded-lg">
                    <p className="text-sm font-medium text-purple-700">Report ID</p>
                    <p className="text-sm font-mono text-purple-900">{reportData.id}</p>
                  </div>
                  <div className="bg-orange-50 p-4 rounded-lg">
                    <p className="text-sm font-medium text-orange-700">Data Points</p>
                    <p className="text-2xl font-bold text-orange-900">
                      {Array.isArray(reportData.data) ? reportData.data.length : Object.keys(reportData.data).length}
                    </p>
                  </div>
                </div>

                {/* Data Preview */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">Data Preview</h3>
                  <div className="bg-slate-50 p-4 rounded-lg max-h-96 overflow-auto">
                    <pre className="text-sm text-slate-800">
                      {JSON.stringify(reportData.data.slice(0, 5), null, 2)}
                    </pre>
                    {reportData.data.length > 5 && (
                      <p className="text-sm text-slate-600 mt-2">
                        ... and {reportData.data.length - 5} more records
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ) : null}
      </div>
    </div>
  );
};
