import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, Ta<PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  TrendingUp, 
  TrendingDown, 
  Target, 
  Users, 
  DollarSign, 
  Clock,
  Brain,
  Zap,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { leadScoringService, LeadScore } from '@/services/ai/LeadScoringService';
import { enhancedProviderManager } from '@/services/ai/EnhancedProviderManager';

interface LeadScoringDashboardProps {
  className?: string;
}

interface LeadWithScore {
  id: string;
  name: string;
  company: string;
  email: string;
  score: LeadScore;
  lastUpdated: Date;
}

export function LeadScoringDashboard({ className }: LeadScoringDashboardProps) {
  const [leads, setLeads] = useState<LeadWithScore[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedLead, setSelectedLead] = useState<LeadWithScore | null>(null);
  const [providerStats, setProviderStats] = useState<any[]>([]);

  useEffect(() => {
    loadLeadScores();
    loadProviderStats();
  }, []);

  const loadLeadScores = async () => {
    try {
      setLoading(true);
      // This would fetch from your database
      // For now, we'll use mock data
      const mockLeads: LeadWithScore[] = [
        {
          id: '1',
          name: 'John Smith',
          company: 'TechCorp Inc',
          email: '<EMAIL>',
          score: {
            overallScore: 85,
            demographicScore: 90,
            behavioralScore: 80,
            engagementScore: 85,
            firmographicScore: 85,
            conversionProbability: 0.75,
            predictedValue: 50000,
            recommendedActions: ['Schedule immediate follow-up', 'Send pricing proposal'],
            aiInsights: {
              strengths: ['High engagement', 'Decision maker role'],
              concerns: ['Budget timeline unclear'],
              opportunities: ['Expansion potential']
            }
          },
          lastUpdated: new Date()
        },
        {
          id: '2',
          name: 'Sarah Johnson',
          company: 'StartupXYZ',
          email: '<EMAIL>',
          score: {
            overallScore: 65,
            demographicScore: 70,
            behavioralScore: 60,
            engagementScore: 65,
            firmographicScore: 65,
            conversionProbability: 0.45,
            predictedValue: 25000,
            recommendedActions: ['Nurture with educational content', 'Schedule demo'],
            aiInsights: {
              strengths: ['Growing company', 'Active engagement'],
              concerns: ['Limited budget', 'Early stage'],
              opportunities: ['Future growth potential']
            }
          },
          lastUpdated: new Date()
        }
      ];
      setLeads(mockLeads);
    } catch (error) {
      console.error('Failed to load lead scores:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadProviderStats = async () => {
    try {
      const stats = enhancedProviderManager.getProviderStats();
      setProviderStats(stats);
    } catch (error) {
      console.error('Failed to load provider stats:', error);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBadgeVariant = (score: number) => {
    if (score >= 80) return 'default';
    if (score >= 60) return 'secondary';
    return 'destructive';
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${Math.round(value * 100)}%`;
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Lead Scoring Dashboard</h2>
          <p className="text-muted-foreground">
            AI-powered lead scoring and predictive analytics
          </p>
        </div>
        <Button onClick={loadLeadScores} disabled={loading}>
          <Zap className="mr-2 h-4 w-4" />
          Refresh Scores
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Leads</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{leads.length}</div>
            <p className="text-xs text-muted-foreground">
              +12% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">High-Quality Leads</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {leads.filter(l => l.score.overallScore >= 80).length}
            </div>
            <p className="text-xs text-muted-foreground">
              Score ≥ 80
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Predicted Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(leads.reduce((sum, l) => sum + l.score.predictedValue, 0))}
            </div>
            <p className="text-xs text-muted-foreground">
              Total pipeline value
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Conversion Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatPercentage(
                leads.reduce((sum, l) => sum + l.score.conversionProbability, 0) / leads.length || 0
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              AI predicted
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="leads" className="space-y-4">
        <TabsList>
          <TabsTrigger value="leads">Lead Scores</TabsTrigger>
          <TabsTrigger value="insights">AI Insights</TabsTrigger>
          <TabsTrigger value="providers">AI Providers</TabsTrigger>
        </TabsList>

        <TabsContent value="leads" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Lead Scoring Results</CardTitle>
              <CardDescription>
                Leads ranked by AI-calculated scores and conversion probability
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {leads.map((lead) => (
                  <div
                    key={lead.id}
                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 cursor-pointer"
                    onClick={() => setSelectedLead(lead)}
                  >
                    <div className="flex items-center space-x-4">
                      <div>
                        <p className="font-medium">{lead.name}</p>
                        <p className="text-sm text-muted-foreground">{lead.company}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <Badge variant={getScoreBadgeVariant(lead.score.overallScore)}>
                          {lead.score.overallScore}/100
                        </Badge>
                        <p className="text-sm text-muted-foreground mt-1">
                          {formatPercentage(lead.score.conversionProbability)} conversion
                        </p>
                      </div>
                      
                      <div className="text-right">
                        <p className="font-medium">{formatCurrency(lead.score.predictedValue)}</p>
                        <p className="text-sm text-muted-foreground">predicted value</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="insights" className="space-y-4">
          {selectedLead && (
            <Card>
              <CardHeader>
                <CardTitle>AI Insights for {selectedLead.name}</CardTitle>
                <CardDescription>
                  Detailed analysis and recommendations
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Score Breakdown */}
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm">Demographic Score</span>
                      <span className="text-sm font-medium">{selectedLead.score.demographicScore}/100</span>
                    </div>
                    <Progress value={selectedLead.score.demographicScore} />
                  </div>
                  
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm">Behavioral Score</span>
                      <span className="text-sm font-medium">{selectedLead.score.behavioralScore}/100</span>
                    </div>
                    <Progress value={selectedLead.score.behavioralScore} />
                  </div>
                  
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm">Engagement Score</span>
                      <span className="text-sm font-medium">{selectedLead.score.engagementScore}/100</span>
                    </div>
                    <Progress value={selectedLead.score.engagementScore} />
                  </div>
                  
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm">Firmographic Score</span>
                      <span className="text-sm font-medium">{selectedLead.score.firmographicScore}/100</span>
                    </div>
                    <Progress value={selectedLead.score.firmographicScore} />
                  </div>
                </div>

                {/* AI Insights */}
                <div className="grid gap-4 md:grid-cols-3">
                  <div>
                    <h4 className="font-medium text-green-600 mb-2 flex items-center">
                      <CheckCircle className="mr-2 h-4 w-4" />
                      Strengths
                    </h4>
                    <ul className="text-sm space-y-1">
                      {selectedLead.score.aiInsights.strengths?.map((strength: string, index: number) => (
                        <li key={index} className="text-muted-foreground">• {strength}</li>
                      ))}
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-yellow-600 mb-2 flex items-center">
                      <AlertTriangle className="mr-2 h-4 w-4" />
                      Concerns
                    </h4>
                    <ul className="text-sm space-y-1">
                      {selectedLead.score.aiInsights.concerns?.map((concern: string, index: number) => (
                        <li key={index} className="text-muted-foreground">• {concern}</li>
                      ))}
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-blue-600 mb-2 flex items-center">
                      <TrendingUp className="mr-2 h-4 w-4" />
                      Opportunities
                    </h4>
                    <ul className="text-sm space-y-1">
                      {selectedLead.score.aiInsights.opportunities?.map((opportunity: string, index: number) => (
                        <li key={index} className="text-muted-foreground">• {opportunity}</li>
                      ))}
                    </ul>
                  </div>
                </div>

                {/* Recommended Actions */}
                <div>
                  <h4 className="font-medium mb-3">Recommended Actions</h4>
                  <div className="space-y-2">
                    {selectedLead.score.recommendedActions.map((action, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <Button variant="outline" size="sm">
                          {action}
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="providers" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>AI Provider Status</CardTitle>
              <CardDescription>
                Multi-provider AI system status and performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {providerStats.map((stat, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded">
                    <div className="flex items-center space-x-3">
                      <Brain className="h-5 w-5" />
                      <div>
                        <p className="font-medium">{stat.provider.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {stat.provider.type} • Privacy: {stat.provider.privacyScore}/10
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-4">
                      <Badge variant={stat.configured ? 'default' : 'secondary'}>
                        {stat.configured ? 'Configured' : 'Not Configured'}
                      </Badge>
                      
                      {stat.usage.requestsToday && (
                        <div className="text-right">
                          <p className="text-sm font-medium">{stat.usage.requestsToday}</p>
                          <p className="text-xs text-muted-foreground">requests today</p>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
