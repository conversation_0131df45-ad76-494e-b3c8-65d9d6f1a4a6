
import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Brain, 
  MessageSquare, 
  Mic, 
  CheckCircle, 
  XCircle, 
  Clock,
  PlayCircle,
  Zap
} from 'lucide-react'
import { useEnhancedAI } from '@/contexts/EnhancedAIContext'
import { useAIStore } from '@/stores/aiStore'
import { nlProcessor } from '@/services/NLProcessor'
import { agentManager } from '@/services/AgentManager'

interface TestResult {
  name: string
  status: 'pending' | 'success' | 'error'
  message: string
  timestamp?: string
  details?: string
}

export const AITestPanel: React.FC = () => {
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const { sendMessage, isInitialized } = useEnhancedAI()
  const { agents, conversations } = useAIStore()

  const updateTestResult = (name: string, status: 'success' | 'error', message: string, details?: string) => {
    setTestResults(prev => prev.map(test => 
      test.name === name 
        ? { ...test, status, message, details, timestamp: new Date().toLocaleTimeString() }
        : test
    ))
  }

  const runTests = async () => {
    setIsRunning(true)
    const tests = [
      'Enhanced AI Context',
      'Agent Management', 
      'Message Processing',
      'Voice Recognition',
      'NLP Processing',
      'Agent Communication'
    ]

    setTestResults(tests.map(name => ({ name, status: 'pending', message: 'Running...' })))

    // Test 1: Enhanced AI Context
    try {
      if (isInitialized) {
        updateTestResult('Enhanced AI Context', 'success', 'Enhanced AI context is initialized', 
          'Router and providers are ready')
      } else {
        updateTestResult('Enhanced AI Context', 'error', 'AI context not initialized',
          'Check EnhancedAIProvider setup')
      }
    } catch (error) {
      updateTestResult('Enhanced AI Context', 'error', `Error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }

    // Test 2: Agent Management
    try {
      const availableAgents = agentManager.getAllAgents()
      const runningAgents = agents.filter(a => a.status === 'running')
      
      if (availableAgents.length > 0) {
        updateTestResult('Agent Management', 'success', 
          `${availableAgents.length} agents available, ${runningAgents.length} running`,
          'Agent manager and store are synchronized')
      } else {
        updateTestResult('Agent Management', 'error', 'No agents found in manager')
      }
    } catch (error) {
      updateTestResult('Agent Management', 'error', `Error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }

    // Test 3: Message Processing
    try {
      await sendMessage('Test message for AI integration validation')
      updateTestResult('Message Processing', 'success', 'Message sent and processed successfully',
        'Enhanced AI provider handling messages correctly')
    } catch (error) {
      updateTestResult('Message Processing', 'error', `Error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }

    // Test 4: Voice Recognition
    try {
      const isVoiceSupported = nlProcessor.isVoiceSupported()
      if (isVoiceSupported) {
        updateTestResult('Voice Recognition', 'success', 'Voice recognition is supported',
          'Browser supports Web Speech API')
      } else {
        updateTestResult('Voice Recognition', 'error', 'Voice recognition not supported in this browser',
          'Consider using Chrome or Edge for voice features')
      }
    } catch (error) {
      updateTestResult('Voice Recognition', 'error', `Error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }

    // Test 5: NLP Processing
    try {
      const result = await nlProcessor.processMessage('show me leads')
      updateTestResult('NLP Processing', 'success', 
        `Intent: ${result.intent}, Confidence: ${Math.round(result.confidence * 100)}%`,
        `Entities: ${Object.keys(result.entities).join(', ')}`)
    } catch (error) {
      updateTestResult('NLP Processing', 'error', `Error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }

    // Test 6: Agent Communication
    try {
      const testAgent = agents.find(a => a.status === 'running')
      if (testAgent) {
        const capabilities = agentManager.getCapabilities(testAgent.id)
        updateTestResult('Agent Communication', 'success',
          `Communication with ${testAgent.name} successful`,
          `Capabilities: ${capabilities.length} available`)
      } else {
        updateTestResult('Agent Communication', 'error', 'No running agents to test communication',
          'Start an agent to test communication')
      }
    } catch (error) {
      updateTestResult('Agent Communication', 'error', `Error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }

    setIsRunning(false)
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'error': return <XCircle className="w-4 h-4 text-red-500" />
      default: return <Clock className="w-4 h-4 text-yellow-500 animate-spin" />
    }
  }

  const successCount = testResults.filter(t => t.status === 'success').length
  const totalTests = testResults.length

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-4">
        <Button 
          onClick={runTests} 
          disabled={isRunning}
          className="flex items-center gap-2"
        >
          <PlayCircle className="w-4 h-4" />
          {isRunning ? 'Running Tests...' : 'Run Integration Tests'}
        </Button>
        
        <div className="flex items-center gap-2">
          <Badge variant={isInitialized ? "default" : "secondary"}>
            {isInitialized ? 'AI Ready' : 'AI Not Ready'}
          </Badge>
          <Badge variant="outline">
            {agents.length} Agents
          </Badge>
          <Badge variant="outline">
            {conversations.length} Conversations
          </Badge>
        </div>
      </div>

      {testResults.length > 0 && (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-sm">Test Results:</h4>
            {totalTests > 0 && (
              <Badge variant={successCount === totalTests ? "default" : "destructive"}>
                {successCount}/{totalTests} Passed
              </Badge>
            )}
          </div>
          
          {testResults.map((test, index) => (
            <div key={index} className="p-3 rounded-lg border space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {getStatusIcon(test.status)}
                  <div>
                    <p className="font-medium text-sm">{test.name}</p>
                    <p className="text-xs text-muted-foreground">{test.message}</p>
                  </div>
                </div>
                {test.timestamp && (
                  <Badge variant="outline" className="text-xs">
                    {test.timestamp}
                  </Badge>
                )}
              </div>
              {test.details && (
                <div className="text-xs text-muted-foreground bg-muted/30 p-2 rounded">
                  {test.details}
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      <Alert>
        <Zap className="h-4 w-4" />
        <AlertDescription>
          This panel validates AI system integration including enhanced context, agent management, 
          message processing, voice capabilities, and NLP functionality. Run tests to ensure 
          all components are working correctly.
        </AlertDescription>
      </Alert>
    </div>
  )
}
