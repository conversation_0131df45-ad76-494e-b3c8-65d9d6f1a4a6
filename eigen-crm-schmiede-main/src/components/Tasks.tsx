
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { TasksHeader } from './tasks/TasksHeader';
import { TasksStatsCards } from './tasks/TasksStatsCards';
import { TaskBoard } from './tasks/TaskBoard';
import { TaskList } from './tasks/TaskList';
import { TaskCalendar } from './tasks/TaskCalendar';
import { TeamWorkload } from './tasks/TeamWorkload';

export const Tasks = () => {
  const [activeView, setActiveView] = useState('board');

  return (
    <div className="p-6 space-y-6">
      <TasksHeader />
      <TasksStatsCards />
      
      <Tabs value={activeView} onValueChange={setActiveView} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="board">Ka<PERSON>ban Board</TabsTrigger>
          <TabsTrigger value="list">Task List</TabsTrigger>
          <TabsTrigger value="calendar">Calendar View</TabsTrigger>
          <TabsTrigger value="workload">Team Workload</TabsTrigger>
        </TabsList>

        <TabsContent value="board">
          <TaskBoard />
        </TabsContent>

        <TabsContent value="list">
          <TaskList />
        </TabsContent>

        <TabsContent value="calendar">
          <TaskCalendar />
        </TabsContent>

        <TabsContent value="workload">
          <TeamWorkload />
        </TabsContent>
      </Tabs>
    </div>
  );
};
