
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { DatabaseService } from '@/services/DatabaseService';
import { useToast } from '@/hooks/use-toast';

// User Profile Hook
export const useCurrentUser = () => {
  return useQuery({
    queryKey: ['current-user'],
    queryFn: () => DatabaseService.getCurrentUser(),
  });
};

// Companies Hooks
export const useCompanies = () => {
  return useQuery({
    queryKey: ['companies'],
    queryFn: () => DatabaseService.getCompanies(),
  });
};

export const useCreateCompany = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (data: any) => DatabaseService.createCompany(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['companies'] });
      toast({
        title: "Success",
        description: "Company created successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to create company.",
        variant: "destructive",
      });
      console.error('Error creating company:', error);
    },
  });
};

export const useUpdateCompany = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: any }) => 
      DatabaseService.updateCompany(id, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['companies'] });
      toast({
        title: "Success",
        description: "Company updated successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to update company.",
        variant: "destructive",
      });
      console.error('Error updating company:', error);
    },
  });
};

export const useDeleteCompany = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (id: string) => DatabaseService.deleteCompany(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['companies'] });
      toast({
        title: "Success",
        description: "Company deleted successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to delete company.",
        variant: "destructive",
      });
      console.error('Error deleting company:', error);
    },
  });
};

// Contacts Hooks
export const useContacts = () => {
  return useQuery({
    queryKey: ['contacts'],
    queryFn: () => DatabaseService.getContacts(),
  });
};

export const useCreateContact = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (data: any) => DatabaseService.createContact(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['contacts'] });
      toast({
        title: "Success",
        description: "Contact created successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to create contact.",
        variant: "destructive",
      });
      console.error('Error creating contact:', error);
    },
  });
};

export const useUpdateContact = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: any }) => 
      DatabaseService.updateContact(id, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['contacts'] });
      toast({
        title: "Success",
        description: "Contact updated successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to update contact.",
        variant: "destructive",
      });
      console.error('Error updating contact:', error);
    },
  });
};

export const useDeleteContact = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (id: string) => DatabaseService.deleteContact(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['contacts'] });
      toast({
        title: "Success",
        description: "Contact deleted successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to delete contact.",
        variant: "destructive",
      });
      console.error('Error deleting contact:', error);
    },
  });
};

// Opportunities Hooks
export const useOpportunities = () => {
  return useQuery({
    queryKey: ['opportunities'],
    queryFn: () => DatabaseService.getOpportunities(),
  });
};

export const useCreateOpportunity = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (data: any) => DatabaseService.createOpportunity(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['opportunities'] });
      toast({
        title: "Success",
        description: "Opportunity created successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to create opportunity.",
        variant: "destructive",
      });
      console.error('Error creating opportunity:', error);
    },
  });
};

export const useUpdateOpportunity = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: any }) => 
      DatabaseService.updateOpportunity(id, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['opportunities'] });
      toast({
        title: "Success",
        description: "Opportunity updated successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to update opportunity.",
        variant: "destructive",
      });
      console.error('Error updating opportunity:', error);
    },
  });
};

export const useDeleteOpportunity = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (id: string) => DatabaseService.deleteOpportunity(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['opportunities'] });
      toast({
        title: "Success",
        description: "Opportunity deleted successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to delete opportunity.",
        variant: "destructive",
      });
      console.error('Error deleting opportunity:', error);
    },
  });
};

// Conversations Hooks
export const useConversations = () => {
  return useQuery({
    queryKey: ['conversations'],
    queryFn: () => DatabaseService.getConversations(),
  });
};

export const useSaveConversation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: any) => DatabaseService.saveConversation(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['conversations'] });
    },
  });
};

// AI Agents Hooks
export const useAIAgents = () => {
  return useQuery({
    queryKey: ['ai-agents'],
    queryFn: () => DatabaseService.getAIAgents(),
  });
};

export const useUpdateAgentStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, status }: { id: string; status: string }) => 
      DatabaseService.updateAgentStatus(id, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['ai-agents'] });
    },
  });
};
