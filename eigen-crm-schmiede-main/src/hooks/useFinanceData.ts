
// Re-export all finance hooks for backwards compatibility
export { useInvoices, useCreateInvoice, useUpdateInvoiceStatus } from './useInvoices';
export { useExpenses } from './useExpenses';
export { useFinanceMetrics } from './useFinanceMetrics';
export { useRealtimeFinance } from '@/services/RealtimeFinanceService';

// Re-export types
export type { Invoice, Expense, FinanceMetrics } from '@/types/finance';
