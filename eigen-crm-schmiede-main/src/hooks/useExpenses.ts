
import { useQuery } from '@tanstack/react-query';
import { errorHandler } from '@/services/ErrorHandler';
import { useLoadingStore } from '@/stores/loadingStore';
import { financeService } from '@/services/FinanceService';

export const useExpenses = () => {
  const { setComponentLoading } = useLoadingStore();
  
  return useQuery({
    queryKey: ['expenses'],
    queryFn: async () => {
      setComponentLoading('expenses', true, 'Loading expenses...');
      try {
        const data = await financeService.getExpenses();
        return data;
      } catch (error) {
        errorHandler.handleError(error as Error, {
          component: 'Finance',
          action: 'loadExpenses',
          severity: 'medium',
          category: 'api'
        });
        throw error;
      } finally {
        setComponentLoading('expenses', false);
      }
    },
    staleTime: 5 * 60 * 1000,
    retry: 2
  });
};
