
import { APIKeys, DatabaseConfig, NotificationConfig, RAGConfig, LLMConfig, AutonomyConfig } from '@/types/settings';
import { initialState } from './initialState';

export const createSettingsActions = (set: any, get: any) => ({
  updateApiKeys: (keys: Partial<APIKeys>) => {
    set((state: any) => ({
      apiKeys: { ...state.apiKeys, ...keys },
      lastSync: new Date()
    }));
    
    // Also update localStorage for individual API keys (backward compatibility)
    Object.entries(keys).forEach(([provider, key]) => {
      if (key) {
        localStorage.setItem(`${provider}_api_key`, key);
      }
    });
  },
  
  updateDatabase: (config: Partial<DatabaseConfig>) => {
    set((state: any) => ({
      database: { ...state.database, ...config },
      lastSync: new Date()
    }));
  },
  
  updateNotifications: (config: Partial<NotificationConfig>) => {
    set((state: any) => ({
      notifications: { ...state.notifications, ...config },
      lastSync: new Date()
    }));
  },
  
  updateRAG: (config: Partial<RAGConfig>) => {
    set((state: any) => ({
      rag: { ...state.rag, ...config },
      lastSync: new Date()
    }));
  },
  
  updateLLM: (config: Partial<LLMConfig>) => {
    set((state: any) => ({
      llm: { ...state.llm, ...config },
      lastSync: new Date()
    }));
  },
  
  updateAutonomy: (config: Partial<AutonomyConfig>) => {
    set((state: any) => ({
      autonomy: { ...state.autonomy, ...config },
      lastSync: new Date()
    }));
  },
  
  syncSettings: async () => {
    // Future: implement Supabase sync
    console.log('Settings synced at:', new Date());
    set({ lastSync: new Date() });
  },
  
  resetSettings: () => {
    set({
      ...initialState,
      lastSync: new Date()
    });
    
    // Clear localStorage
    Object.keys(localStorage).forEach(key => {
      if (key.includes('_api_key') || key.includes('_config')) {
        localStorage.removeItem(key);
      }
    });
  },
  
  exportSettings: () => {
    const state = get();
    return JSON.stringify({
      apiKeys: state.apiKeys,
      database: state.database,
      notifications: state.notifications,
      rag: state.rag,
      llm: state.llm,
      autonomy: state.autonomy,
      exportedAt: new Date().toISOString()
    }, null, 2);
  },
  
  importSettings: (settingsJson: string) => {
    try {
      const settings = JSON.parse(settingsJson);
      set({
        apiKeys: settings.apiKeys || initialState.apiKeys,
        database: settings.database || initialState.database,
        notifications: settings.notifications || initialState.notifications,
        rag: settings.rag || initialState.rag,
        llm: settings.llm || initialState.llm,
        autonomy: settings.autonomy || initialState.autonomy,
        lastSync: new Date()
      });
    } catch (error) {
      console.error('Failed to import settings:', error);
      throw new Error('Invalid settings format');
    }
  }
});
