
import { SettingsState } from '@/types/settings';

export const initialState: Omit<SettingsState, 'lastSync'> = {
  apiKeys: {},
  database: {
    host: 'localhost',
    port: 5432,
    database: 'crm_db',
    ssl: true,
    poolSize: 10
  },
  notifications: {
    email: true,
    push: false,
    slack: false,
    webhooks: [],
    frequency: 'immediate' as const
  },
  rag: {
    chunkSize: 1000,
    overlap: 200,
    indexingEnabled: false,
    vectorDimensions: 1536,
    embeddingModel: 'text-embedding-ada-002'
  },
  llm: {
    primaryProvider: 'openai',
    fallbackProvider: 'anthropic',
    temperature: 0.7,
    maxTokens: 2000,
    timeout: 30000
  },
  autonomy: {
    level: 'manual' as const,
    approvalRequired: true,
    autoExecute: false,
    riskThreshold: 0.8
  }
};
