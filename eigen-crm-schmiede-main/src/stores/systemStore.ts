
import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'

export interface DynamicView {
  id: string
  title: string
  component: string
  data: any
  timestamp: string
}

interface SystemState {
  dynamicViews: DynamicView[]
  isConnected: boolean
  lastUpdate: string | null
  
  // Actions
  addDynamicView: (view: DynamicView) => void
  removeDynamicView: (id: string) => void
  setConnectionStatus: (connected: boolean) => void
  updateLastUpdate: () => void
}

export const useSystemStore = create<SystemState>()(
  subscribeWithSelector((set) => ({
    dynamicViews: [],
    isConnected: false,
    lastUpdate: null,
    
    addDynamicView: (view) => set((state) => ({
      dynamicViews: [...state.dynamicViews, view]
    })),
    
    removeDynamicView: (id) => set((state) => ({
      dynamicViews: state.dynamicViews.filter(view => view.id !== id)
    })),
    
    setConnectionStatus: (connected) => set({ isConnected: connected }),
    updateLastUpdate: () => set({ lastUpdate: new Date().toISOString() })
  }))
)
