
import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'

export interface ThoughtProcess {
  id: string
  agentId: string
  timestamp: string
  type: 'analysis' | 'decision' | 'action' | 'memory' | 'planning'
  content: string
  confidence: number
  context: Record<string, any>
  reasoning: string[]
  alternatives: string[]
  outcome?: string
}

interface ThoughtState {
  thoughtProcesses: ThoughtProcess[]
  
  // Actions
  addThoughtProcess: (thought: ThoughtProcess) => void
  getAgentThoughts: (agentId: string) => ThoughtProcess[]
}

export const useThoughtStore = create<ThoughtState>()(
  subscribeWithSelector((set, get) => ({
    thoughtProcesses: [],
    
    addThoughtProcess: (thought) => set((state) => ({
      thoughtProcesses: [...state.thoughtProcesses, thought]
    })),
    
    getAgentThoughts: (agentId) => {
      const state = get()
      return state.thoughtProcesses.filter(thought => thought.agentId === agentId)
    }
  }))
)
