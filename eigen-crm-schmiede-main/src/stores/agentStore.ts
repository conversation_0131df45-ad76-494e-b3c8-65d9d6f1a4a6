
import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import type { AutonomyLevel } from '@/types/autonomy'
import { AgentInitializer } from '@/services/agent/AgentInitializer'

export interface Agent {
  id: string
  name: string
  type: 'research' | 'lead_qualifier' | 'phone' | 'automation'
  status: 'idle' | 'running' | 'error' | 'awaiting_approval'
  lastActivity: string | null
  configuration: Record<string, any>
  autonomyLevel?: AutonomyLevel
  pendingApprovals: string[] // approval request IDs
}

interface AgentState {
  agents: Agent[]
  activeAgents: string[]
  
  // Actions
  addAgent: (agent: Agent) => void
  updateAgent: (id: string, updates: Partial<Agent>) => void
  startAgent: (id: string) => void
  stopAgent: (id: string) => void
}

export const useAgentStore = create<AgentState>()(
  subscribeWithSelector((set) => ({
    agents: AgentInitializer.createDefaultAgents(),
    activeAgents: [],
    
    addAgent: (agent) => set((state) => ({
      agents: [...state.agents, agent]
    })),
    
    updateAgent: (id, updates) => set((state) => ({
      agents: state.agents.map(agent => 
        agent.id === id ? { ...agent, ...updates } : agent
      )
    })),
    
    startAgent: (id) => set((state) => ({
      activeAgents: [...state.activeAgents, id],
      agents: state.agents.map(agent =>
        agent.id === id ? { ...agent, status: 'running' as const } : agent
      )
    })),
    
    stopAgent: (id) => set((state) => ({
      activeAgents: state.activeAgents.filter(agentId => agentId !== id),
      agents: state.agents.map(agent =>
        agent.id === id ? { ...agent, status: 'idle' as const } : agent
      )
    }))
  }))
)
