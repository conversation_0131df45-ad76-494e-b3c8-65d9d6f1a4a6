
import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'

interface CoreState {
  isConnected: boolean
  lastUpdate: Date | null
  setConnectionStatus: (connected: boolean) => void
  updateLastUpdate: () => void
}

export const useCoreStore = create<CoreState>()(
  subscribeWithSelector((set) => ({
    isConnected: true,
    lastUpdate: null,
    
    setConnectionStatus: (connected) => set({ isConnected: connected }),
    updateLastUpdate: () => set({ lastUpdate: new Date() })
  }))
)
