
import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import type { AutonomyLevel, ApprovalRequest, AutonomyConfiguration } from '@/types/autonomy'

interface AutonomyState {
  approvalRequests: ApprovalRequest[]
  autonomyConfig: AutonomyConfiguration
  
  // Actions
  addApprovalRequest: (request: ApprovalRequest) => void
  updateApprovalRequest: (id: string, updates: Partial<ApprovalRequest>) => void
  removeApprovalRequest: (id: string) => void
  updateAutonomyConfig: (config: Partial<AutonomyConfiguration>) => void
  setAgentAutonomyLevel: (agentId: string, level: AutonomyLevel) => void
}

export const useAutonomyStore = create<AutonomyState>()(
  subscribeWithSelector((set, get) => ({
    approvalRequests: [],
    autonomyConfig: {
      globalAutonomyLevel: 'supervised',
      agentSpecificLevels: {},
      approvalTimeout: 30,
      emergencyStopEnabled: true,
      batchApprovalEnabled: true,
      autoApprovalPatterns: []
    },
    
    addApprovalRequest: (request) => set((state) => ({
      approvalRequests: [...state.approvalRequests, request]
    })),
    
    updateApprovalRequest: (id, updates) => set((state) => ({
      approvalRequests: state.approvalRequests.map(request =>
        request.id === id ? { ...request, ...updates } : request
      )
    })),
    
    removeApprovalRequest: (id) => set((state) => ({
      approvalRequests: state.approvalRequests.filter(r => r.id !== id)
    })),
    
    updateAutonomyConfig: (config) => set((state) => ({
      autonomyConfig: { ...state.autonomyConfig, ...config }
    })),
    
    setAgentAutonomyLevel: (agentId, level) => set((state) => ({
      autonomyConfig: {
        ...state.autonomyConfig,
        agentSpecificLevels: {
          ...state.autonomyConfig.agentSpecificLevels,
          [agentId]: level
        }
      }
    }))
  }))
)
