
import { Project } from '@/types/project';

export const mockProjects: Project[] = [
  {
    id: '1',
    name: 'Downtown Office Complex',
    description: 'Modern 12-story office building with retail space on ground floor',
    status: 'active',
    priority: 'high',
    startDate: '2024-01-15',
    endDate: '2024-12-31',
    estimatedBudget: 2500000,
    actualBudget: 2100000,
    progress: 65,
    clientId: '1',
    clientName: 'Metro Development Corp',
    projectManager: '<PERSON>',
    team: [
      {
        id: '1',
        name: '<PERSON>',
        role: 'Project Manager',
        email: '<EMAIL>'
      },
      {
        id: '2',
        name: '<PERSON>',
        role: 'Senior Engineer',
        email: '<EMAIL>'
      }
    ],
    location: 'Seattle, WA',
    category: 'commercial',
    milestones: [
      {
        id: '1',
        title: 'Foundation Complete',
        description: 'Foundation and basement construction',
        dueDate: '2024-03-15',
        completed: true,
        completedDate: '2024-03-10'
      },
      {
        id: '2',
        title: 'Structural Framework',
        description: 'Steel framework and floor construction',
        dueDate: '2024-06-30',
        completed: true,
        completedDate: '2024-06-25'
      },
      {
        id: '3',
        title: 'Interior Build-out',
        description: 'Interior construction and utilities',
        dueDate: '2024-10-15',
        completed: false
      }
    ],
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-20T00:00:00Z'
  },
  {
    id: '2',
    name: 'Residential Development',
    description: '45-unit residential complex with amenities',
    status: 'planning',
    priority: 'medium',
    startDate: '2024-03-01',
    endDate: '2025-02-28',
    estimatedBudget: 1800000,
    actualBudget: 0,
    progress: 15,
    clientId: '2',
    clientName: 'Harbor Homes LLC',
    projectManager: 'Sarah Johnson',
    team: [
      {
        id: '3',
        name: 'Sarah Johnson',
        role: 'Project Manager',
        email: '<EMAIL>'
      }
    ],
    location: 'Portland, OR',
    category: 'residential',
    milestones: [
      {
        id: '4',
        title: 'Permits & Approvals',
        description: 'Obtain all necessary building permits',
        dueDate: '2024-02-28',
        completed: true,
        completedDate: '2024-02-25'
      },
      {
        id: '5',
        title: 'Site Preparation',
        description: 'Site clearing and preparation',
        dueDate: '2024-04-15',
        completed: false
      }
    ],
    createdAt: '2024-01-10T00:00:00Z',
    updatedAt: '2024-01-25T00:00:00Z'
  }
];
