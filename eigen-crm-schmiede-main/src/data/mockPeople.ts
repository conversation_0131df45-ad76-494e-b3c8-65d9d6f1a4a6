
import { Person } from '@/types/person';

export type { Person };

export const mockPeople: Person[] = [
  {
    id: '1',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    position: 'Project Manager',
    company: 'Metro Development Corp',
    companyId: '1',
    avatar: 'SJ',
    type: 'client',
    status: 'active',
    tags: ['VIP', 'Decision Maker'],
    notes: 'Key contact for downtown projects',
    address: {
      street: '123 Business Ave',
      city: 'Seattle',
      state: 'WA',
      zipCode: '98101',
      country: 'USA'
    },
    socialMedia: {
      linkedin: 'https://linkedin.com/in/sarah<PERSON><PERSON>son',
      website: 'https://metrodev.com'
    },
    lastContact: '2024-01-15',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z'
  },
  {
    id: '2',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    position: 'Site Supervisor',
    company: 'ConstructCo',
    companyId: '2',
    avatar: 'MW',
    type: 'contractor',
    status: 'active',
    tags: ['Experienced', 'Reliable'],
    notes: 'Excellent track record on residential projects',
    address: {
      city: 'Portland',
      state: 'OR',
      country: 'USA'
    },
    lastContact: '2024-01-12',
    createdAt: '2024-01-05T00:00:00Z',
    updatedAt: '2024-01-12T00:00:00Z'
  },
  {
    id: '3',
    firstName: 'Elena',
    lastName: 'Rodriguez',
    email: '<EMAIL>',
    phone: '+****************',
    position: 'Senior Engineer',
    company: 'Internal',
    companyId: '3',
    avatar: 'ER',
    type: 'employee',
    status: 'active',
    tags: ['Engineering', 'Lead'],
    notes: 'Specializes in structural engineering',
    lastContact: '2024-01-10',
    createdAt: '2023-12-01T00:00:00Z',
    updatedAt: '2024-01-10T00:00:00Z'
  }
];
